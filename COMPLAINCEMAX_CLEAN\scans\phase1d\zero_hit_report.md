# Zero Hit Report - Phase 1D

## Suspicious Files with No Nuggets

### backend\logging_config.py

**Reason:** domain_relevant_path  
**Word Count:** 770  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### backend\models.py

**Reason:** domain_relevant_path  
**Word Count:** 229  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### backend\tests\test_authentication_and_users.py

**Reason:** domain_relevant_path  
**Word Count:** 528  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### compare_checklists.py

**Reason:** domain_relevant_path  
**Word Count:** 143  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### copy_policy_jsons.bat

**Reason:** domain_relevant_path  
**Word Count:** 91  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\edit_tool_config.json

**Reason:** domain_relevant_path  
**Word Count:** 6  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\enhanced-edit-tool-config.json

**Reason:** domain_relevant_path  
**Word Count:** 38  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\inheritance.schema.json

**Reason:** domain_relevant_path  
**Word Count:** 47  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\intake_schema.json

**Reason:** domain_relevant_path  
**Word Count:** 8  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\jsconfig.json

**Reason:** domain_relevant_path  
**Word Count:** 12  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\prod-mc-auth.json

**Reason:** domain_relevant_path  
**Word Count:** 24  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\selectors.schema.json

**Reason:** domain_relevant_path  
**Word Count:** 55  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\syntaxes.schema.json

**Reason:** domain_relevant_path  
**Word Count:** 23  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig - Copy.json

**Reason:** domain_relevant_path  
**Word Count:** 541  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig-named - Copy.json

**Reason:** domain_relevant_path  
**Word Count:** 17  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig-named.json

**Reason:** domain_relevant_path  
**Word Count:** 17  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.app.json

**Reason:** domain_relevant_path  
**Word Count:** 52  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.base.json

**Reason:** domain_relevant_path  
**Word Count:** 46  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.browser.build.json

**Reason:** domain_relevant_path  
**Word Count:** 9  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.browser.json

**Reason:** domain_relevant_path  
**Word Count:** 28  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.cjs.json

**Reason:** domain_relevant_path  
**Word Count:** 17  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.cjs.spec.json

**Reason:** domain_relevant_path  
**Word Count:** 38  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.core.build.json

**Reason:** domain_relevant_path  
**Word Count:** 9  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.esm.json

**Reason:** domain_relevant_path  
**Word Count:** 15  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.esm5.json

**Reason:** domain_relevant_path  
**Word Count:** 19  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.esm5.rollup.json

**Reason:** domain_relevant_path  
**Word Count:** 13  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.json

**Reason:** domain_relevant_path  
**Word Count:** 116  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.lib.json

**Reason:** domain_relevant_path  
**Word Count:** 20  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.node.build.json

**Reason:** domain_relevant_path  
**Word Count:** 9  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.node.json

**Reason:** domain_relevant_path  
**Word Count:** 46  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.spec.json

**Reason:** domain_relevant_path  
**Word Count:** 21  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.src.json

**Reason:** domain_relevant_path  
**Word Count:** 34  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.types.json

**Reason:** domain_relevant_path  
**Word Count:** 25  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.types.spec.json

**Reason:** domain_relevant_path  
**Word Count:** 11  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\tsconfig.worker.json

**Reason:** domain_relevant_path  
**Word Count:** 23  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\types.schema.json

**Reason:** domain_relevant_path  
**Word Count:** 49  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### data\json\archive\units.schema.json

**Reason:** domain_relevant_path  
**Word Count:** 42  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### docs\AUTHORS.md

**Reason:** domain_relevant_path  
**Word Count:** 557  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### docs\AUTHORS.txt

**Reason:** domain_relevant_path  
**Word Count:** 8  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### docs\config.txt

**Reason:** domain_relevant_path  
**Word Count:** 164  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### docs\configuration.md

**Reason:** domain_relevant_path  
**Word Count:** 54  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### docs\policy\09 Documenting Disaster Damage and Developing Project Files.json

**Reason:** domain_relevant_path  
**Word Count:** 206  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### docs\policy\README.md

**Reason:** domain_relevant_path  
**Word Count:** 4  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### docs\policy\SCREENSHOT ABACUS LANDING PAGE.json

**Reason:** domain_relevant_path  
**Word Count:** 33  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### docs\policy\fema_contaminated-debris-mou_9-7-2010.json

**Reason:** domain_relevant_path  
**Word Count:** 48  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### docs\prod-mc-auth_timeline.txt

**Reason:** domain_relevant_path  
**Word Count:** 12  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### eslint.config.js

**Reason:** domain_relevant_path  
**Word Count:** 49  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\__tests__\EmergencyIntakeSuperWizard.test.js

**Reason:** domain_relevant_path  
**Word Count:** 1377  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\__tests__\EmergencyIntakeSuperWizard.test.tsx

**Reason:** domain_relevant_path  
**Word Count:** 763  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\__tests__\ProfessionalIntakeSuperWizard.test.js

**Reason:** domain_relevant_path  
**Word Count:** 1304  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\__tests__\ProfessionalIntakeSuperWizard.test.tsx

**Reason:** domain_relevant_path  
**Word Count:** 766  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\jest.config.js

**Reason:** domain_relevant_path  
**Word Count:** 79  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\next.config.js

**Reason:** domain_relevant_path  
**Word Count:** 729  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\next.config.ts

**Reason:** domain_relevant_path  
**Word Count:** 238  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\postcss.config.js

**Reason:** domain_relevant_path  
**Word Count:** 11  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\src\app\api\auth\login\route.js

**Reason:** domain_relevant_path  
**Word Count:** 531  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\src\app\api\auth\login\route.ts

**Reason:** domain_relevant_path  
**Word Count:** 82  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\src\components\ProfessionalIntakeWizard_old.js

**Reason:** domain_relevant_path  
**Word Count:** 1000  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\src\components\ProfessionalIntakeWizard_old.tsx

**Reason:** domain_relevant_path  
**Word Count:** 453  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\src\components\compliance\steps\ReviewSummaryStep.js

**Reason:** policy_document  
**Word Count:** 786  
**Policy Keywords:** 3  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### frontend\tailwind.config.js

**Reason:** domain_relevant_path  
**Word Count:** 29  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### public\policy\09 Documenting Disaster Damage and Developing Project Files.json

**Reason:** domain_relevant_path  
**Word Count:** 206  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### public\policy\fema_contaminated-debris-mou_9-7-2010.json

**Reason:** domain_relevant_path  
**Word Count:** 48  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### split_checklist_by_phase.py

**Reason:** domain_relevant_path  
**Word Count:** 123  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### src\components\wizard.perf.test.tsx

**Reason:** domain_relevant_path  
**Word Count:** 91  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### test_api_auth.py

**Reason:** domain_relevant_path  
**Word Count:** 225  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### tsconfig.app.json

**Reason:** domain_relevant_path  
**Word Count:** 52  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### tsconfig.json

**Reason:** domain_relevant_path  
**Word Count:** 14  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### tsconfig.node.json

**Reason:** domain_relevant_path  
**Word Count:** 46  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### vite.config.js

**Reason:** domain_relevant_path  
**Word Count:** 30  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

### vite.config.ts

**Reason:** domain_relevant_path  
**Word Count:** 32  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain domain logic in non-standard format or require specialized detectors

