#!/usr/bin/env python3
"""
Quick runner for the ALL NEW APP salvage operation
"""

import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from salvage_automation import SalvageAutomation
    
    print("🔧 ALL NEW APP Salvage Automation")
    print("=" * 50)
    
    # Verify source directory exists
    source_dir = Path("C:/Users/<USER>/Documents/repo analysis 202508/ALL NEW APP")
    if not source_dir.exists():
        print(f"❌ Source directory not found: {source_dir}")
        print("Please verify the path and try again.")
        sys.exit(1)
    
    # Run the salvage operation
    salvage = SalvageAutomation()
    salvage.run()
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure salvage_automation.py is in the same directory.")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error during salvage operation: {e}")
    sys.exit(1)

