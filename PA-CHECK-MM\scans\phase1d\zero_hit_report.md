# Zero Hit Report

Files with 0 nuggets but domain relevance:

- **frontend/node_modules/axios/lib/core/mergeConfig.js** (106 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/call-bind-apply-helpers/tsconfig.json** (9 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/axios/lib/helpers/resolveConfig.js** (57 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/big-integer/tsconfig.json** (24 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/internals/models/common.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/internals/models/fields.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/internals/models/props/toolbar.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/internals/models/props/tabs.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/internals/models/index.js** (27 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/internals/models/helpers.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/internals/models/validation.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/internals/models/props/clock.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/internals/models/props/basePickerProps.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/models/adapters.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/models/common.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/models/fields.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/models/pickers.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/models/index.js** (82 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/models/timezone.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/models/views.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/node/models/validation.js** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/internals/models/props/toolbar.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/internals/models/props/tabs.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/internals/models/validation.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/internals/models/index.js** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/internals/models/props/basePickerProps.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/internals/models/props/clock.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/internals/models/helpers.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/internals/models/common.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/internals/models/fields.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/models/timezone.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/models/pickers.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/models/validation.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/models/views.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/models/index.js** (7 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/models/adapters.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/models/common.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/modern/models/fields.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/caniuse-lite/data/features/referrer-policy.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/caniuse-lite/data/features/webauthn.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/caniuse-lite/data/features/contentsecuritypolicy2.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/caniuse-lite/data/features/contentsecuritypolicy.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/caniuse-lite/data/features/feature-policy.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/caniuse-lite/data/features/document-policy.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/LICENSE** (22 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/dist/package.json** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/dist/PlainValue-b8036b75.js** (1275 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/dist/legacy-exports.js** (3 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/types.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/seq.js** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/util.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/types/timestamp.js** (10 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/types/set.js** (3 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/types/pairs.js** (3 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/dist/util.js** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/types/binary.js** (8 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/types/omap.js** (3 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/parse-cst.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/scalar.js** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/seq.js** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/LICENSE** (13 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/index.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/map.js** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/pair.js** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/map.js** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/index.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/pair.js** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/scalar.js** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/browser/parse-cst.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/util.mjs** (18 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/util.js** (16 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/dist/util.js** (19 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/types/binary.js** (8 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/dist/legacy-exports.js** (16 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/dist/PlainValue-ec8e588e.js** (876 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/types/omap.js** (3 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/types/set.js** (3 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/types/pairs.js** (3 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/cosmiconfig/node_modules/yaml/types/timestamp.js** (10 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/dist/esm/package.json** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/encodings/tables/gb18030-ranges.json** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/lib/bom-handling.js** (52 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/encodings/utf7.js** (290 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/media-typer/LICENSE** (22 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/merge-descriptors/index.d.ts** (11 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/media-typer/index.js** (143 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/lib/index.js** (180 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/lib/streams.js** (109 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/encodings/utf32.js** (319 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/.idea/modules.xml** (8 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/.idea/iconv-lite.iml** (12 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/.idea/codeStyles/codeStyleConfig.xml** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/package.json** (44 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/LICENSE** (21 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/.github/dependabot.yml** (11 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/.idea/codeStyles/Project.xml** (47 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/encodings/sbcs-codec.js** (72 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/encodings/utf16.js** (197 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/encodings/sbcs-data.js** (179 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/encodings/index.js** (23 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/.idea/inspectionProfiles/Project_Default.xml** (6 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/qs/.nycrc** (13 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/qs/.editorconfig** (46 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/negotiator/lib/charset.js** (169 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/negotiator/lib/encoding.js** (205 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/negotiator/lib/mediaType.js** (294 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/negotiator/lib/language.js** (179 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/qs/lib/index.js** (11 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/qs/lib/formats.js** (23 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/qs/lib/parse.js** (328 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/qs/lib/utils.js** (268 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/qs/lib/stringify.js** (356 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/qs/dist/qs.js** (141 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/mime-db/index.js** (12 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/mime-db/LICENSE** (23 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/mime-db/README.md** (109 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/mime-db/package.json** (56 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/merge-descriptors/license** (11 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/merge-descriptors/index.js** (26 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/merge-descriptors/readme.md** (55 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/negotiator/HISTORY.md** (114 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/negotiator/index.js** (83 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/negotiator/package.json** (43 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/negotiator/LICENSE** (24 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/mime-types/README.md** (126 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/mime-types/index.js** (211 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/mime-types/LICENSE** (23 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/mime-types/package.json** (45 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/mime-types/mimeScore.js** (52 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/body-parser/LICENSE** (23 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/accepts/README.md** (140 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/accepts/index.js** (238 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/accepts/package.json** (47 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/accepts/LICENSE** (23 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/express/lib/view.js** (205 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/express/LICENSE** (24 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/finalhandler/HISTORY.md** (233 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/fresh/LICENSE** (23 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/fresh/index.js** (136 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/fresh/package.json** (46 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/fresh/README.md** (117 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/fresh/HISTORY.md** (80 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/finalhandler/LICENSE** (22 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/finalhandler/index.js** (293 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/finalhandler/package.json** (43 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/express/index.js** (11 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/content-disposition/HISTORY.md** (66 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/content-disposition/index.js** (459 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/cookie-signature/LICENSE** (22 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/content-disposition/package.json** (44 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/content-disposition/LICENSE** (22 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/content-disposition/README.md** (142 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@jridgewell/gen-mapping/LICENSE** (19 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@jridgewell/gen-mapping/dist/types/sourcemap-segment.d.ts** (12 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/dist/cjs/package.json** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/LICENSE** (21 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@jridgewell/trace-mapping/LICENSE** (19 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@jridgewell/trace-mapping/dist/types/strip-filename.d.ts** (4 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@jridgewell/trace-mapping/dist/types/sourcemap-segment.d.ts** (16 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@jridgewell/trace-mapping/dist/types/by-source.d.ts** (7 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@jridgewell/trace-mapping/dist/types/resolve.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@babel/core/src/config/resolve-targets.ts** (56 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@babel/core/src/config/resolve-targets-browser.ts** (40 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@babel/core/src/config/files/index.ts** (29 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@babel/core/src/config/files/index-browser.ts** (113 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@anthropic-ai/sdk/src/tsconfig.json** (11 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@mui/base/legacy/utils/ClassNameConfigurator.js** (46 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@mui/base/modern/utils/ClassNameConfigurator.js** (47 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/type-is/package.json** (47 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/type-is/LICENSE** (23 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/serve-static/package.json** (41 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/raw-body/index.js** (336 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/raw-body/package.json** (50 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/raw-body/LICENSE** (22 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/qs/test/empty-keys-cases.js** (267 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/qs/test/parse.js** (1276 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/qs/test/utils.js** (262 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/qs/test/stringify.js** (1306 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/raw-body/README.md** (223 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/send/README.md** (317 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/serve-static/LICENSE** (25 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/serve-static/index.js** (208 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/raw-body/SECURITY.md** (24 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@modelcontextprotocol/sdk/node_modules/send/LICENSE** (23 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@mui/base/utils/ClassNameConfigurator.js** (47 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **node_modules/@mui/base/node/utils/ClassNameConfigurator.js** (55 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/react-dropzone/rollup.config.js** (33 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/react-dropzone/commitlint.config.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/react-i18next/lint-staged.config.mjs** (4 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/react-dropzone/typings/tests/tsconfig.json** (22 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/oblivious-set/tsconfig.json** (38 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/react-dropzone/.editorconfig** (13 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/react-transition-group/esm/config.js** (3 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/react-transition-group/cjs/config.js** (9 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/react-transition-group/config/package.json** (6 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/resolve/.editorconfig** (37 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/remove-accents/jest.config.js** (4 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/es-errors/tsconfig.json** (49 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/es-define-property/tsconfig.json** (10 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/es-set-tostringtag/tsconfig.json** (9 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/es-object-atoms/tsconfig.json** (6 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/framer-motion/dist/es/components/MotionConfig/index.mjs** (43 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs** (12 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/dunder-proto/tsconfig.json** (9 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/is-arrayish/.editorconfig** (18 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/has-symbols/tsconfig.json** (10 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/gopd/tsconfig.json** (9 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/has-tostringtag/tsconfig.json** (49 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/hasown/tsconfig.json** (6 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/math-intrinsics/tsconfig.json** (3 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/framer-motion/dist/es/utils/GlobalConfig.mjs** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/framer-motion/dist/es/utils/reduced-motion/use-reduced-motion-config.mjs** (19 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/framer-motion/dist/es/projection/geometry/models.mjs** (17 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/framer-motion/dist/es/render/html/config-motion.mjs** (12 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/framer-motion/dist/es/render/svg/config-motion.mjs** (40 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/framer-motion/dist/es/render/dom/utils/create-config.mjs** (19 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/get-proto/tsconfig.json** (9 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/framer-motion/node_modules/@emotion/is-prop-valid/types/tsconfig.json** (26 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/framer-motion/node_modules/@emotion/memoize/types/tsconfig.json** (26 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/setuptools/config/_validate_pyproject/formats.py** (259 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/setuptools/config/__init__.py** (35 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/pip/_vendor/truststore/_macos.py** (501 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/pip/_vendor/truststore/_windows.py** (554 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/pip-24.0.dist-info/AUTHORS.txt** (760 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/samples/security/spire/istio-spire-config.yaml** (70 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/samples/extauthz/README.md** (99 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **migrations/versions/create_checklist_documents.py** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istiod-remote/templates/telemetryv2_1.17.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istiod-remote/templates/telemetryv2_1.19.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istiod-remote/templates/telemetryv2_1.18.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istiod-remote/templates/telemetryv2_1.20.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istiod-remote/templates/istiod-injector-configmap.yaml** (75 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istio-control/istio-discovery/templates/configmap-jwks.yaml** (14 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istio-cni/templates/configmap-cni.yaml** (33 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istiod-remote/templates/configmap.yaml** (112 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istio-control/istio-discovery/templates/configmap.yaml** (112 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istio-control/istio-discovery/templates/istiod-injector-configmap.yaml** (75 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istio-control/istio-discovery/templates/telemetryv2_1.20.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istio-control/istio-discovery/templates/telemetryv2_1.17.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istio-control/istio-discovery/templates/telemetryv2_1.19.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **manifests/charts/istio-control/istio-discovery/templates/telemetryv2_1.18.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istio-cni/templates/configmap-cni.yaml** (33 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istio-control/istio-discovery/templates/telemetryv2_1.17.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istio-control/istio-discovery/templates/telemetryv2_1.18.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istio-control/istio-discovery/templates/telemetryv2_1.20.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istio-control/istio-discovery/templates/telemetryv2_1.19.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istio-control/istio-discovery/templates/istiod-injector-configmap.yaml** (75 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istio-control/istio-discovery/templates/configmap.yaml** (112 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istio-control/istio-discovery/templates/configmap-jwks.yaml** (14 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/samples/bookinfo/policy/productpage_envoy_ratelimit.yaml** (88 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istiod-remote/templates/telemetryv2_1.20.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istiod-remote/templates/telemetryv2_1.17.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istiod-remote/templates/telemetryv2_1.19.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istiod-remote/templates/telemetryv2_1.18.yaml** (486 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istiod-remote/templates/configmap.yaml** (112 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **istio/istio-1.20.3/manifests/charts/istiod-remote/templates/istiod-injector-configmap.yaml** (75 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/common/map.d.ts** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/common/null.js** (17 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/common/null.d.ts** (4 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/common/seq.d.ts** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/core/float.js** (47 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/core/float.d.ts** (4 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/core/int.d.ts** (4 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/core/int.js** (42 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/common/string.d.ts** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/common/string.js** (16 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/core/bool.js** (21 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/core/bool.d.ts** (4 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/yaml-1.1/timestamp.js** (105 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/src/config.ts** (43 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/yaml-1.1/timestamp.d.ts** (6 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/yaml-1.1/bool.js** (29 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/yaml-1.1/bool.d.ts** (7 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/yaml-1.1/float.js** (50 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/yaml-1.1/float.d.ts** (4 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/yaml-1.1/binary.js** (69 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/yaml-1.1/binary.d.ts** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/yaml-1.1/int.d.ts** (5 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/yaml-1.1/merge.d.ts** (9 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/dist/schema/yaml-1.1/int.js** (76 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/browser/dist/schema/yaml-1.1/timestamp.js** (101 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/browser/dist/schema/yaml-1.1/bool.js** (26 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/browser/dist/schema/yaml-1.1/binary.js** (57 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/browser/dist/schema/yaml-1.1/int.js** (71 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/browser/dist/schema/yaml-1.1/float.js** (46 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/src/components/auth/Register.tsx** (165 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/browser/dist/schema/core/int.js** (38 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/browser/dist/schema/core/float.js** (43 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/browser/dist/schema/common/null.js** (15 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/browser/dist/schema/core/bool.js** (19 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/yaml/browser/dist/schema/common/string.js** (14 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/pip/_vendor/pygments/formatters/_mapping.py** (23 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/pip/_vendor/requests/auth.py** (315 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/pip/_internal/models/direct_url.py** (235 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/pip/_internal/models/index.py** (28 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/pip/_internal/models/format_control.py** (78 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/pip/_internal/models/installation_report.py** (56 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/pip/_internal/models/scheme.py** (31 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/pip/_internal/models/target_python.py** (122 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/venv/Lib/site-packages/pip/_vendor/chardet/jpcntx.py** (238 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/ModelTraining.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/ModelTrainingOutlined.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/ModelTrainingRounded.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/ModelTrainingSharp.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/ModelTrainingTwoTone.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/HorizontalRuleOutlined.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/HorizontalRuleRounded.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/HorizontalRuleSharp.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/HorizontalRule.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/HorizontalRuleTwoTone.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/Policy.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/Policy.js** (17 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/PolicyOutlined.js** (13 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/PolicyOutlined.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/PolicyRounded.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/PolicySharp.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/PolicyRounded.js** (17 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/PolicySharp.js** (17 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/PolicyTwoTone.js** (16 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/PolicyTwoTone.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/base/utils/ClassNameConfigurator.js** (47 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/base/node/utils/ClassNameConfigurator.js** (55 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/base/modern/utils/ClassNameConfigurator.js** (47 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/material/modern/Avatar/Avatar.js** (289 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/material/node/Avatar/Avatar.js** (297 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/material/Avatar/Avatar.js** (289 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/material/legacy/Avatar/Avatar.js** (296 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/models/common.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/models/fields.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/models/pickers.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/models/index.js** (7 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/models/adapters.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/models/timezone.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/models/views.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/models/validation.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/internals/models/props/toolbar.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/internals/models/props/basePickerProps.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/internals/models/validation.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/internals/models/props/clock.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/internals/models/props/tabs.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/internals/models/index.js** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/internals/models/common.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/internals/models/helpers.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/legacy/internals/models/fields.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/models/validation.d.ts** (8 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/models/timezone.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/models/validation.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/models/views.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/models/views.d.ts** (3 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/models/index.js** (7 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/models/index.d.ts** (7 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/models/pickers.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/models/fields.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/models/adapters.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/models/common.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/internals/models/helpers.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/internals/models/helpers.d.ts** (9 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/internals/models/index.d.ts** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/internals/models/index.js** (2 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/internals/models/fields.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/internals/models/common.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/internals/models/props/tabs.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/internals/models/props/toolbar.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/internals/models/validation.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/internals/models/props/basePickerProps.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/x-date-pickers/internals/models/props/clock.js** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/system/modern/styleFunctionSx/defaultSxConfig.js** (291 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/system/legacy/styleFunctionSx/defaultSxConfig.js** (293 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/system/styleFunctionSx/defaultSxConfig.js** (297 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/system/esm/styleFunctionSx/defaultSxConfig.js** (291 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/RuleFolderSharp.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/RuleFolderTwoTone.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/RuleOutlined.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/RuleFolder.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/RuleFolderOutlined.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/RuleFolderRounded.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/RuleRounded.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/RuleSharp.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/RuleTwoTone.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/Rule.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/Schema.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/SchemaOutlined.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/SchemaRounded.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/SchemaSharp.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
- **frontend/node_modules/@mui/icons-material/SchemaTwoTone.d.ts** (1 lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches
