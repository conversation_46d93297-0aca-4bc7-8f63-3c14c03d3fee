﻿param(
    [string]$SourceDir = "C:\Users\<USER>\Documents\repo analysis 202508\PA-CHECK-MM",
    [string]$OutputDir = "C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d"
)

# Ensure we're using UTF8 encoding
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Define output files
$ManifestFile = Join-Path $OutputDir "full_scan_manifest.csv"
$ProgressFile = Join-Path $OutputDir "full_scan_progress.log"
$StateFile = Join-Path $OutputDir "state.json"
$LongPathFile = Join-Path $OutputDir "long_path_analysis.md"

# Initialize progress logging
function Write-Progress-Log {
    param([string]$Message)
    $timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    $logEntry = "$timestamp - $Message"
    Add-Content -Path $ProgressFile -Value $logEntry -Encoding UTF8
    Write-Host $logEntry
}

Write-Progress-Log "Starting manifest generation - phase=manifest"

# Function to get extended path
function Get-ExtendedPath {
    param([string]$Path)
    if ($Path.Length -gt 260 -and -not $Path.StartsWith("\\?\")) {
        return "\\?\$Path"
    }
    return $Path
}

# Function to detect if file is binary
function Test-IsBinary {
    param([string]$FilePath)
    try {
        $extendedPath = Get-ExtendedPath $FilePath
        $bytes = [System.IO.File]::ReadAllBytes($extendedPath) | Select-Object -First 8192
        if ($bytes.Length -eq 0) { return $false }
        
        # Check for null bytes (common in binary files)
        $nullCount = ($bytes | Where-Object { $_ -eq 0 }).Count
        if ($nullCount -gt 0) { return $true }
        
        # Check for high-bit characters (entropy test)
        $highBitCount = ($bytes | Where-Object { $_ -gt 127 }).Count
        $ratio = $highBitCount / $bytes.Length
        return $ratio -gt 0.3
    }
    catch {
        return $true  # Assume binary if we can't read it
    }
}

# Function to compute SHA1
function Get-FileSHA1 {
    param([string]$FilePath, [long]$FileSize)
    try {
        if ($FileSize -gt 52428800) {  # 50MB limit
            return ""
        }
        $extendedPath = Get-ExtendedPath $FilePath
        $sha1 = [System.Security.Cryptography.SHA1]::Create()
        $stream = [System.IO.File]::OpenRead($extendedPath)
        $hash = $sha1.ComputeHash($stream)
        $stream.Close()
        $sha1.Dispose()
        return [System.BitConverter]::ToString($hash).Replace("-", "").ToLower()
    }
    catch {
        return ""
    }
}

# First, do OS enumeration to get baseline count
Write-Progress-Log "Starting OS enumeration for baseline count"
$osFileCount = 0
try {
    $extendedSourceDir = Get-ExtendedPath $SourceDir
    Get-ChildItem -Path $extendedSourceDir -Recurse -File -Force -ErrorAction SilentlyContinue | ForEach-Object {
        $osFileCount++
        if ($osFileCount % 5000 -eq 0) {
            Write-Progress-Log "OS enumeration progress: $osFileCount files counted"
        }
    }
}
catch {
    Write-Progress-Log "ERROR during OS enumeration: $($_.Exception.Message)"
    exit 1
}

Write-Progress-Log "OS enumeration completed: total_files=$osFileCount"

# Check for resume state
$resumeFromPath = $null
$processedCount = 0
if (Test-Path $StateFile) {
    try {
        $state = Get-Content $StateFile -Raw | ConvertFrom-Json
        $resumeFromPath = $state.last_path
        $processedCount = $state.processed_count
        Write-Progress-Log "Resuming from: $resumeFromPath (processed: $processedCount)"
    }
    catch {
        Write-Progress-Log "Could not parse state file, starting fresh"
    }
}

# Initialize manifest file if not resuming
if (-not $resumeFromPath -and -not (Test-Path $ManifestFile)) {
    "path,bytes,is_binary,sha1" | Out-File -FilePath $ManifestFile -Encoding UTF8
    Write-Progress-Log "Created manifest file with header"
}

# Get all files and sort lexicographically
Write-Progress-Log "Collecting all files for lexicographic sorting"
$allFiles = @()
$longPaths = @()

try {
    $extendedSourceDir = Get-ExtendedPath $SourceDir
    Get-ChildItem -Path $extendedSourceDir -Recurse -File -Force -ErrorAction SilentlyContinue | ForEach-Object {
        $relativePath = $_.FullName.Substring($SourceDir.Length + 1).Replace('\', '/')
        $allFiles += @{
            FullPath = $_.FullName
            RelativePath = $relativePath
            Length = $_.Length
        }
        
        if ($_.FullName.Length -gt 260) {
            $longPaths += $_.FullName
        }
    }
}
catch {
    Write-Progress-Log "ERROR collecting files: $($_.Exception.Message)"
    exit 1
}

# Sort files lexicographically by relative path
$sortedFiles = $allFiles | Sort-Object RelativePath

Write-Progress-Log "Collected and sorted $($sortedFiles.Count) files"

# Write long path analysis if any found
if ($longPaths.Count -gt 0) {
    "# Long Path Analysis`n" | Out-File -FilePath $LongPathFile -Encoding UTF8
    "Found $($longPaths.Count) paths requiring extended-length handling:`n" | Add-Content -Path $LongPathFile -Encoding UTF8
    $longPaths | ForEach-Object { "- $_" } | Add-Content -Path $LongPathFile -Encoding UTF8
    Write-Progress-Log "Wrote long path analysis: $($longPaths.Count) paths"
}

# Process files and build manifest
$manifestRows = 0
$startIndex = 0

# Find resume point if applicable
if ($resumeFromPath) {
    for ($i = 0; $i -lt $sortedFiles.Count; $i++) {
        if ($sortedFiles[$i].RelativePath -eq $resumeFromPath) {
            $startIndex = $i + 1
            break
        }
    }
}

Write-Progress-Log "Processing files starting from index $startIndex"

for ($i = $startIndex; $i -lt $sortedFiles.Count; $i++) {
    $file = $sortedFiles[$i]
    $retryCount = 0
    $maxRetries = 15
    $processed = $false
    
    while (-not $processed -and $retryCount -lt $maxRetries) {
        try {
            $isBinary = Test-IsBinary $file.FullPath
            $sha1 = Get-FileSHA1 $file.FullPath $file.Length
            
            # Escape any quotes in the path
            $escapedPath = $file.RelativePath.Replace('"', '""')
            $csvRow = "`"$escapedPath`",$($file.Length),$($isBinary.ToString().ToLower()),`"$sha1`""
            
            Add-Content -Path $ManifestFile -Value $csvRow -Encoding UTF8
            $manifestRows++
            $processed = $true
            
            # Checkpoint every 5000 files or if it's been 60 seconds
            if ($manifestRows % 5000 -eq 0) {
                $state = @{
                    last_path = $file.RelativePath
                    processed_count = $manifestRows
                    timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                }
                $state | ConvertTo-Json | Out-File -FilePath $StateFile -Encoding UTF8
                Write-Progress-Log "Checkpoint: processed=$manifestRows rows_written=$manifestRows last_path=$($file.RelativePath)"
            }
        }
        catch {
            $retryCount++
            if ($retryCount -lt $maxRetries) {
                Start-Sleep -Milliseconds (100 * $retryCount)  # Exponential backoff
            } else {
                # Record error entry
                $escapedPath = $file.RelativePath.Replace('"', '""')
                $csvRow = "`"$escapedPath`",0,true,`"`""
                Add-Content -Path $ManifestFile -Value $csvRow -Encoding UTF8
                $manifestRows++
                Write-Progress-Log "ERROR processing $($file.RelativePath): $($_.Exception.Message)"
                $processed = $true
            }
        }
    }
}

Write-Progress-Log "Manifest generation completed: manifest_rows=$manifestRows"

# Final validation
if ($manifestRows -ne $osFileCount) {
    Write-Progress-Log "ABORT_MISMATCH manifest=$manifestRows os=$osFileCount"
    
    # Try to identify some differences (simplified approach)
    $manifestPaths = @()
    if (Test-Path $ManifestFile) {
        $manifestContent = Get-Content $ManifestFile -Encoding UTF8
        for ($i = 1; $i -lt $manifestContent.Count; $i++) {  # Skip header
            if ($manifestContent[$i] -match '^"([^"]+)"') {
                $manifestPaths += $matches[1]
            }
        }
    }
    
    $osPaths = $sortedFiles | ForEach-Object { $_.RelativePath }
    $missing = Compare-Object $osPaths $manifestPaths | Select-Object -First 20
    
    if ($missing) {
        Write-Progress-Log "Sample differences:"
        $missing | ForEach-Object {
            Write-Progress-Log "  $($_.SideIndicator) $($_.InputObject)"
        }
    }
    
    exit 1
} else {
    Write-Progress-Log "COMPLETED_MANIFEST manifest=$manifestRows os=$osFileCount"
    
    # Clean up state file on successful completion
    if (Test-Path $StateFile) {
        Remove-Item $StateFile -Force
    }
    
    exit 0
}
