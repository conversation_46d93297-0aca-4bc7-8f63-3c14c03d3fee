﻿# CBCS Salvage – Large File Extraction

This archive contains oversized files (≥25MB) removed from the CBCS legacy project to improve Git handling.

## Summary

* Files removed: 33
* Total size removed: 5,126.4 MB
* File types: .rar, .zip, .html, .json, .pack, .wasm, .md
* Date of operation: 2025-08-05

## File Types Breakdown

* .json: 15 files
* .pack: 8 files  
* .rar: 2 files
* .zip: 2 files
* .wasm: 2 files
* .html: 2 files
* .md: 1 file
* (no extension): 1 file

## Purpose

These files were moved to prevent Git push failures due to GitHub's file size limitations. All files are preserved in the salvage_assets/ directory with their original structure maintained.

## Location

Original source: C:/Users/<USER>/Documents/repo analysis 202508/CBCS/
Salvage location: C:/Users/<USER>/Documents/SalvageControlSystem/CBCS/salvage_assets/

See file_inventory.csv for detailed file mapping.
