param(
    [string]$SourceDir = "C:\Users\<USER>\Documents\repo analysis 202508\PA-CHECK-MM",
    [string]$OutputDir = "C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d",
    [string]$ManifestPath = "C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d\full_scan_manifest.csv"
)

$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

$DeepNuggetsFile = Join-Path $OutputDir "deep_nuggets.jsonl"
$NuggetsReportFile = Join-Path $OutputDir "deep_nuggets_report.md"
$CoverageFile = Join-Path $OutputDir "full_scan_coverage.json"
$ProgressFile = Join-Path $OutputDir "full_scan_progress.log"
$DetectorExpansionFile = Join-Path $OutputDir "detector_expansion.md"
$ZeroHitReportFile = Join-Path $OutputDir "zero_hit_report.md"
$StateFile = Join-Path $OutputDir "state.json"

function Write-Progress-Log {
    param([string]$Message)
    $timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    $logEntry = "$timestamp - $Message"
    Add-Content -Path $ProgressFile -Value $logEntry -Encoding UTF8
    Write-Host $logEntry
}

Write-Progress-Log "Starting Phase 1D content scan"

# FEMA/Policy patterns
$FemaPatterns = @(
    "PAPPG", "Public Assistance", "Individual Assistance", "FEMA", "Category", 
    "DRRA", "1235b", "1206", "HMGP", "HMA", "CFR", "procurement", 
    "mutual aid", "force account", "project", "damage inventory", 
    "site worksheet", "hazard mitigation", "EHP", "Davis-Bacon", "BABA"
)

# Data/Schema patterns
$DataPatterns = @(
    "schema", "model", "DDL", "Prisma", "CREATE TABLE", "OpenAPI", "Swagger",
    "interface", "zod", "pydantic", "rules", "checklist", "mapping",
    "decision table", "state machine", "evaluation", "parser", "validator"
)

# Auth/Security patterns
$AuthPatterns = @(
    "JWT", "JWE", "OAuth", "HMAC", "signature", "roles", "permissions",
    "rbac", "abac", "rate limit", "retry", "circuit breaker", "secrets",
    "kms", "vault", "bcrypt", "argon2", "crypto"
)

function Get-ExtendedPath {
    param([string]$Path)
    if ($Path.Length -gt 260 -and -not $Path.StartsWith("\\?\")) {
        return "\\?\$Path"
    }
    return $Path
}

function Find-Nuggets {
    param([string[]]$Lines, [string]$FilePath, [string]$FileHash)
    
    $nuggets = @()
    $detectedPatterns = @()
    
    for ($i = 0; $i -lt $Lines.Count; $i++) {
        $line = $Lines[$i]
        $matchedDetectors = @()
        
        # Check FEMA patterns
        foreach ($pattern in $FemaPatterns) {
            if ($line -match [regex]::Escape($pattern)) {
                $matchedDetectors += "regex:$pattern"
                $detectedPatterns += $pattern
            }
        }
        
        # Check Data patterns
        foreach ($pattern in $DataPatterns) {
            if ($line -match [regex]::Escape($pattern)) {
                $matchedDetectors += "regex:$pattern"
                $detectedPatterns += $pattern
            }
        }
        
        # Check Auth patterns
        foreach ($pattern in $AuthPatterns) {
            if ($line -match [regex]::Escape($pattern)) {
                $matchedDetectors += "regex:$pattern"
                $detectedPatterns += $pattern
            }
        }
        
        if ($matchedDetectors.Count -gt 0) {
            $startLine = [Math]::Max(0, $i - 1)
            $endLine = [Math]::Min($Lines.Count - 1, $i + 10)
            
            # Ensure 3-12 line range
            if (($endLine - $startLine + 1) -lt 3) {
                $endLine = [Math]::Min($Lines.Count - 1, $startLine + 2)
            }
            if (($endLine - $startLine + 1) -gt 12) {
                $endLine = $startLine + 11
            }
            
            $snippet = ($Lines[$startLine..$endLine] -join "`n").Trim()
            $snippet = $snippet -replace '(?i)(password|secret|key|token)\s*[:=]\s*\S+', '$1=***REDACTED***'
            
            # Determine category
            $category = "Other"
            if ($matchedDetectors -match "FEMA|PAPPG|Public Assistance|EHP|DRRA") { $category = "FEMA/Compliance" }
            elseif ($matchedDetectors -match "schema|model|interface|DDL") { $category = "Data Models" }
            elseif ($matchedDetectors -match "JWT|OAuth|auth|rbac") { $category = "Authentication/Security" }
            elseif ($matchedDetectors -match "state machine|workflow|wizard") { $category = "Wizard/Workflow" }
            elseif ($FilePath -match "frontend|ui|component") { $category = "Frontend/UI" }
            elseif ($FilePath -match "backend|api|server") { $category = "Backend Logic" }
            elseif ($FilePath -match "config|yaml|json") { $category = "Config/Data Assets" }
            elseif ($FilePath -match "\.md$|\.txt$|README") { $category = "Docs/Guides" }
            
            # Generate tags (minimum 3)
            $tags = @()
            if ($FilePath -match "frontend|ui|component") { $tags += "frontend" }
            elseif ($FilePath -match "backend|api|server") { $tags += "backend" }
            else { $tags += "core" }
            
            if ($matchedDetectors -match "FEMA|PAPPG|Public Assistance") { $tags += "fema-pa" }
            elseif ($matchedDetectors -match "BCA|benefit") { $tags += "bca" }
            elseif ($matchedDetectors -match "EHP|environmental") { $tags += "ehp" }
            else { $tags += "policy" }
            
            $fileExt = [System.IO.Path]::GetExtension($FilePath).ToLower().TrimStart('.')
            if ($fileExt) { $tags += $fileExt } else { $tags += "text" }
            
            while ($tags.Count -lt 3) { $tags += "general" }
            
            # Create nugget with exact schema
            $nugget = @{
                path = $FilePath
                sha1 = $FileHash
                category = $category
                tags = $tags
                lines = @($startLine + 1, $endLine + 1)
                snippet = $snippet
                reason = "Contains domain-relevant patterns: $($matchedDetectors -join ', ')"
                detectors = $matchedDetectors
                risk_level = "medium"
                action = "review-security"
                has_more_in_file = $false
                discovery_mode = "manifest-scan"
                ts_utc = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                sig = [System.Security.Cryptography.SHA1]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes("$FilePath$($startLine + 1)$($endLine + 1)$snippet")) | ForEach-Object { $_.ToString("x2") } | Join-String
            }
            
            $nuggets += $nugget
        }
    }
    
    return $nuggets, $detectedPatterns
}

function Test-ZeroHitCriteria {
    param([string]$FilePath, [string[]]$Lines)
    
    $domainRelevant = $FilePath -match "wizards?|rules?|auth|schema|models?|configs?|mappings?|policy|pappg|drra|elig|checklist"
    if ($domainRelevant) { return $true }
    
    $wordCount = ($Lines -join " ").Split(" ", [StringSplitOptions]::RemoveEmptyEntries).Count
    if ($wordCount -lt 500) { return $false }
    
    $policyTermCount = 0
    foreach ($line in $Lines) {
        if ($line -match "FEMA|PAPPG|Category|DRRA|policy|compliance|regulation|procurement|assistance|mitigation") {
            $policyTermCount++
            if ($policyTermCount -ge 3) { return $true }
        }
    }
    
    return $false
}

# Load manifest
Write-Progress-Log "Loading manifest from $ManifestPath"
$manifestContent = Get-Content $ManifestPath -Encoding UTF8
$manifestRows = @()

for ($i = 1; $i -lt $manifestContent.Count; $i++) {
    $line = $manifestContent[$i]
    if ($line -match '^"([^"]+)",(\d+),(true|false),"([^"]*)"') {
        $manifestRows += @{
            path = $matches[1]
            bytes = [int]$matches[2]
            is_binary = $matches[3] -eq "true"
            sha1 = $matches[4]
            index = $i - 1
        }
    }
}

$totalFiles = $manifestRows.Count
Write-Progress-Log "Loaded manifest: $totalFiles files to process"

# Resume state
$startIndex = 0
$processedCount = 0
$totalNuggets = 0

if (Test-Path $StateFile) {
    try {
        $state = Get-Content $StateFile -Raw | ConvertFrom-Json
        $startIndex = $state.manifest_index
        $processedCount = $state.processed_count
        $totalNuggets = $state.total_nuggets
        Write-Progress-Log "Resuming from index $startIndex (processed: $processedCount, nuggets: $totalNuggets)"
    }
    catch {
        Write-Progress-Log "Starting fresh"
    }
}

# Initialize output files
if ($startIndex -eq 0) {
    "" | Out-File -FilePath $DeepNuggetsFile -Encoding UTF8
    "# Deep Nuggets Report`n`nGenerated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss UTC')`n" | Out-File -FilePath $NuggetsReportFile -Encoding UTF8
    "# Zero Hit Report`n`nFiles with 0 nuggets but domain relevance:`n" | Out-File -FilePath $ZeroHitReportFile -Encoding UTF8
    "# Detector Expansion`n`nNew domain tokens/phrases discovered during scan:`n" | Out-File -FilePath $DetectorExpansionFile -Encoding UTF8
}

# Process files
$coverage = @()
$zeroHits = @()
$newDetectors = @()

Write-Progress-Log "Starting file processing from index $startIndex"

for ($i = $startIndex; $i -lt $manifestRows.Count; $i++) {
    $fileInfo = $manifestRows[$i]
    $absolutePath = Join-Path $SourceDir $fileInfo.path
    $extendedPath = Get-ExtendedPath $absolutePath
    
    # Create coverage entry
    $coverageEntry = @{
        path = $fileInfo.path
        sha1 = $fileInfo.sha1
        bytes = $fileInfo.bytes
        is_binary = $fileInfo.is_binary
        scanned_lines = 0
        nuggets = 0
        has_more_in_file = $false
        first_seen_utc = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        last_scan_utc = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        error = $null
    }
    
    try {
        if ($fileInfo.is_binary) {
            $coverageEntry.scanned_lines = 0
            $coverageEntry.nuggets = 0
        }
        else {
            $retryCount = 0
            $maxRetries = 15
            $fileProcessed = $false
            
            while (-not $fileProcessed -and $retryCount -lt $maxRetries) {
                try {
                    $lines = Get-Content $extendedPath -Encoding UTF8 -ErrorAction Stop
                    $coverageEntry.scanned_lines = $lines.Count
                    
                    $nuggets, $detectedPatterns = Find-Nuggets $lines $fileInfo.path $fileInfo.sha1
                    $coverageEntry.nuggets = $nuggets.Count
                    $totalNuggets += $nuggets.Count
                    
                    # Write nuggets immediately
                    foreach ($nugget in $nuggets) {
                        $nuggetJson = $nugget | ConvertTo-Json -Compress
                        Add-Content -Path $DeepNuggetsFile -Value $nuggetJson -Encoding UTF8
                        
                        $reportEntry = "## $($nugget.path) (Lines $($nugget.lines[0])-$($nugget.lines[1]))`n**Category:** $($nugget.category)`n**Tags:** $($nugget.tags -join ', ')`n**Reason:** $($nugget.reason)`n````n$($nugget.snippet)`n```n`n"
                        Add-Content -Path $NuggetsReportFile -Value $reportEntry -Encoding UTF8
                    }
                    
                    # Check zero hits
                    if ($nuggets.Count -eq 0 -and (Test-ZeroHitCriteria $fileInfo.path $lines)) {
                        $zeroHitEntry = "- **$($fileInfo.path)** ($($lines.Count) lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches"
                        Add-Content -Path $ZeroHitReportFile -Value $zeroHitEntry -Encoding UTF8
                        $zeroHits += $fileInfo.path
                    }
                    
                    $newDetectors += $detectedPatterns
                    $fileProcessed = $true
                }
                catch {
                    $retryCount++
                    if ($retryCount -lt $maxRetries) {
                        Start-Sleep -Milliseconds (100 * $retryCount)
                    } else {
                        $coverageEntry.error = "locked"
                        $fileProcessed = $true
                    }
                }
            }
        }
    }
    catch {
        $coverageEntry.error = $_.Exception.Message
    }
    
    $coverage += $coverageEntry
    $processedCount++
    
    # Checkpoint every 100 files
    if ($processedCount % 100 -eq 0) {
        $state = @{
            manifest_index = $i + 1
            last_path = $fileInfo.path
            processed_count = $processedCount
            total_nuggets = $totalNuggets
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        }
        $state | ConvertTo-Json | Out-File -FilePath $StateFile -Encoding UTF8
        Write-Progress-Log "Checkpoint: processed=$processedCount nuggets=$totalNuggets last_path=$($fileInfo.path) last_sha1=$($fileInfo.sha1)"
    }
}

Write-Progress-Log "File processing completed: processed=$processedCount nuggets=$totalNuggets"

# Write detector expansion
if ($newDetectors.Count -gt 0) {
    $uniqueDetectors = $newDetectors | Sort-Object -Unique
    $expansionContent = "## New Patterns Discovered`n`nTotal: $($uniqueDetectors.Count)`n`n"
    foreach ($detector in $uniqueDetectors) {
        $expansionContent += "- $detector`n"
    }
    Add-Content -Path $DetectorExpansionFile -Value $expansionContent -Encoding UTF8
}

# Write final coverage
$coverage | ConvertTo-Json | Out-File -FilePath $CoverageFile -Encoding UTF8

# HARD GATES validation
$manifestCount = $totalFiles
$coverageCount = $coverage.Count

if ($coverageCount -ne $manifestCount) {
    Write-Progress-Log "ABORT_MISMATCH manifest=$manifestCount coverage=$coverageCount"
    exit 1
} else {
    Write-Progress-Log "COMPLETED coverage=$coverageCount manifest=$manifestCount"
    
    $uniqueDetectors = $newDetectors | Sort-Object -Unique
    $summary = @"
# Phase 1D Content Scan Summary

**Completion Status:** COMPLETED
**Files Processed:** $processedCount / $manifestCount
**Nuggets Extracted:** $totalNuggets
**Zero Hits Identified:** $($zeroHits.Count)
**Detector Expansion:** $($uniqueDetectors.Count) unique patterns

## Output Files Generated:
- deep_nuggets.jsonl ($totalNuggets entries)
- deep_nuggets_report.md (human-readable)
- full_scan_coverage.json ($coverageCount entries)
- zero_hit_report.md ($($zeroHits.Count) entries)
- detector_expansion.md ($($uniqueDetectors.Count) patterns)

## Validation Results:
**HARD GATES PASSED:** Coverage ($coverageCount) = Manifest ($manifestCount)

**Phase 1D content scanning completed successfully.**
"@
    
    Write-Host $summary
    Add-Content -Path $ProgressFile -Value $summary -Encoding UTF8
    
    if (Test-Path $StateFile) {
        Remove-Item $StateFile -Force
    }
    
    exit 0
}
