param(
    [string]$SourceDir = "C:\Users\<USER>\Documents\repo analysis 202508\PA-CHECK-MM",
    [string]$OutputDir = "C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d",
    [string]$ManifestPath = "C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d\full_scan_manifest.csv",
    [int]$BatchSize = 50
)

# Ensure UTF8 encoding
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Define output files
$DeepNuggetsFile = Join-Path $OutputDir "deep_nuggets.jsonl"
$NuggetsReportFile = Join-Path $OutputDir "deep_nuggets_report.md"
$CoverageFile = Join-Path $OutputDir "full_scan_coverage.json"
$ProgressFile = Join-Path $OutputDir "full_scan_progress.log"
$ZeroHitReportFile = Join-Path $OutputDir "zero_hit_report.md"
$StateFile = Join-Path $OutputDir "state.json"

# Initialize progress logging
function Write-Progress-Log {
    param([string]$Message)
    $timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    $logEntry = "$timestamp - $Message"
    Add-Content -Path $ProgressFile -Value $logEntry -Encoding UTF8
    Write-Host $logEntry
}

Write-Progress-Log "Starting Fast Phase 1D content scan (batch size: $BatchSize)"

# Simple detector patterns
$FemaKeywords = @("PAPPG", "FEMA", "Category", "DRRA", "Public Assistance", "Individual Assistance")
$DataKeywords = @("schema", "model", "interface", "CREATE TABLE", "OpenAPI", "Swagger")
$AuthKeywords = @("JWT", "OAuth", "auth", "login", "password", "token", "rbac")

# Function to get extended path
function Get-ExtendedPath {
    param([string]$Path)
    if ($Path.Length -gt 260 -and -not $Path.StartsWith("\\?\")) {
        return "\\?\$Path"
    }
    return $Path
}

# Function to find nuggets (simplified)
function Find-SimpleNuggets {
    param(
        [string[]]$Lines,
        [string]$FilePath,
        [string]$FileHash
    )
    
    $nuggets = @()
    $lineNum = 0
    
    foreach ($line in $Lines) {
        $lineNum++
        $foundKeywords = @()
        
        # Check for FEMA keywords
        foreach ($keyword in $FemaKeywords) {
            if ($line -match [regex]::Escape($keyword)) {
                $foundKeywords += "fema:$keyword"
            }
        }
        
        # Check for Data keywords
        foreach ($keyword in $DataKeywords) {
            if ($line -match [regex]::Escape($keyword)) {
                $foundKeywords += "data:$keyword"
            }
        }
        
        # Check for Auth keywords
        foreach ($keyword in $AuthKeywords) {
            if ($line -match [regex]::Escape($keyword)) {
                $foundKeywords += "auth:$keyword"
            }
        }
        
        if ($foundKeywords.Count -gt 0) {
            $startLine = [Math]::Max(1, $lineNum - 2)
            $endLine = [Math]::Min($Lines.Count, $lineNum + 2)
            
            $snippet = ($Lines[($startLine-1)..($endLine-1)] | ForEach-Object { $_.Trim() }) -join "`n"
            $snippet = $snippet.Substring(0, [Math]::Min(300, $snippet.Length))
            
            # Simple secret redaction
            $snippet = $snippet -replace 'password\s*[:=]\s*\S+', 'password=***'
            $snippet = $snippet -replace 'secret\s*[:=]\s*\S+', 'secret=***'
            
            $category = "Other"
            if ($foundKeywords -match "fema:") { $category = "FEMA/Compliance" }
            elseif ($foundKeywords -match "data:") { $category = "Data Models" }
            elseif ($foundKeywords -match "auth:") { $category = "Authentication/Security" }
            
            $tags = @("core", "scan", [System.IO.Path]::GetExtension($FilePath).TrimStart('.'))
            if ($foundKeywords -match "fema:") { $tags += "fema-pa" }
            
            $nugget = @{
                path = $FilePath
                sha1 = $FileHash
                category = $category
                tags = $tags
                lines = @($startLine, $endLine)
                snippet = $snippet
                reason = "Found keywords: $($foundKeywords -join ', ')"
                detectors = $foundKeywords
                risk_level = "medium"
                action = "review"
                has_more_in_file = $false
                discovery_mode = "fast-scan"
                ts_utc = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                sig = [System.Security.Cryptography.SHA1]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes("$FilePath$lineNum$snippet")) | ForEach-Object { $_.ToString("x2") } | Join-String
            }
            
            $nuggets += $nugget
        }
    }
    
    return $nuggets
}

# Load manifest
Write-Progress-Log "Loading manifest from $ManifestPath"
$manifestContent = Get-Content $ManifestPath -Encoding UTF8
$manifestRows = @()

Write-Progress-Log "Parsing manifest entries..."
for ($i = 1; $i -lt $manifestContent.Count; $i++) {  # Skip header
    $line = $manifestContent[$i]
    if ($line -match '^"([^"]+)",(\d+),(true|false),"([^"]*)"') {
        $manifestRows += @{
            path = $matches[1]
            bytes = [int]$matches[2]
            is_binary = $matches[3] -eq "true"
            sha1 = $matches[4]
            index = $i - 1
        }
    }
    
    if ($i % 10000 -eq 0) {
        Write-Progress-Log "Parsed $i manifest entries..."
    }
}

$totalFiles = $manifestRows.Count
Write-Progress-Log "Loaded manifest: $totalFiles files to process"

# Check for resume state
$startIndex = 0
$processedCount = 0
$totalNuggets = 0

if (Test-Path $StateFile) {
    try {
        $state = Get-Content $StateFile -Raw | ConvertFrom-Json
        $startIndex = $state.manifest_index
        $processedCount = $state.processed_count
        $totalNuggets = $state.total_nuggets
        Write-Progress-Log "Resuming from index $startIndex (processed: $processedCount, nuggets: $totalNuggets)"
    }
    catch {
        Write-Progress-Log "Could not parse state file, starting fresh"
    }
}

# Initialize output files if starting fresh
if ($startIndex -eq 0) {
    "" | Out-File -FilePath $DeepNuggetsFile -Encoding UTF8
    "# Deep Nuggets Report`n`nGenerated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss UTC')`n" | Out-File -FilePath $NuggetsReportFile -Encoding UTF8
    "# Zero Hit Report`n`nFiles with 0 nuggets but domain relevance:`n" | Out-File -FilePath $ZeroHitReportFile -Encoding UTF8
}

# Process files in batches
$coverage = @()
$zeroHits = @()

Write-Progress-Log "Starting file processing from index $startIndex in batches of $BatchSize"

for ($batchStart = $startIndex; $batchStart -lt $manifestRows.Count; $batchStart += $BatchSize) {
    $batchEnd = [Math]::Min($batchStart + $BatchSize - 1, $manifestRows.Count - 1)
    Write-Progress-Log "Processing batch: files $batchStart to $batchEnd"
    
    for ($i = $batchStart; $i -le $batchEnd; $i++) {
        $fileInfo = $manifestRows[$i]
        $absolutePath = Join-Path $SourceDir $fileInfo.path
        $extendedPath = Get-ExtendedPath $absolutePath
        
        $coverageEntry = @{
            path = $fileInfo.path
            sha1 = $fileInfo.sha1
            bytes = $fileInfo.bytes
            is_binary = $fileInfo.is_binary
            scanned_lines = 0
            nuggets = 0
            has_more_in_file = $false
            first_seen_utc = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            last_scan_utc = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            error = $null
        }
        
        try {
            if ($fileInfo.is_binary) {
                # Binary file - just record coverage
                $coverageEntry.scanned_lines = 0
                $coverageEntry.nuggets = 0
            }
            else {
                # Text file - read and analyze
                try {
                    $lines = Get-Content $extendedPath -Encoding UTF8 -ErrorAction Stop
                    $coverageEntry.scanned_lines = $lines.Count
                    
                    $nuggets = Find-SimpleNuggets $lines $fileInfo.path $fileInfo.sha1
                    $coverageEntry.nuggets = $nuggets.Count
                    $totalNuggets += $nuggets.Count
                    
                    # Write nuggets to JSONL
                    foreach ($nugget in $nuggets) {
                        $nuggetJson = $nugget | ConvertTo-Json -Compress
                        Add-Content -Path $DeepNuggetsFile -Value $nuggetJson -Encoding UTF8
                        
                        # Add to report
                        $reportEntry = "## $($nugget.path) (Lines $($nugget.lines[0])-$($nugget.lines[1]))`n"
                        $reportEntry += "**Category:** $($nugget.category)`n"
                        $reportEntry += "**Reason:** $($nugget.reason)`n"
                        $reportEntry += "````n$($nugget.snippet)`n```n`n"
                        Add-Content -Path $NuggetsReportFile -Value $reportEntry -Encoding UTF8
                    }
                    
                    # Check for zero hits on domain-relevant files
                    if ($nuggets.Count -eq 0 -and ($fileInfo.path -match "wizard|rule|auth|schema|model|config|policy")) {
                        $zeroHitEntry = "- **$($fileInfo.path)** ($($lines.Count) lines) - Domain-relevant but no matches"
                        Add-Content -Path $ZeroHitReportFile -Value $zeroHitEntry -Encoding UTF8
                        $zeroHits += $fileInfo.path
                    }
                }
                catch {
                    $coverageEntry.error = "read_error"
                }
            }
        }
        catch {
            $coverageEntry.error = $_.Exception.Message
        }
        
        $coverage += $coverageEntry
        $processedCount++
    }
    
    # Checkpoint after each batch
    $state = @{
        manifest_index = $batchEnd + 1
        last_path = $manifestRows[$batchEnd].path
        processed_count = $processedCount
        total_nuggets = $totalNuggets
        timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    }
    $state | ConvertTo-Json | Out-File -FilePath $StateFile -Encoding UTF8
    Write-Progress-Log "Batch checkpoint: processed=$processedCount nuggets=$totalNuggets last_path=$($manifestRows[$batchEnd].path)"
}

Write-Progress-Log "File processing completed: processed=$processedCount nuggets=$totalNuggets"

# Write final coverage file
Write-Progress-Log "Writing coverage file..."
$coverage | ConvertTo-Json | Out-File -FilePath $CoverageFile -Encoding UTF8

# Final validation
$manifestCount = $totalFiles
$coverageCount = $coverage.Count

if ($coverageCount -ne $manifestCount) {
    Write-Progress-Log "ABORT_MISMATCH manifest=$manifestCount coverage=$coverageCount"
    exit 1
} else {
    Write-Progress-Log "COMPLETED coverage=$coverageCount manifest=$manifestCount"
    
    # Generate summary
    $summary = @"
# Phase 1D Fast Content Scan Summary

**Completion Status:** COMPLETED
**Files Processed:** $processedCount / $manifestCount
**Nuggets Extracted:** $totalNuggets
**Zero Hits Identified:** $($zeroHits.Count)

**Output Files Generated:**
- deep_nuggets.jsonl ($totalNuggets entries)
- deep_nuggets_report.md (human-readable)
- full_scan_coverage.json ($coverageCount entries)
- zero_hit_report.md ($($zeroHits.Count) entries)

**Validation:** Perfect parity achieved (Coverage = Manifest = $coverageCount)
"@
    
    Write-Host $summary
    Add-Content -Path $ProgressFile -Value $summary -Encoding UTF8
    
    # Clean up state file
    if (Test-Path $StateFile) {
        Remove-Item $StateFile -Force
    }
    
    exit 0
}
