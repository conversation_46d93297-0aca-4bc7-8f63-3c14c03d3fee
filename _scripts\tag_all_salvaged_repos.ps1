# Git Tagging Script – Salvage Phase 1

$repoRoot = "C:\Users\<USER>\Documents\repo analysis 202508"
$tagPrefix = "v0.1-salvage"
$logFile = "$repoRoot\_tagging_log.txt"

# List of repos that have completed salvage
$salvagedRepos = @(
    "000-RECOVERED COMPLIANCEMAX FILES",
    "EaseUS_Recovery",
    "CBCS",
    "CBCD Application V2",
    "compliancemax",
    "compliancemax_clean",
    "ComplianceMax_Cursor_Recovery_Starter",
    "compliancemax_final",
    "PA-CHECK",
    "PA-CHECK-MM",
    "ALL NEW APP"
)

"--- Salvage Tagging Log $(Get-Date) ---" | Out-File $logFile -Encoding utf8

foreach ($repo in $salvagedRepos) {
    $repoPath = Join-Path $repoRoot $repo
    if (Test-Path "$repoPath\.git") {
        Set-Location $repoPath
        $safeTag = "$tagPrefix-$($repo -replace '\s+', '-').ToLower()"

        try {
            git tag -a $safeTag -m ("Salvage complete for " + $repo)
            $logLine = "Tagged " + $repo + " with " + $safeTag
            $logLine | Out-File $logFile -Append
        }
        catch {
            $errorMessage = $_.Exception.Message
            $logLine = "Failed to tag " + $repo + ": " + $errorMessage
            $logLine | Out-File $logFile -Append
        }
    }
    else {
        $logLine = "No Git repo found in " + $repo + " - skipping"
        $logLine | Out-File $logFile -Append
    }
}

Set-Location $repoRoot
"Tagging process complete." | Out-File $logFile -Append
