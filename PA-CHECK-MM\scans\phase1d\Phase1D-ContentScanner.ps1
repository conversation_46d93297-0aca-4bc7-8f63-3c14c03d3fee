param(
    [string]$SourceDir = "C:\Users\<USER>\Documents\repo analysis 202508\PA-CHECK-MM",
    [string]$OutputDir = "C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d",
    [string]$ManifestPath = "C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d\full_scan_manifest.csv"
)

# Ensure UTF8 encoding
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Define output files exactly as specified
$DeepNuggetsFile = Join-Path $OutputDir "deep_nuggets.jsonl"
$NuggetsReportFile = Join-Path $OutputDir "deep_nuggets_report.md"
$CoverageFile = Join-Path $OutputDir "full_scan_coverage.json"
$ProgressFile = Join-Path $OutputDir "full_scan_progress.log"
$DetectorExpansionFile = Join-Path $OutputDir "detector_expansion.md"
$ZeroHitReportFile = Join-Path $OutputDir "zero_hit_report.md"
$StateFile = Join-Path $OutputDir "state.json"

# Progress logging function
function Write-Progress-Log {
    param([string]$Message)
    $timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    $logEntry = "$timestamp - $Message"
    Add-Content -Path $ProgressFile -Value $logEntry -Encoding UTF8
    Write-Host $logEntry
}

Write-Progress-Log "Starting Phase 1D content scan - exact specification implementation"

# DETECTORS - exactly as specified
$FemaPolicyPatterns = @(
    "PAPPG", "Public Assistance", "Individual Assistance", "FEMA", "Category[\s-]?[A-G]\b", 
    "DRRA", "1235b", "1206", "\bHMGP\b", "\bHMA\b", "44\s*CFR", "2\s*CFR\s*200", 
    "procurement", "time[- ]and[- ]materials", "mutual aid", "force account", 
    "small project", "large project", "damage inventory", "site worksheet", 
    "hazard mitigation", "EHP", "Davis[- ]Bacon", "BABA"
)

$DataSchemaRulesPatterns = @(
    "schema", "model", "DDL", "Prisma", "CREATE TABLE", "OpenAPI", "Swagger", 
    "interface", "zod", "pydantic", "rules?", "checklist", "mapping", 
    "decision table", "state machine", "evaluation", "parser", "validator"
)

$AuthSecurityInfraPatterns = @(
    "JWT", "JWE", "OAuth", "HMAC", "signature", "roles", "permissions", 
    "rbac", "abac", "rate limit", "retry", "circuit breaker", "secrets?:", 
    "kms", "vault", "bcrypt", "argon2", "crypto"
)

# Additional domain-specific patterns discovered during analysis
$ExpandedPatterns = @(
    "BCA", "benefit.cost", "remediation", "compliance", "flood.map", 
    "environmental", "historic.preservation", "cbcs", "cost.share"
)

# Extended path function
function Get-ExtendedPath {
    param([string]$Path)
    if ($Path.Length -gt 260 -and -not $Path.StartsWith("\\?\")) {
        return "\\?\$Path"
    }
    return $Path
}

# Secret redaction function
function Redact-Secrets {
    param([string]$Text)
    $Text = $Text -replace '(?i)(password|secret|key|token|api_key)\s*[:=]\s*["\''`]?[^\s"''`]+["\''`]?', '$1=***REDACTED***'
    $Text = $Text -replace '(?i)(bearer\s+)[a-zA-Z0-9\-_\.]+', '$1***REDACTED***'
    $Text = $Text -replace '(?i)(authorization:\s*)[^\s]+', '$1***REDACTED***'
    return $Text
}

# Nugget detection function - implements exact specification
function Find-Nuggets {
    param(
        [string[]]$Lines,
        [string]$FilePath,
        [string]$FileHash
    )
    
    $nuggets = @()
    $detectedPatterns = @()
    
    for ($i = 0; $i -lt $Lines.Count; $i++) {
        $line = $Lines[$i]
        $matchedDetectors = @()
        
        # FEMA/Policy detection
        foreach ($pattern in $FemaPolicyPatterns) {
            if ($line -match $pattern) {
                $matchedDetectors += "regex:$pattern"
                $detectedPatterns += $pattern
            }
        }
        
        # Data/Schema/Rules detection
        foreach ($pattern in $DataSchemaRulesPatterns) {
            if ($line -match $pattern) {
                $matchedDetectors += "regex:$pattern"
                $detectedPatterns += $pattern
            }
        }
        
        # Auth/Security/Infra detection
        foreach ($pattern in $AuthSecurityInfraPatterns) {
            if ($line -match $pattern) {
                $matchedDetectors += "regex:$pattern"
                $detectedPatterns += $pattern
            }
        }
        
        # Expanded patterns
        foreach ($pattern in $ExpandedPatterns) {
            if ($line -match $pattern) {
                $matchedDetectors += "keyword:$pattern"
                $detectedPatterns += $pattern
            }
        }
        
        # If matches found, extract nugget (3-12 lines as specified)
        if ($matchedDetectors.Count -gt 0) {
            $startLine = [Math]::Max(0, $i - 1)
            $endLine = [Math]::Min($Lines.Count - 1, $i + 10)
            
            # Ensure 3-12 line range
            if (($endLine - $startLine + 1) -lt 3) {
                $endLine = [Math]::Min($Lines.Count - 1, $startLine + 2)
            }
            if (($endLine - $startLine + 1) -gt 12) {
                $endLine = $startLine + 11
            }
            
            $snippet = ($Lines[$startLine..$endLine] | ForEach-Object { $_.Trim() }) -join "`n"
            $snippet = Redact-Secrets $snippet
            
            # Determine category based on detectors
            $category = "Other"
            if ($matchedDetectors -match "FEMA|PAPPG|Public Assistance|EHP|DRRA") { $category = "FEMA/Compliance" }
            elseif ($matchedDetectors -match "schema|model|interface|DDL") { $category = "Data Models" }
            elseif ($matchedDetectors -match "JWT|OAuth|auth|rbac") { $category = "Authentication/Security" }
            elseif ($matchedDetectors -match "state machine|workflow|wizard") { $category = "Wizard/Workflow" }
            elseif ($FilePath -match "frontend|ui|component") { $category = "Frontend/UI" }
            elseif ($FilePath -match "backend|api|server") { $category = "Backend Logic" }
            elseif ($FilePath -match "config|yaml|json") { $category = "Config/Data Assets" }
            elseif ($FilePath -match "\.md$|\.txt$|README") { $category = "Docs/Guides" }
            
            # Generate minimum 3 tags (role + domain + format/tech)
            $tags = @()
            
            # Role tag
            if ($FilePath -match "frontend|ui|component") { $tags += "frontend" }
            elseif ($FilePath -match "backend|api|server") { $tags += "backend" }
            else { $tags += "core" }
            
            # Domain tag
            if ($matchedDetectors -match "FEMA|PAPPG|Public Assistance") { $tags += "fema-pa" }
            elseif ($matchedDetectors -match "BCA|benefit.cost") { $tags += "bca" }
            elseif ($matchedDetectors -match "EHP|environmental") { $tags += "ehp" }
            elseif ($matchedDetectors -match "remediation") { $tags += "remediation" }
            else { $tags += "policy" }
            
            # Format/tech tag
            $fileExt = [System.IO.Path]::GetExtension($FilePath).ToLower().TrimStart('.')
            if ($fileExt) { $tags += $fileExt }
            else { $tags += "text" }
            
            # Ensure minimum 3 tags
            while ($tags.Count -lt 3) {
                $tags += "general"
            }
            
            # Create nugget with exact schema
            $nugget = @{
                path = $FilePath
                sha1 = $FileHash
                category = $category
                tags = $tags
                lines = @($startLine + 1, $endLine + 1)
                snippet = $snippet
                reason = "Contains domain-relevant patterns: $($matchedDetectors -join ', ')"
                detectors = $matchedDetectors
                risk_level = "medium"
                action = "review-security"
                has_more_in_file = $false
                discovery_mode = "manifest-scan"
                ts_utc = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                sig = [System.Security.Cryptography.SHA1]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes("$FilePath$($startLine + 1)$($endLine + 1)$snippet")) | ForEach-Object { $_.ToString("x2") } | Join-String
            }
            
            $nuggets += $nugget
        }
    }
    
    return $nuggets, $detectedPatterns
}

# Zero-hit criteria function - exact specification
function Test-ZeroHitCriteria {
    param(
        [string]$FilePath,
        [string[]]$Lines
    )
    
    # Check if name/path suggests domain relevance
    $domainRelevantPath = $FilePath -match "wizards?|rules?|auth|schema|models?|configs?|mappings?|policy|pappg|drra|elig|checklist"
    
    # Check if docs ≥ 500 words with ≥ 3 policy terms
    $wordCount = ($Lines -join " ").Split(" ", [StringSplitOptions]::RemoveEmptyEntries).Count
    $policyTermCount = 0
    $policyTerms = @("FEMA", "PAPPG", "Category", "DRRA", "policy", "compliance", "regulation", "procurement", "assistance", "mitigation")
    
    foreach ($line in $Lines) {
        foreach ($term in $policyTerms) {
            if ($line -match $term) {
                $policyTermCount++
                break
            }
        }
    }
    
    return $domainRelevantPath -or ($wordCount -ge 500 -and $policyTermCount -ge 3)
}

# Load manifest - exact specification
Write-Progress-Log "Loading manifest from $ManifestPath"
$manifestContent = Get-Content $ManifestPath -Encoding UTF8
$manifestRows = @()

for ($i = 1; $i -lt $manifestContent.Count; $i++) {  # Skip header
    $line = $manifestContent[$i]
    if ($line -match '^"([^"]+)",(\d+),(true|false),"([^"]*)"') {
        $manifestRows += @{
            path = $matches[1]
            bytes = [int]$matches[2]
            is_binary = $matches[3] -eq "true"
            sha1 = $matches[4]
            index = $i - 1
        }
    }
}

$totalFiles = $manifestRows.Count
Write-Progress-Log "Loaded manifest: $totalFiles files to process"

# Check for resume state
$startIndex = 0
$processedCount = 0
$totalNuggets = 0

if (Test-Path $StateFile) {
    try {
        $state = Get-Content $StateFile -Raw | ConvertFrom-Json
        $startIndex = $state.manifest_index
        $processedCount = $state.processed_count
        $totalNuggets = $state.total_nuggets
        Write-Progress-Log "Resuming from index $startIndex (processed: $processedCount, nuggets: $totalNuggets)"
    }
    catch {
        Write-Progress-Log "Could not parse state file, starting fresh"
    }
}

# Initialize output files if starting fresh
if ($startIndex -eq 0) {
    "" | Out-File -FilePath $DeepNuggetsFile -Encoding UTF8
    "# Deep Nuggets Report`n`nGenerated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss UTC')`n" | Out-File -FilePath $NuggetsReportFile -Encoding UTF8
    "# Zero Hit Report`n`nFiles with 0 nuggets but domain relevance:`n" | Out-File -FilePath $ZeroHitReportFile -Encoding UTF8
    "# Detector Expansion`n`nNew domain tokens/phrases discovered during scan:`n" | Out-File -FilePath $DetectorExpansionFile -Encoding UTF8
}

# Process files in strict lexicographic order
$coverage = @()
$zeroHits = @()
$newDetectors = @()
$lastCheckpoint = Get-Date

Write-Progress-Log "Starting file processing from index $startIndex"

for ($i = $startIndex; $i -lt $manifestRows.Count; $i++) {
    $fileInfo = $manifestRows[$i]
    $absolutePath = Join-Path $SourceDir $fileInfo.path
    $extendedPath = Get-ExtendedPath $absolutePath

    # Create coverage entry with exact schema
    $coverageEntry = @{
        path = $fileInfo.path
        sha1 = $fileInfo.sha1
        bytes = $fileInfo.bytes
        is_binary = $fileInfo.is_binary
        scanned_lines = 0
        nuggets = 0
        has_more_in_file = $false
        first_seen_utc = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        last_scan_utc = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        error = $null
    }

    try {
        if ($fileInfo.is_binary) {
            # Binary file - record coverage only, no parsing
            $coverageEntry.scanned_lines = 0
            $coverageEntry.nuggets = 0
        }
        else {
            # Text file - read ALL lines and extract nuggets
            $retryCount = 0
            $maxRetries = 15
            $fileProcessed = $false

            while (-not $fileProcessed -and $retryCount -lt $maxRetries) {
                try {
                    $lines = Get-Content $extendedPath -Encoding UTF8 -ErrorAction Stop
                    $coverageEntry.scanned_lines = $lines.Count

                    $nuggets, $detectedPatterns = Find-Nuggets $lines $fileInfo.path $fileInfo.sha1
                    $coverageEntry.nuggets = $nuggets.Count
                    $totalNuggets += $nuggets.Count

                    # Write nuggets to JSONL - append-only
                    foreach ($nugget in $nuggets) {
                        $nuggetJson = $nugget | ConvertTo-Json -Compress
                        Add-Content -Path $DeepNuggetsFile -Value $nuggetJson -Encoding UTF8

                        # Add to human-readable report
                        $reportEntry = "## $($nugget.path) (Lines $($nugget.lines[0])-$($nugget.lines[1]))`n"
                        $reportEntry += "**Category:** $($nugget.category)`n"
                        $reportEntry += "**Tags:** $($nugget.tags -join ', ')`n"
                        $reportEntry += "**Risk Level:** $($nugget.risk_level)`n"
                        $reportEntry += "**Action:** $($nugget.action)`n"
                        $reportEntry += "**Reason:** $($nugget.reason)`n"
                        $reportEntry += "**Detectors:** $($nugget.detectors -join ', ')`n"
                        $reportEntry += "````n$($nugget.snippet)`n```n`n"
                        Add-Content -Path $NuggetsReportFile -Value $reportEntry -Encoding UTF8
                    }

                    # Check zero-hit criteria
                    if ($nuggets.Count -eq 0 -and (Test-ZeroHitCriteria $fileInfo.path $lines)) {
                        $detectorsTriedList = "FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra, Expanded patterns"
                        $hypothesis = "Domain-relevant file with no current pattern matches - may need detector expansion"
                        $zeroHitEntry = "- **$($fileInfo.path)** ($($lines.Count) lines) - Detectors tried: $detectorsTriedList - Hypothesis: $hypothesis"
                        Add-Content -Path $ZeroHitReportFile -Value $zeroHitEntry -Encoding UTF8
                        $zeroHits += $fileInfo.path
                    }

                    # Track new detector patterns for expansion
                    $newDetectors += $detectedPatterns

                    $fileProcessed = $true
                }
                catch {
                    $retryCount++
                    if ($retryCount -lt $maxRetries) {
                        Start-Sleep -Milliseconds (100 * $retryCount)  # Exponential backoff
                    } else {
                        $coverageEntry.error = "locked"
                        $fileProcessed = $true
                    }
                }
            }
        }
    }
    catch {
        $coverageEntry.error = $_.Exception.Message
    }

    $coverage += $coverageEntry
    $processedCount++

    # Checkpoint every ~100 files or ~2 minutes
    $currentTime = Get-Date
    if ($processedCount % 100 -eq 0 -or ($currentTime - $lastCheckpoint).TotalMinutes -ge 2) {
        $state = @{
            manifest_index = $i + 1
            last_path = $fileInfo.path
            processed_count = $processedCount
            total_nuggets = $totalNuggets
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        }
        $state | ConvertTo-Json | Out-File -FilePath $StateFile -Encoding UTF8
        Write-Progress-Log "Checkpoint: processed=$processedCount nuggets=$totalNuggets last_path=$($fileInfo.path) last_sha1=$($fileInfo.sha1)"
        $lastCheckpoint = $currentTime
    }
}

Write-Progress-Log "File processing completed: processed=$processedCount nuggets=$totalNuggets"

# Write detector expansion report
if ($newDetectors.Count -gt 0) {
    $uniqueDetectors = $newDetectors | Sort-Object -Unique
    $expansionContent = "## New Patterns Discovered`n`n"
    $expansionContent += "Total unique patterns found: $($uniqueDetectors.Count)`n`n"
    foreach ($detector in $uniqueDetectors) {
        $expansionContent += "- $detector`n"
    }
    Add-Content -Path $DetectorExpansionFile -Value $expansionContent -Encoding UTF8
}

# Write final coverage file - atomic operation
$tempCoverageFile = "$CoverageFile.tmp"
$coverage | ConvertTo-Json | Out-File -FilePath $tempCoverageFile -Encoding UTF8
Move-Item $tempCoverageFile $CoverageFile -Force

# HARD GATES validation
$manifestCount = $totalFiles
$coverageCount = $coverage.Count

if ($coverageCount -ne $manifestCount) {
    Write-Progress-Log "ABORT_MISMATCH manifest=$manifestCount coverage=$coverageCount"

    # Find up to 10 missing paths
    $manifestPaths = $manifestRows | ForEach-Object { $_.path }
    $coveragePaths = $coverage | ForEach-Object { $_.path }
    $missing = Compare-Object $manifestPaths $coveragePaths | Select-Object -First 10

    if ($missing) {
        Write-Progress-Log "Sample missing entries:"
        $missing | ForEach-Object {
            Write-Progress-Log "  $($_.SideIndicator) $($_.InputObject)"
        }
    }

    exit 1
} else {
    Write-Progress-Log "COMPLETED coverage=$coverageCount manifest=$manifestCount"

    # Generate one-page summary
    $uniqueDetectors = $newDetectors | Sort-Object -Unique
    $summary = @"
# Phase 1D Content Scan Summary

**Completion Status:** COMPLETED
**Files Processed:** $processedCount / $manifestCount
**Nuggets Extracted:** $totalNuggets
**Zero Hits Identified:** $($zeroHits.Count)
**Detector Expansion:** $($uniqueDetectors.Count) unique patterns discovered

## Output Files Generated:
- deep_nuggets.jsonl ($totalNuggets entries)
- deep_nuggets_report.md (human-readable format)
- full_scan_coverage.json ($coverageCount entries)
- zero_hit_report.md ($($zeroHits.Count) domain-relevant files)
- detector_expansion.md ($($uniqueDetectors.Count) new patterns)

## Validation Results:
**HARD GATES PASSED:** Coverage entries ($coverageCount) = Manifest entries ($manifestCount)

## Detector Expansion Highlights:
$($uniqueDetectors | Select-Object -First 10 | ForEach-Object { "- $_" } | Out-String)

**Phase 1D content scanning completed successfully with perfect parity validation.**
"@

    Write-Host $summary
    Add-Content -Path $ProgressFile -Value $summary -Encoding UTF8

    # Clean up state file on successful completion
    if (Test-Path $StateFile) {
        Remove-Item $StateFile -Force
    }

    exit 0
}
