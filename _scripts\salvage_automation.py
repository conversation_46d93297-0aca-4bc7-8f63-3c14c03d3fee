import os
import shutil
import hashlib
import csv
from pathlib import Path
from datetime import datetime
import json

class SalvageAutomation:
    def __init__(self):
        self.source_dir = Path("C:/Users/<USER>/Documents/repo analysis 202508/ALL NEW APP")
        self.salvage_base = Path("C:/Users/<USER>/Documents/SalvageControlSystem/ALL_NEW_APP")
        self.salvage_assets = self.salvage_base / "salvage_assets"
        self.inventory_file = self.salvage_base / "file_inventory.csv"
        self.min_size_mb = 25
        
        # Stats tracking
        self.files_moved = 0
        self.duplicates_deleted = 0
        self.total_salvage_size = 0
        self.moved_files = []
        
    def ensure_directories(self):
        """Create necessary directories"""
        self.salvage_assets.mkdir(parents=True, exist_ok=True)
        (self.salvage_base / "docs").mkdir(parents=True, exist_ok=True)
        
    def get_file_hash(self, filepath):
        """Calculate MD5 hash for duplicate detection"""
        hash_md5 = hashlib.md5()
        try:
            with open(filepath, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except:
            return None
            
    def scan_large_files(self):
        """Scan for files ≥25MB"""
        large_files = []
        file_hashes = {}
        
        print(f"Scanning {self.source_dir} for files ≥{self.min_size_mb}MB...")
        
        for root, dirs, files in os.walk(self.source_dir):
            for file in files:
                filepath = Path(root) / file
                try:
                    size_bytes = filepath.stat().st_size
                    size_mb = size_bytes / (1024 * 1024)
                    
                    if size_mb >= self.min_size_mb:
                        file_hash = self.get_file_hash(filepath)
                        
                        file_info = {
                            'path': filepath,
                            'relative_path': filepath.relative_to(self.source_dir),
                            'size_mb': round(size_mb, 1),
                            'extension': filepath.suffix.lower(),
                            'hash': file_hash
                        }
                        
                        # Check for duplicates
                        if file_hash and file_hash in file_hashes:
                            print(f"Duplicate found: {filepath}")
                            file_info['is_duplicate'] = True
                        else:
                            file_info['is_duplicate'] = False
                            if file_hash:
                                file_hashes[file_hash] = filepath
                                
                        large_files.append(file_info)
                        
                except (OSError, PermissionError) as e:
                    print(f"Error accessing {filepath}: {e}")
                    
        return large_files
        
    def move_files(self, large_files):
        """Move files to salvage directory"""
        print(f"Moving {len(large_files)} large files...")
        
        for file_info in large_files:
            if file_info['is_duplicate']:
                # Delete duplicate
                try:
                    file_info['path'].unlink()
                    self.duplicates_deleted += 1
                    print(f"Deleted duplicate: {file_info['relative_path']}")
                except Exception as e:
                    print(f"Error deleting {file_info['path']}: {e}")
            else:
                # Move to salvage
                dest_path = self.salvage_assets / file_info['relative_path']
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                
                try:
                    shutil.move(str(file_info['path']), str(dest_path))
                    self.files_moved += 1
                    self.total_salvage_size += file_info['size_mb']
                    
                    self.moved_files.append({
                        'original_path': str(file_info['relative_path']),
                        'new_path': f"salvage_assets/{file_info['relative_path']}",
                        'ext': file_info['extension'],
                        'size_MB': file_info['size_mb'],
                        'note': self.get_file_note(file_info['extension'])
                    })
                    
                    print(f"Moved: {file_info['relative_path']} ({file_info['size_mb']}MB)")
                    
                except Exception as e:
                    print(f"Error moving {file_info['path']}: {e}")
                    
    def get_file_note(self, extension):
        """Generate descriptive note based on file extension"""
        notes = {
            '.pack': 'Next.js Webpack output',
            '.zip': 'Archive file',
            '.node': 'Native Node.js module',
            '.wasm': 'WebAssembly binary',
            '.tar': 'Archive file',
            '.gz': 'Compressed file',
            '.rar': 'Archive file',
            '.exe': 'Executable file',
            '.dll': 'Dynamic library',
            '.so': 'Shared library'
        }
        return notes.get(extension, 'Large binary file')
        
    def write_inventory(self):
        """Write CSV inventory of moved files"""
        with open(self.inventory_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['original_path', 'new_path', 'ext', 'size_MB', 'note']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(self.moved_files)
            
    def create_readme(self):
        """Create README.md in salvage directory"""
        readme_content = f"""# ALL NEW APP – Salvage Summary

## Operation Summary
- **Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Source**: C:/Users/<USER>/Documents/repo analysis 202508/ALL NEW APP/
- **Destination**: C:/Users/<USER>/Documents/SalvageControlSystem/ALL_NEW_APP/salvage_assets/

## Results
- **Files moved**: {self.files_moved}
- **Duplicates deleted**: {self.duplicates_deleted}
- **Total salvage size**: {self.total_salvage_size:.1f} MB
- **Threshold**: Files ≥{self.min_size_mb}MB

## File Types Processed
{self.get_file_type_summary()}

## Next Steps
1. Review `file_inventory.csv` for complete file listing
2. Source directory is now ready for Git operations
3. Large assets are preserved in salvage archive
"""
        
        readme_path = self.salvage_base / "README.md"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
            
    def get_file_type_summary(self):
        """Generate summary of file types moved"""
        ext_counts = {}
        for file_info in self.moved_files:
            ext = file_info['ext'] or 'no extension'
            ext_counts[ext] = ext_counts.get(ext, 0) + 1
            
        summary = []
        for ext, count in sorted(ext_counts.items()):
            summary.append(f"- **{ext}**: {count} files")
            
        return '\n'.join(summary) if summary else "- No files moved"
        
    def update_rebuild_log(self):
        """Create or append to REBUILD_LOG.md"""
        log_path = self.salvage_base / "docs" / "REBUILD_LOG.md"
        
        log_entry = f"""
### Salvage Audit – Phase 1 (ALL NEW APP)
**Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Files ≥{self.min_size_mb}MB were moved from:
C:/Users/<USER>/Documents/repo analysis 202508/ALL NEW APP/

They were relocated to:
C:/Users/<USER>/Documents/SalvageControlSystem/ALL_NEW_APP/salvage_assets/

**Results**:
- Files moved: {self.files_moved}
- Duplicates deleted: {self.duplicates_deleted}
- Total size: {self.total_salvage_size:.1f} MB

Duplicates were deleted. All Git-hostile and compiled artifacts are now staged for future release.

"""
        
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(log_entry)
            
    def update_gitignore(self):
        """Update .gitignore in source directory"""
        gitignore_path = self.source_dir / ".gitignore"
        
        gitignore_additions = """
# Large files and build artifacts
*.zip
*.rar
*.tar
*.gz
*.wasm
*.node
*.pack
.next/
build/
output/
node_modules/
dist/
*.log
core
*.prn
*.docx
*.exe
*.dll
*.so
"""
        
        if gitignore_path.exists():
            with open(gitignore_path, 'a', encoding='utf-8') as f:
                f.write(gitignore_additions)
        else:
            with open(gitignore_path, 'w', encoding='utf-8') as f:
                f.write(gitignore_additions.strip())
                
    def run(self):
        """Execute the complete salvage operation"""
        print("🚀 Starting ALL NEW APP Salvage Operation...")
        
        # Step 1: Ensure directories exist
        self.ensure_directories()
        
        # Step 2: Scan for large files
        large_files = self.scan_large_files()
        print(f"Found {len(large_files)} files ≥{self.min_size_mb}MB")
        
        # Step 3: Move files and delete duplicates
        self.move_files(large_files)
        
        # Step 4: Write inventory
        self.write_inventory()
        print(f"Inventory written to: {self.inventory_file}")
        
        # Step 5: Create documentation
        self.create_readme()
        self.update_rebuild_log()
        
        # Step 6: Update .gitignore
        self.update_gitignore()
        
        print(f"""
✅ Salvage Operation Complete!

📊 Summary:
- Files moved: {self.files_moved}
- Duplicates deleted: {self.duplicates_deleted}
- Total salvage size: {self.total_salvage_size:.1f} MB

📁 Files created:
- {self.inventory_file}
- {self.salvage_base}/README.md
- {self.salvage_base}/docs/REBUILD_LOG.md
- {self.source_dir}/.gitignore (updated)

The source directory is now ready for Git operations!
""")

if __name__ == "__main__":
    salvage = SalvageAutomation()
    salvage.run()
