#!/usr/bin/env python3
import csv
import json
import os
import re
import hashlib
from datetime import datetime
import sys

# Paths
SOURCE_DIR = r"C:\Users\<USER>\Documents\repo analysis 202508\PA-CHECK-MM"
OUTPUT_DIR = r"C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d"
MANIFEST_PATH = os.path.join(OUTPUT_DIR, "full_scan_manifest.csv")

# Output files
DEEP_NUGGETS_FILE = os.path.join(OUTPUT_DIR, "deep_nuggets.jsonl")
NUGGETS_REPORT_FILE = os.path.join(OUTPUT_DIR, "deep_nuggets_report.md")
COVERAGE_FILE = os.path.join(OUTPUT_DIR, "full_scan_coverage.json")
PROGRESS_FILE = os.path.join(OUTPUT_DIR, "full_scan_progress.log")
DETECTOR_EXPANSION_FILE = os.path.join(OUTPUT_DIR, "detector_expansion.md")
ZERO_HIT_REPORT_FILE = os.path.join(OUTPUT_DIR, "zero_hit_report.md")
STATE_FILE = os.path.join(OUTPUT_DIR, "state.json")

# EXACT DETECTOR PATTERNS AS SPECIFIED
FEMA_PATTERNS = [
    r"PAPPG", r"Public Assistance", r"Individual Assistance", r"FEMA", 
    r"Category[\s-]?[A-G]\b", r"DRRA", r"1235b", r"1206", r"\bHMGP\b", 
    r"\bHMA\b", r"44\s*CFR", r"2\s*CFR\s*200", r"procurement", 
    r"time[- ]and[- ]materials", r"mutual aid", r"force account", 
    r"small project", r"large project", r"damage inventory", 
    r"site worksheet", r"hazard mitigation", r"EHP", r"Davis[- ]Bacon", r"BABA"
]

DATA_PATTERNS = [
    r"schema", r"model", r"DDL", r"Prisma", r"CREATE TABLE", r"OpenAPI", 
    r"Swagger", r"interface", r"zod", r"pydantic", r"rules?", r"checklist", 
    r"mapping", r"decision table", r"state machine", r"evaluation", 
    r"parser", r"validator"
]

AUTH_PATTERNS = [
    r"JWT", r"JWE", r"OAuth", r"HMAC", r"signature", r"roles", r"permissions", 
    r"rbac", r"abac", r"rate limit", r"retry", r"circuit breaker", 
    r"secrets?:", r"kms", r"vault", r"bcrypt", r"argon2", r"crypto"
]

def write_progress_log(message):
    timestamp = datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
    log_entry = f"{timestamp} - {message}"
    with open(PROGRESS_FILE, "a", encoding="utf-8") as f:
        f.write(log_entry + "\n")
    print(log_entry)

def redact_secrets(text):
    text = re.sub(r'(?i)(password|secret|key|token)\s*[:=]\s*\S+', r'\1=***REDACTED***', text)
    return text

def find_nuggets(lines, file_path, file_hash):
    nuggets = []
    detected_patterns = []
    
    for i, line in enumerate(lines):
        matched_detectors = []
        
        # Check FEMA patterns
        for pattern in FEMA_PATTERNS:
            if re.search(pattern, line, re.IGNORECASE):
                matched_detectors.append(f"regex:{pattern}")
                detected_patterns.append(pattern)
        
        # Check Data patterns
        for pattern in DATA_PATTERNS:
            if re.search(pattern, line, re.IGNORECASE):
                matched_detectors.append(f"regex:{pattern}")
                detected_patterns.append(pattern)
        
        # Check Auth patterns
        for pattern in AUTH_PATTERNS:
            if re.search(pattern, line, re.IGNORECASE):
                matched_detectors.append(f"regex:{pattern}")
                detected_patterns.append(pattern)
        
        if matched_detectors:
            start_line = max(0, i - 1)
            end_line = min(len(lines) - 1, i + 10)
            
            # Ensure 3-12 line range
            if (end_line - start_line + 1) < 3:
                end_line = min(len(lines) - 1, start_line + 2)
            if (end_line - start_line + 1) > 12:
                end_line = start_line + 11
            
            snippet = "\n".join(lines[start_line:end_line + 1]).strip()
            snippet = redact_secrets(snippet)
            
            # Determine category
            category = "Other"
            if any("FEMA" in d or "PAPPG" in d or "Public Assistance" in d or "EHP" in d or "DRRA" in d for d in matched_detectors):
                category = "FEMA/Compliance"
            elif any("schema" in d or "model" in d or "interface" in d or "DDL" in d for d in matched_detectors):
                category = "Data Models"
            elif any("JWT" in d or "OAuth" in d or "auth" in d or "rbac" in d for d in matched_detectors):
                category = "Authentication/Security"
            elif any("state machine" in d or "workflow" in d or "wizard" in d for d in matched_detectors):
                category = "Wizard/Workflow"
            elif re.search(r"frontend|ui|component", file_path, re.IGNORECASE):
                category = "Frontend/UI"
            elif re.search(r"backend|api|server", file_path, re.IGNORECASE):
                category = "Backend Logic"
            elif re.search(r"config|yaml|json", file_path, re.IGNORECASE):
                category = "Config/Data Assets"
            elif re.search(r"\.md$|\.txt$|README", file_path, re.IGNORECASE):
                category = "Docs/Guides"
            
            # Generate tags (minimum 3)
            tags = []
            if re.search(r"frontend|ui|component", file_path, re.IGNORECASE):
                tags.append("frontend")
            elif re.search(r"backend|api|server", file_path, re.IGNORECASE):
                tags.append("backend")
            else:
                tags.append("core")
            
            if any("FEMA" in d or "PAPPG" in d or "Public Assistance" in d for d in matched_detectors):
                tags.append("fema-pa")
            elif any("BCA" in d or "benefit" in d for d in matched_detectors):
                tags.append("bca")
            elif any("EHP" in d or "environmental" in d for d in matched_detectors):
                tags.append("ehp")
            else:
                tags.append("policy")
            
            file_ext = os.path.splitext(file_path)[1].lower().lstrip('.')
            if file_ext:
                tags.append(file_ext)
            else:
                tags.append("text")
            
            while len(tags) < 3:
                tags.append("general")
            
            # Create nugget with exact schema
            sig_input = f"{file_path}{start_line + 1}{end_line + 1}{snippet}"
            sig = hashlib.sha1(sig_input.encode('utf-8')).hexdigest()
            
            nugget = {
                "path": file_path,
                "sha1": file_hash,
                "category": category,
                "tags": tags,
                "lines": [start_line + 1, end_line + 1],
                "snippet": snippet,
                "reason": f"Contains domain-relevant patterns: {', '.join(matched_detectors)}",
                "detectors": matched_detectors,
                "risk_level": "medium",
                "action": "review-security",
                "has_more_in_file": False,
                "discovery_mode": "manifest-scan",
                "ts_utc": datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "sig": sig
            }
            
            nuggets.append(nugget)
    
    return nuggets, detected_patterns

def test_zero_hit_criteria(file_path, lines):
    domain_relevant = re.search(r"wizards?|rules?|auth|schema|models?|configs?|mappings?|policy|pappg|drra|elig|checklist", file_path, re.IGNORECASE)
    if domain_relevant:
        return True
    
    word_count = len(" ".join(lines).split())
    if word_count < 500:
        return False
    
    policy_term_count = 0
    for line in lines:
        if re.search(r"FEMA|PAPPG|Category|DRRA|policy|compliance|regulation|procurement|assistance|mitigation", line, re.IGNORECASE):
            policy_term_count += 1
            if policy_term_count >= 3:
                return True
    
    return False

def main():
    write_progress_log("Starting Phase 1D content scan - Python implementation")
    
    # Load manifest
    write_progress_log(f"Loading manifest from {MANIFEST_PATH}")
    manifest_rows = []
    
    with open(MANIFEST_PATH, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        header = next(reader)  # Skip header
        for row in reader:
            if len(row) >= 4:
                manifest_rows.append({
                    'path': row[0],
                    'bytes': int(row[1]),
                    'is_binary': row[2] == 'true',
                    'sha1': row[3]
                })
    
    total_files = len(manifest_rows)
    write_progress_log(f"Loaded manifest: {total_files} files to process")
    
    # Resume state
    start_index = 0
    processed_count = 0
    total_nuggets = 0
    
    if os.path.exists(STATE_FILE):
        try:
            with open(STATE_FILE, 'r', encoding='utf-8') as f:
                state = json.load(f)
                start_index = state.get('manifest_index', 0)
                processed_count = state.get('processed_count', 0)
                total_nuggets = state.get('total_nuggets', 0)
                write_progress_log(f"Resuming from index {start_index} (processed: {processed_count}, nuggets: {total_nuggets})")
        except:
            write_progress_log("Starting fresh")
    
    # Initialize output files
    if start_index == 0:
        with open(DEEP_NUGGETS_FILE, 'w', encoding='utf-8') as f:
            pass  # Create empty file
        with open(NUGGETS_REPORT_FILE, 'w', encoding='utf-8') as f:
            f.write(f"# Deep Nuggets Report\n\nGenerated: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}\n\n")
        with open(ZERO_HIT_REPORT_FILE, 'w', encoding='utf-8') as f:
            f.write("# Zero Hit Report\n\nFiles with 0 nuggets but domain relevance:\n\n")
        with open(DETECTOR_EXPANSION_FILE, 'w', encoding='utf-8') as f:
            f.write("# Detector Expansion\n\nNew domain tokens/phrases discovered during scan:\n\n")
    
    # Process files
    coverage = []
    zero_hits = []
    new_detectors = []
    
    write_progress_log(f"Starting file processing from index {start_index}")
    
    for i in range(start_index, len(manifest_rows)):
        file_info = manifest_rows[i]
        absolute_path = os.path.join(SOURCE_DIR, file_info['path'])
        
        # Create coverage entry
        coverage_entry = {
            "path": file_info['path'],
            "sha1": file_info['sha1'],
            "bytes": file_info['bytes'],
            "is_binary": file_info['is_binary'],
            "scanned_lines": 0,
            "nuggets": 0,
            "has_more_in_file": False,
            "first_seen_utc": datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            "last_scan_utc": datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            "error": None
        }
        
        try:
            if file_info['is_binary']:
                coverage_entry['scanned_lines'] = 0
                coverage_entry['nuggets'] = 0
            else:
                # Text file - read and analyze
                try:
                    with open(absolute_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                    
                    coverage_entry['scanned_lines'] = len(lines)
                    
                    nuggets, detected_patterns = find_nuggets(lines, file_info['path'], file_info['sha1'])
                    coverage_entry['nuggets'] = len(nuggets)
                    total_nuggets += len(nuggets)
                    
                    # Write nuggets immediately
                    for nugget in nuggets:
                        with open(DEEP_NUGGETS_FILE, 'a', encoding='utf-8') as f:
                            f.write(json.dumps(nugget, ensure_ascii=False) + '\n')
                        
                        with open(NUGGETS_REPORT_FILE, 'a', encoding='utf-8') as f:
                            f.write(f"## {nugget['path']} (Lines {nugget['lines'][0]}-{nugget['lines'][1]})\n")
                            f.write(f"**Category:** {nugget['category']}\n")
                            f.write(f"**Tags:** {', '.join(nugget['tags'])}\n")
                            f.write(f"**Reason:** {nugget['reason']}\n")
                            f.write(f"```\n{nugget['snippet']}\n```\n\n")
                    
                    # Check zero hits
                    if len(nuggets) == 0 and test_zero_hit_criteria(file_info['path'], lines):
                        zero_hit_entry = f"- **{file_info['path']}** ({len(lines)} lines) - Detectors tried: FEMA/Policy, Data/Schema/Rules, Auth/Security/Infra - Hypothesis: Domain-relevant file with no current pattern matches\n"
                        with open(ZERO_HIT_REPORT_FILE, 'a', encoding='utf-8') as f:
                            f.write(zero_hit_entry)
                        zero_hits.append(file_info['path'])
                    
                    new_detectors.extend(detected_patterns)
                    
                except Exception as e:
                    coverage_entry['error'] = "read_error"
        
        except Exception as e:
            coverage_entry['error'] = str(e)
        
        coverage.append(coverage_entry)
        processed_count += 1
        
        # Checkpoint every 100 files
        if processed_count % 100 == 0:
            state = {
                'manifest_index': i + 1,
                'last_path': file_info['path'],
                'processed_count': processed_count,
                'total_nuggets': total_nuggets,
                'timestamp': datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
            }
            with open(STATE_FILE, 'w', encoding='utf-8') as f:
                json.dump(state, f)
            write_progress_log(f"Checkpoint: processed={processed_count} nuggets={total_nuggets} last_path={file_info['path']} last_sha1={file_info['sha1']}")
    
    write_progress_log(f"File processing completed: processed={processed_count} nuggets={total_nuggets}")
    
    # Write detector expansion
    if new_detectors:
        unique_detectors = sorted(set(new_detectors))
        with open(DETECTOR_EXPANSION_FILE, 'a', encoding='utf-8') as f:
            f.write(f"## New Patterns Discovered\n\nTotal: {len(unique_detectors)}\n\n")
            for detector in unique_detectors:
                f.write(f"- {detector}\n")
    
    # Write final coverage
    with open(COVERAGE_FILE, 'w', encoding='utf-8') as f:
        json.dump(coverage, f, ensure_ascii=False, indent=2)
    
    # HARD GATES validation
    manifest_count = total_files
    coverage_count = len(coverage)
    
    if coverage_count != manifest_count:
        write_progress_log(f"ABORT_MISMATCH manifest={manifest_count} coverage={coverage_count}")
        sys.exit(1)
    else:
        write_progress_log(f"COMPLETED coverage={coverage_count} manifest={manifest_count}")
        
        unique_detectors = sorted(set(new_detectors))
        summary = f"""# Phase 1D Content Scan Summary

**Completion Status:** COMPLETED
**Files Processed:** {processed_count} / {manifest_count}
**Nuggets Extracted:** {total_nuggets}
**Zero Hits Identified:** {len(zero_hits)}
**Detector Expansion:** {len(unique_detectors)} unique patterns

## Output Files Generated:
- deep_nuggets.jsonl ({total_nuggets} entries)
- deep_nuggets_report.md (human-readable)
- full_scan_coverage.json ({coverage_count} entries)
- zero_hit_report.md ({len(zero_hits)} entries)
- detector_expansion.md ({len(unique_detectors)} patterns)

## Validation Results:
**HARD GATES PASSED:** Coverage ({coverage_count}) = Manifest ({manifest_count})

**Phase 1D content scanning completed successfully.**
"""
        
        print(summary)
        with open(PROGRESS_FILE, 'a', encoding='utf-8') as f:
            f.write(summary + '\n')
        
        # Clean up state file
        if os.path.exists(STATE_FILE):
            os.remove(STATE_FILE)

if __name__ == "__main__":
    main()
