#!/usr/bin/env python3
"""
Validate and clean Phase 1D manifest to remove missing files
"""

import os
import sys
import csv
import json
from datetime import datetime
from typing import Dict, List, Any
from collections import defaultdict

# Configuration
SOURCE_ROOT = r"C:\Users\<USER>\Documents\SalvageControlSystem"
MANIFEST_PATH = r"C:\Users\<USER>\Documents\repo analysis 202508\SCANS_OUT\SalvageControlSystem\phase1d_r2\full_scan_manifest.csv"
REPORTS_DIR = "reports_local"

# Output files
CLEANED_MANIFEST = os.path.join(REPORTS_DIR, "phase1d_manifest.cleaned.csv")
MISSING_FILES = os.path.join(REPORTS_DIR, "missing_in_manifest.csv")
STATS_MD = os.path.join(REPORTS_DIR, "phase1d_manifest.stats.md")
PROGRESS_LOG = os.path.join(REPORTS_DIR, "scan_progress_local.log")

def now_ts() -> str:
    """Return current timestamp"""
    return datetime.now().isoformat(timespec='seconds')

def ensure_reports_dir():
    """Ensure reports directory exists"""
    os.makedirs(REPORTS_DIR, exist_ok=True)

def append_log(message: str):
    """Append message to progress log"""
    ensure_reports_dir()
    with open(PROGRESS_LOG, 'a', encoding='utf-8') as f:
        f.write(f"{now_ts()} - {message}\n")

def get_file_info(file_path: str) -> Dict[str, Any]:
    """Get file information if it exists"""
    try:
        if os.path.exists(file_path):
            stat = os.stat(file_path)
            return {
                'exists': True,
                'size': stat.st_size,
                'mtime': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'ext': os.path.splitext(file_path)[1].lower()
            }
        else:
            return {
                'exists': False,
                'size': None,
                'mtime': None,
                'ext': os.path.splitext(file_path)[1].lower()
            }
    except Exception as e:
        return {
            'exists': False,
            'size': None,
            'mtime': None,
            'ext': os.path.splitext(file_path)[1].lower(),
            'error': str(e)
        }

def load_manifest() -> List[Dict[str, str]]:
    """Load the current manifest"""
    if not os.path.exists(MANIFEST_PATH):
        raise FileNotFoundError(f"Manifest not found: {MANIFEST_PATH}")
    
    rows = []
    with open(MANIFEST_PATH, 'r', encoding='utf-8', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            rows.append(row)
    
    return rows

def validate_and_clean_manifest():
    """Main validation and cleaning function"""
    print(f"Starting manifest validation at {now_ts()}")
    append_log("Starting Phase 1D manifest validation and cleaning")
    
    # Load manifest
    manifest_rows = load_manifest()
    total_rows = len(manifest_rows)
    append_log(f"Loaded manifest with {total_rows} rows")
    
    # Process each row
    existing_rows = []
    missing_rows = []
    stats = {
        'total_rows': total_rows,
        'existing_count': 0,
        'missing_count': 0,
        'total_bytes': 0,
        'ext_counts': defaultdict(int),
        'ext_bytes': defaultdict(int),
        'errors': []
    }
    
    print(f"Processing {total_rows} manifest entries...")
    
    for i, row in enumerate(manifest_rows):
        if i % 100 == 0:
            print(f"  Progress: {i}/{total_rows} ({i/total_rows*100:.1f}%)")
        
        rel_path = row.get('path', '')
        if not rel_path:
            continue
        
        # Convert to absolute path
        abs_path = os.path.join(SOURCE_ROOT, rel_path.replace('/', os.sep))
        
        # Get file info
        file_info = get_file_info(abs_path)
        
        if file_info['exists']:
            # File exists - add to cleaned manifest
            existing_rows.append(row)
            stats['existing_count'] += 1
            
            if file_info['size'] is not None:
                stats['total_bytes'] += file_info['size']
                stats['ext_bytes'][file_info['ext']] += file_info['size']
            
            stats['ext_counts'][file_info['ext']] += 1
            
        else:
            # File missing - add to missing list
            missing_entry = {
                'path': rel_path,
                'bytes': row.get('bytes', ''),
                'is_binary': row.get('is_binary', ''),
                'sha1': row.get('sha1', ''),
                'abs_path': abs_path,
                'error': file_info.get('error', 'File not found')
            }
            missing_rows.append(missing_entry)
            stats['missing_count'] += 1
            stats['ext_counts'][file_info['ext']] += 1
            
            if file_info.get('error'):
                stats['errors'].append(f"{rel_path}: {file_info['error']}")
    
    # Calculate missing percentage
    missing_percentage = (stats['missing_count'] / total_rows) * 100 if total_rows > 0 else 0
    
    print(f"Validation complete:")
    print(f"  Total rows: {total_rows}")
    print(f"  Existing files: {stats['existing_count']}")
    print(f"  Missing files: {stats['missing_count']} ({missing_percentage:.1f}%)")
    
    append_log(f"Validation complete: {stats['existing_count']} existing, {stats['missing_count']} missing ({missing_percentage:.1f}%)")
    
    # Write cleaned manifest
    ensure_reports_dir()
    with open(CLEANED_MANIFEST, 'w', encoding='utf-8', newline='') as f:
        if existing_rows:
            writer = csv.DictWriter(f, fieldnames=existing_rows[0].keys(), lineterminator='\n')
            writer.writeheader()
            writer.writerows(existing_rows)
    
    print(f"Wrote cleaned manifest: {CLEANED_MANIFEST} ({len(existing_rows)} rows)")
    
    # Write missing files report
    if missing_rows:
        with open(MISSING_FILES, 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=missing_rows[0].keys(), lineterminator='\n')
            writer.writeheader()
            writer.writerows(missing_rows)
        
        print(f"Wrote missing files report: {MISSING_FILES} ({len(missing_rows)} rows)")
    
    # Generate statistics markdown
    stats_content = generate_stats_markdown(stats, missing_percentage)
    with open(STATS_MD, 'w', encoding='utf-8') as f:
        f.write(stats_content)
    
    print(f"Wrote statistics report: {STATS_MD}")
    
    append_log(f"Cleaning complete. Cleaned manifest: {len(existing_rows)} rows, Missing files: {len(missing_rows)} rows")
    
    return stats

def generate_stats_markdown(stats: Dict[str, Any], missing_percentage: float) -> str:
    """Generate statistics markdown report"""
    content = [
        "# Phase 1D Manifest Validation Statistics",
        f"Generated: {now_ts()}",
        ""
    ]
    
    # Warning if >5% missing
    if missing_percentage > 5.0:
        content.extend([
            "⚠️ **WARNING: High Missing File Rate**",
            f"**{missing_percentage:.1f}% of manifest entries are missing files!**",
            "This indicates significant file system changes since manifest creation.",
            ""
        ])
    
    # Summary statistics
    content.extend([
        "## Summary",
        f"- **Total manifest rows**: {stats['total_rows']:,}",
        f"- **Existing files**: {stats['existing_count']:,} ({(stats['existing_count']/stats['total_rows']*100):.1f}%)",
        f"- **Missing files**: {stats['missing_count']:,} ({missing_percentage:.1f}%)",
        f"- **Total bytes (existing)**: {stats['total_bytes']:,} ({stats['total_bytes']/1024/1024/1024:.2f} GB)",
        ""
    ])
    
    # Extension breakdown
    content.extend([
        "## File Extension Breakdown",
        "",
        "| Extension | Count | Bytes | Avg Size |",
        "|-----------|-------|-------|----------|"
    ])
    
    # Sort extensions by count
    sorted_exts = sorted(stats['ext_counts'].items(), key=lambda x: x[1], reverse=True)
    
    for ext, count in sorted_exts:
        ext_display = ext if ext else "(no extension)"
        bytes_total = stats['ext_bytes'].get(ext, 0)
        avg_size = bytes_total / count if count > 0 else 0
        
        content.append(f"| {ext_display} | {count:,} | {bytes_total:,} | {avg_size:,.0f} |")
    
    content.extend([
        "",
        "## File Size Distribution",
        ""
    ])
    
    # Size categories
    size_categories = {
        'Empty (0 bytes)': 0,
        'Small (< 1KB)': 0,
        'Medium (1KB - 1MB)': 0,
        'Large (1MB - 100MB)': 0,
        'Very Large (> 100MB)': 0
    }
    
    # This would require re-reading file sizes, so we'll skip for now
    content.extend([
        "*(Size distribution analysis would require re-reading all files)*",
        ""
    ])
    
    # Errors section
    if stats['errors']:
        content.extend([
            "## Errors Encountered",
            ""
        ])
        for error in stats['errors'][:20]:  # Limit to first 20 errors
            content.append(f"- {error}")
        
        if len(stats['errors']) > 20:
            content.append(f"- ... and {len(stats['errors']) - 20} more errors")
        
        content.append("")
    
    # Output files
    content.extend([
        "## Generated Files",
        f"- **Cleaned Manifest**: `{CLEANED_MANIFEST}`",
        f"- **Missing Files Report**: `{MISSING_FILES}`",
        f"- **This Statistics Report**: `{STATS_MD}`",
        f"- **Progress Log**: `{PROGRESS_LOG}`",
        ""
    ])
    
    # Recommendations
    content.extend([
        "## Recommendations",
        ""
    ])
    
    if missing_percentage > 5.0:
        content.extend([
            "- ⚠️ **High missing file rate detected**",
            "- Consider regenerating the manifest from current file system state",
            "- Review file system changes since original manifest creation",
            ""
        ])
    
    content.extend([
        "- Use the cleaned manifest for content scanning to avoid missing file errors",
        "- Review missing files report to understand what content may be lost",
        "- Consider updating source paths if files have been moved rather than deleted",
        ""
    ])
    
    return '\n'.join(content)

def main():
    """Main function"""
    try:
        stats = validate_and_clean_manifest()
        
        print("\n" + "="*60)
        print("MANIFEST VALIDATION COMPLETE")
        print("="*60)
        print(f"✅ Cleaned manifest ready: {CLEANED_MANIFEST}")
        print(f"📊 Statistics report: {STATS_MD}")
        if stats['missing_count'] > 0:
            print(f"⚠️  Missing files report: {MISSING_FILES}")
        print(f"📝 Progress log: {PROGRESS_LOG}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        append_log(f"ERROR: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
