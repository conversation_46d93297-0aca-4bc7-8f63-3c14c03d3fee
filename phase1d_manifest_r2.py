#!/usr/bin/env python3
"""
Phase 1D — MANIFEST_R2 (Zero-Filter • Deterministic • Correct Text/Binary • Hard Gate)
Rebuild manifest with accurate text/binary classification
"""

import os
import sys
import csv
import json
import time
import hashlib
import stat
from datetime import datetime
from typing import List, Dict, Any, Iterator, Set

# Configuration
SOURCE_DIR = r"C:\Users\<USER>\Documents\SalvageControlSystem"
OUTPUT_DIR = r"C:\Users\<USER>\Documents\repo analysis 202508\SCANS_OUT\SalvageControlSystem\phase1d_r2"

# Output files
MANIFEST_CSV = os.path.join(OUTPUT_DIR, 'full_scan_manifest.csv')
PROGRESS_LOG = os.path.join(OUTPUT_DIR, 'full_scan_progress.log')
STATE_JSON = os.path.join(OUTPUT_DIR, 'state.json')
LONG_PATH_MD = os.path.join(OUTPUT_DIR, 'long_path_analysis.md')
COMPLETED_FILE = os.path.join(OUTPUT_DIR, 'COMPLETED_MANIFEST_R2')
ABORT_FILE = os.path.join(OUTPUT_DIR, 'ABORT_MISMATCH_R2')

# Configuration
CHUNK_SIZE = 1024 * 1024  # 1MB chunks for hashing
SHA_LIMIT = 50 * 1024 * 1024  # 50MB limit for SHA1
BINARY_SIZE_LIMIT = 200 * 1024 * 1024  # 200MB safety limit
CONTENT_SAMPLE_SIZE = 8192  # 8KB for content analysis
CHECKPOINT_EVERY = 5000
CHECKPOINT_SECONDS = 60
PHASE = 'manifest_r2'

# Text file extensions (definitive text indicators)
TEXT_EXTENSIONS: Set[str] = {
    '.py', '.ps1', '.bat', '.cmd', '.ts', '.tsx', '.js', '.jsx',
    '.json', '.jsonl', '.csv', '.md', '.txt', '.yaml', '.yml',
    '.ini', '.cfg', '.conf', '.toml', '.xml', '.html', '.css', '.sql'
}

# CSV header
CSV_HEADER = ['path', 'bytes', 'is_binary', 'sha1']

def now_ts() -> str:
    """Return current UTC timestamp"""
    return datetime.utcnow().isoformat(timespec='seconds') + 'Z'

def as_long_path(p: str) -> str:
    """Convert to long path format for Windows"""
    ap = os.path.abspath(p)
    if ap.startswith('\\\\?\\'):
        return ap
    return "\\\\\\\\?\\" + ap

def append_log(line: str) -> None:
    """Append line to progress log"""
    with open(PROGRESS_LOG, 'a', encoding='utf-8') as f:
        f.write(f"{line}\n")

def atomic_write(path: str, content: str) -> None:
    """Atomic write using temp file + rename"""
    tmp = path + '.tmp'
    with open(tmp, 'w', encoding='utf-8', newline='') as f:
        f.write(content)
    os.replace(tmp, path)

def is_reparse_point(path: str) -> bool:
    """Check if path is a reparse point (junction/symlink)"""
    try:
        st = os.lstat(path)
        attrs = getattr(st, 'st_file_attributes', 0)
        return bool(attrs & getattr(stat, 'FILE_ATTRIBUTE_REPARSE_POINT', 0))
    except Exception:
        return False

def list_entries_sorted(dir_path: str) -> List[os.DirEntry]:
    """List directory entries in sorted order"""
    try:
        with os.scandir(dir_path) as it:
            entries = [e for e in it]
    except (PermissionError, FileNotFoundError):
        return []
    
    # Sort by name for deterministic order
    entries.sort(key=lambda e: e.name)
    return entries

def rel_path_forward_slash(path: str) -> str:
    """Convert to relative path with forward slashes"""
    rel = os.path.relpath(path, SOURCE_DIR)
    return rel.replace('\\', '/')

def is_text_by_extension(file_path: str) -> bool:
    """Check if file is text based on extension"""
    ext = os.path.splitext(file_path)[1].lower()
    return ext in TEXT_EXTENSIONS

def try_decode_text(content: bytes) -> bool:
    """Try to decode content as text using common encodings"""
    if not content:
        return True  # Empty files are text
    
    # Check for NUL bytes (strong binary indicator)
    if b'\x00' in content:
        return False
    
    # Try common text encodings
    encodings = ['utf-8', 'utf-16', 'utf-16-le', 'utf-16-be', 'latin-1', 'cp1252']
    
    for encoding in encodings:
        try:
            content.decode(encoding)
            return True
        except (UnicodeDecodeError, UnicodeError):
            continue
    
    return False

def is_text_file(file_path: str, file_size: int) -> bool:
    """Determine if file is text using extension and content analysis"""
    
    # Safety check for very large files
    if file_size > BINARY_SIZE_LIMIT:
        return False
    
    # Check extension first (fast path)
    if is_text_by_extension(file_path):
        return True
    
    # For files without text extensions, check content
    try:
        sample_size = min(file_size, CONTENT_SAMPLE_SIZE) if file_size > 0 else 0
        if sample_size == 0:
            return True  # Empty files are text
        
        with open(as_long_path(file_path), 'rb') as f:
            content = f.read(sample_size)
        
        return try_decode_text(content)
    
    except Exception:
        # If we can't read the file, assume binary
        return False

def compute_sha1(file_path: str, file_size: int) -> str:
    """Compute SHA1 hash for files <= 50MB"""
    if file_size < 0 or file_size > SHA_LIMIT:
        return ''
    
    try:
        hasher = hashlib.sha1()
        with open(as_long_path(file_path), 'rb') as f:
            while True:
                chunk = f.read(CHUNK_SIZE)
                if not chunk:
                    break
                hasher.update(chunk)
        return hasher.hexdigest()
    except Exception:
        return ''

def get_file_size(file_path: str) -> int:
    """Get file size using lstat"""
    try:
        st = os.lstat(file_path)
        return st.st_size
    except Exception:
        return -1

def iter_all_files(root_dir: str) -> Iterator[str]:
    """Iterate all files in deterministic lexicographic order without following symlinks"""
    stack = [root_dir]
    
    while stack:
        current_dir = stack.pop()
        entries = list_entries_sorted(current_dir)
        
        # Separate files and directories
        files = []
        dirs = []
        
        for entry in entries:
            full_path = os.path.join(current_dir, entry.name)
            
            # Check if it's a reparse point (symlink/junction)
            is_reparse = entry.is_symlink() or is_reparse_point(full_path)
            
            try:
                is_dir = entry.is_dir(follow_symlinks=False)
            except Exception:
                is_dir = False
            
            if is_dir and not is_reparse:
                dirs.append(full_path)
            else:
                # Treat everything else as a file (including reparse points)
                files.append(full_path)
        
        # Yield files first
        for file_path in files:
            yield file_path
        
        # Add directories to stack in reverse order for correct lexicographic processing
        for dir_path in reversed(dirs):
            stack.append(dir_path)

def load_state() -> Dict[str, Any]:
    """Load resume state if it exists"""
    if os.path.exists(STATE_JSON):
        try:
            with open(STATE_JSON, 'r', encoding='utf-8') as f:
                state = json.load(f)
            if (state.get('phase') == PHASE and 
                state.get('source_dir') == SOURCE_DIR and 
                state.get('output_dir') == OUTPUT_DIR):
                return state
        except Exception:
            pass
    
    return {
        'phase': PHASE,
        'source_dir': SOURCE_DIR,
        'output_dir': OUTPUT_DIR,
        'last_path': None,
        'processed': 0,
        'rows_written': 0,
        'text_files': 0,
        'binary_files': 0
    }

def save_state(state: Dict[str, Any]) -> None:
    """Save current state"""
    atomic_write(STATE_JSON, json.dumps(state, ensure_ascii=False, indent=2))

def count_existing_manifest_rows() -> int:
    """Count existing rows in manifest (excluding header)"""
    if not os.path.exists(MANIFEST_CSV):
        return 0
    
    try:
        with open(MANIFEST_CSV, 'r', encoding='utf-8', newline='') as f:
            reader = csv.reader(f)
            header = next(reader, None)  # Skip header
            count = 0
            for row in reader:
                if row:  # Non-empty row
                    count += 1
            return count
    except Exception:
        return 0

def initialize_manifest() -> None:
    """Initialize manifest CSV with header if it doesn't exist"""
    if not os.path.exists(MANIFEST_CSV):
        with open(MANIFEST_CSV, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f, lineterminator='\n')
            writer.writerow(CSV_HEADER)

def count_os_files() -> int:
    """Count all files using OS enumeration for parity check"""
    count = 0
    for _ in iter_all_files(SOURCE_DIR):
        count += 1
    return count

def main():
    """Main manifest generation function"""
    print(f"Starting Phase 1D Manifest R2 Generation at {now_ts()}")
    
    # Initialize
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    initialize_manifest()
    
    state = load_state()
    
    # Resume tracking
    last_path = state.get('last_path')
    processed = state.get('processed', 0)
    rows_written = state.get('rows_written', 0)
    text_files = state.get('text_files', 0)
    binary_files = state.get('binary_files', 0)
    
    # If resuming, verify existing manifest count
    if last_path:
        existing_rows = count_existing_manifest_rows()
        if existing_rows != rows_written:
            print(f"Warning: State shows {rows_written} rows but manifest has {existing_rows}")
            rows_written = existing_rows
    
    skipping = last_path is not None
    files_this_checkpoint = 0
    last_checkpoint_time = time.time()
    long_paths = []
    
    append_log(f"time={now_ts()} phase={PHASE} start processed={processed} rows_written={rows_written} resume_from={last_path}")
    
    # Process files
    with open(MANIFEST_CSV, 'a', encoding='utf-8', newline='') as f:
        writer = csv.writer(f, lineterminator='\n')
        
        for file_path in iter_all_files(SOURCE_DIR):
            rel_path = rel_path_forward_slash(file_path)
            
            # Skip until resume point
            if skipping:
                if rel_path <= last_path:
                    continue
                else:
                    skipping = False
            
            # Check for long paths
            abs_path = os.path.abspath(file_path)
            if len(abs_path) >= 260:
                long_paths.append(rel_path)
            
            # Get file metadata
            file_size = get_file_size(file_path)
            
            # Determine if text or binary
            is_reparse = is_reparse_point(file_path) or os.path.islink(file_path)
            if is_reparse:
                is_binary = True
                binary_files += 1
            else:
                is_text = is_text_file(file_path, file_size)
                is_binary = not is_text
                if is_text:
                    text_files += 1
                else:
                    binary_files += 1
            
            # Compute hash
            sha1_hash = compute_sha1(file_path, file_size)
            
            # Write row
            writer.writerow([
                rel_path,
                str(file_size),
                'true' if is_binary else 'false',
                sha1_hash
            ])
            
            processed += 1
            rows_written += 1
            files_this_checkpoint += 1
            
            # Checkpoint
            current_time = time.time()
            if (files_this_checkpoint >= CHECKPOINT_EVERY or 
                current_time - last_checkpoint_time >= CHECKPOINT_SECONDS):
                
                state.update({
                    'last_path': rel_path,
                    'processed': processed,
                    'rows_written': rows_written,
                    'text_files': text_files,
                    'binary_files': binary_files
                })
                save_state(state)
                
                append_log(f"time={now_ts()} phase={PHASE} processed={processed} rows_written={rows_written} text={text_files} binary={binary_files} last_path={rel_path}")
                
                files_this_checkpoint = 0
                last_checkpoint_time = current_time
    
    # Final state update
    state.update({
        'last_path': 'END',
        'processed': processed,
        'rows_written': rows_written,
        'text_files': text_files,
        'binary_files': binary_files,
        'completed': now_ts()
    })
    save_state(state)
    
    append_log(f"time={now_ts()} phase={PHASE} complete processed={processed} rows_written={rows_written} text={text_files} binary={binary_files}")
    
    # Write long path analysis if needed
    if long_paths:
        content = [
            f"# Long Path Analysis ({now_ts()})",
            "",
            "The following paths required long-path handling (\\\\?\\ prefix):",
            ""
        ]
        for path in long_paths:
            content.append(f"- {path}")
        content.append("")
        atomic_write(LONG_PATH_MD, '\n'.join(content))
    
    # Parity validation (Hard Gate)
    print("Performing parity validation...")
    os_count = count_os_files()
    manifest_count = rows_written
    
    if manifest_count == os_count:
        result = f"COMPLETED_MANIFEST_R2 manifest={manifest_count} os={os_count} text={text_files} binary={binary_files}"
        atomic_write(COMPLETED_FILE, result + '\n')
        print(result)
        return 0
    else:
        # Generate sample differences for debugging
        manifest_paths = set()
        try:
            with open(MANIFEST_CSV, 'r', encoding='utf-8', newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row.get('path'):
                        manifest_paths.add(row['path'])
        except Exception:
            pass
        
        os_paths = set()
        for file_path in iter_all_files(SOURCE_DIR):
            os_paths.add(rel_path_forward_slash(file_path))
        
        only_in_os = list(os_paths - manifest_paths)[:10]
        only_in_manifest = list(manifest_paths - os_paths)[:10]
        
        error_lines = [f"ABORT_MISMATCH_R2 manifest={manifest_count} os={os_count}"]
        if only_in_os:
            error_lines.append("only_in_os:")
            for path in only_in_os:
                error_lines.append(f"+ {path}")
        if only_in_manifest:
            error_lines.append("only_in_manifest:")
            for path in only_in_manifest:
                error_lines.append(f"- {path}")
        
        result = '\n'.join(error_lines) + '\n'
        atomic_write(ABORT_FILE, result)
        print(result.strip())
        return 2

if __name__ == "__main__":
    sys.exit(main())
