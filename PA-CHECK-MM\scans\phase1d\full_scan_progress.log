﻿2025-08-10T16:23:30.123Z - Starting manifest generation - phase=manifest
2025-08-10T16:23:30.152Z - Starting OS enumeration for baseline count
2025-08-10T16:23:30.592Z - OS enumeration progress: 5000 files counted
2025-08-10T16:23:30.888Z - OS enumeration progress: 10000 files counted
2025-08-10T16:23:31.181Z - OS enumeration progress: 15000 files counted
2025-08-10T16:23:31.549Z - OS enumeration progress: 20000 files counted
2025-08-10T16:23:32.061Z - OS enumeration progress: 25000 files counted
2025-08-10T16:23:32.536Z - OS enumeration progress: 30000 files counted
2025-08-10T16:23:33.237Z - OS enumeration progress: 35000 files counted
2025-08-10T16:23:33.823Z - OS enumeration progress: 40000 files counted
2025-08-10T16:23:34.327Z - OS enumeration progress: 45000 files counted
2025-08-10T16:23:34.598Z - OS enumeration completed: total_files=48438
2025-08-10T16:23:34.624Z - Created manifest file with header
2025-08-10T16:23:34.627Z - Collecting all files for lexicographic sorting
2025-08-10T16:25:38.303Z - Collected and sorted 48438 files
2025-08-10T16:25:38.343Z - Wrote long path analysis: 1 paths
2025-08-10T16:25:38.346Z - Processing files starting from index 0
2025-08-10T16:27:59.100Z - Checkpoint: processed=5000 rows_written=5000 last_path=frontend/node_modules/date-fns/fp/lastDayOfDecade/index.d.ts
2025-08-10T16:31:16.548Z - Checkpoint: processed=10000 rows_written=10000 last_path=frontend/node_modules/date-fns/esm/fp/quartersToMonths/index.js
2025-08-10T16:35:24.848Z - Checkpoint: processed=15000 rows_written=15000 last_path=node_modules/@babel/runtime/helpers/esm/classPrivateFieldGet.js
2025-08-10T16:38:45.992Z - Checkpoint: processed=20000 rows_written=20000 last_path=frontend/node_modules/framer-motion/dist/framer-motion.js
2025-08-10T16:43:30.934Z - Checkpoint: processed=25000 rows_written=25000 last_path=frontend/node_modules/@mui/icons-material/MonitorWeightOutlined.js
2025-08-10T16:45:14.355Z - Checkpoint: processed=30000 rows_written=30000 last_path=frontend/node_modules/@mui/icons-material/OndemandVideoTwoTone.d.ts
2025-08-10T16:47:17.170Z - Checkpoint: processed=35000 rows_written=35000 last_path=frontend/node_modules/@mui/icons-material/ErrorOutlineRounded.d.ts
2025-08-10T16:50:26.796Z - Checkpoint: processed=40000 rows_written=40000 last_path=frontend/node_modules/@mui/utils/legacy/useSlotProps/desktop.ini
2025-08-10T16:53:11.618Z - Checkpoint: processed=45000 rows_written=45000 last_path=frontend/node_modules/@mui/icons-material/ShapeLineTwoTone.d.ts
2025-08-10T16:54:23.948Z - Manifest generation completed: manifest_rows=48438
2025-08-10T16:54:23.952Z - COMPLETED_MANIFEST manifest=48438 os=48438
2025-08-10T17:02:28.020Z - Starting Phase 1D content scan
2025-08-10T17:02:28.042Z - Loading manifest from C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d\full_scan_manifest.csv
2025-08-10T17:04:44.664Z - Loaded manifest: 48438 files to process
2025-08-10T17:04:44.686Z - Starting file processing from index 0
2025-08-10T17:08:44.517Z - Starting Fast Phase 1D content scan (batch size: 50)
2025-08-10T17:08:44.521Z - Loading manifest from C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d\full_scan_manifest.csv
2025-08-10T17:08:44.890Z - Parsing manifest entries...
2025-08-10T17:08:50.042Z - Parsed 10000 manifest entries...
2025-08-10T17:09:04.841Z - Parsed 20000 manifest entries...
2025-08-10T17:09:31.936Z - Parsed 30000 manifest entries...
2025-08-10T17:10:19.228Z - Parsed 40000 manifest entries...
2025-08-10T17:11:14.975Z - Loaded manifest: 48438 files to process
2025-08-10T17:11:15.026Z - Starting file processing from index 0 in batches of 50
2025-08-10T17:11:15.030Z - Processing batch: files 0 to 49
2025-08-10T17:11:15.488Z - Batch checkpoint: processed=50 nuggets=0 last_path=frontend/node_modules/date-fns/esm/setISOWeek/index.d.ts
2025-08-10T17:11:15.491Z - Processing batch: files 50 to 99
2025-08-10T17:11:15.864Z - Batch checkpoint: processed=100 nuggets=0 last_path=frontend/node_modules/date-fns/esm/parse/_lib/parsers/ExtendedYearParser.js
2025-08-10T17:11:15.866Z - Processing batch: files 100 to 149
2025-08-10T17:11:16.508Z - Batch checkpoint: processed=150 nuggets=0 last_path=frontend/node_modules/date-fns/esm/previousDay/index.js.flow
2025-08-10T17:11:16.510Z - Processing batch: files 150 to 199
2025-08-10T17:11:17.006Z - Batch checkpoint: processed=200 nuggets=0 last_path=frontend/node_modules/date-fns/esm/subDays/index.js.flow
2025-08-10T17:11:17.009Z - Processing batch: files 200 to 249
2025-08-10T17:11:17.341Z - Batch checkpoint: processed=250 nuggets=0 last_path=frontend/node_modules/date-fns/esm/yearsToQuarters/desktop.ini
2025-08-10T17:11:17.344Z - Processing batch: files 250 to 299
2025-08-10T17:11:17.635Z - Batch checkpoint: processed=300 nuggets=0 last_path=frontend/node_modules/date-fns/esm/startOfHour/index.js
2025-08-10T17:11:17.637Z - Processing batch: files 300 to 349
2025-08-10T17:11:17.971Z - Batch checkpoint: processed=350 nuggets=0 last_path=frontend/node_modules/date-fns/esm/startOfTomorrow/desktop.ini
2025-08-10T17:11:17.974Z - Processing batch: files 350 to 399
2025-08-10T17:11:18.203Z - Batch checkpoint: processed=400 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/th/_lib/formatDistance/index.js
2025-08-10T17:11:18.206Z - Processing batch: files 400 to 449
2025-08-10T17:11:18.445Z - Batch checkpoint: processed=450 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/tr/_lib/desktop.ini
2025-08-10T17:11:18.449Z - Processing batch: files 450 to 499
2025-08-10T17:11:18.762Z - Batch checkpoint: processed=500 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/sk/_lib/formatLong/desktop.ini
2025-08-10T17:11:18.765Z - Processing batch: files 500 to 549
2025-08-10T17:11:19.046Z - Batch checkpoint: processed=550 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/sr/package.json
2025-08-10T17:11:19.054Z - Processing batch: files 550 to 599
2025-08-10T17:11:19.326Z - Batch checkpoint: processed=600 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/zh-TW/_lib/match/desktop.ini
2025-08-10T17:11:19.328Z - Processing batch: files 600 to 649
2025-08-10T17:11:19.621Z - Batch checkpoint: processed=650 nuggets=0 last_path=frontend/node_modules/date-fns/esm/monthsToQuarters/desktop.ini
2025-08-10T17:11:19.631Z - Processing batch: files 650 to 699
2025-08-10T17:11:19.931Z - Batch checkpoint: processed=700 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/uz/_lib/match/index.js
2025-08-10T17:11:19.934Z - Processing batch: files 700 to 749
2025-08-10T17:11:20.225Z - Batch checkpoint: processed=750 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/zh-HK/index.d.ts
2025-08-10T17:11:20.229Z - Processing batch: files 750 to 799
2025-08-10T17:11:20.356Z - Batch checkpoint: processed=800 nuggets=0 last_path=frontend/node_modules/date-fns/fp/formatDistanceStrict/desktop.ini
2025-08-10T17:11:20.359Z - Processing batch: files 800 to 849
2025-08-10T17:11:20.525Z - Batch checkpoint: processed=850 nuggets=0 last_path=frontend/node_modules/date-fns/fp/formatRelative/desktop.ini
2025-08-10T17:11:20.528Z - Processing batch: files 850 to 899
2025-08-10T17:11:20.723Z - Batch checkpoint: processed=900 nuggets=0 last_path=frontend/node_modules/date-fns/fp/endOfSecond/desktop.ini
2025-08-10T17:11:20.725Z - Processing batch: files 900 to 949
2025-08-10T17:11:20.847Z - Batch checkpoint: processed=950 nuggets=0 last_path=frontend/node_modules/date-fns/fp/getWeekYear/desktop.ini
2025-08-10T17:11:20.850Z - Processing batch: files 950 to 999
2025-08-10T17:11:20.964Z - Batch checkpoint: processed=1000 nuggets=0 last_path=frontend/node_modules/date-fns/fp/intlFormat/index.js.flow
2025-08-10T17:11:20.966Z - Processing batch: files 1000 to 1049
2025-08-10T17:11:21.067Z - Batch checkpoint: processed=1050 nuggets=0 last_path=frontend/node_modules/date-fns/fp/getDaysInYear/index.js
2025-08-10T17:11:21.069Z - Processing batch: files 1050 to 1099
2025-08-10T17:11:21.181Z - Batch checkpoint: processed=1100 nuggets=0 last_path=frontend/node_modules/date-fns/fp/getQuarter/index.js.flow
2025-08-10T17:11:21.183Z - Processing batch: files 1100 to 1149
2025-08-10T17:11:21.349Z - Batch checkpoint: processed=1150 nuggets=0 last_path=frontend/node_modules/date-fns/fp/addHours/index.js.flow
2025-08-10T17:11:21.351Z - Processing batch: files 1150 to 1199
2025-08-10T17:11:21.645Z - Batch checkpoint: processed=1200 nuggets=0 last_path=frontend/node_modules/date-fns/fp/areIntervalsOverlapping/index.js
2025-08-10T17:11:21.647Z - Processing batch: files 1200 to 1249
2025-08-10T17:11:21.792Z - Batch checkpoint: processed=1250 nuggets=0 last_path=frontend/node_modules/date-fns/esm/_lib/roundingMethods/index.js
2025-08-10T17:11:21.795Z - Processing batch: files 1250 to 1299
2025-08-10T17:11:22.006Z - Batch checkpoint: processed=1300 nuggets=0 last_path=frontend/node_modules/date-fns/formatDuration/package.json
2025-08-10T17:11:22.009Z - Processing batch: files 1300 to 1349
2025-08-10T17:11:22.194Z - Batch checkpoint: processed=1350 nuggets=0 last_path=frontend/node_modules/date-fns/fp/differenceInMilliseconds/package.json
2025-08-10T17:11:22.197Z - Processing batch: files 1350 to 1399
2025-08-10T17:11:22.303Z - Batch checkpoint: processed=1400 nuggets=0 last_path=frontend/node_modules/date-fns/fp/differenceInYears/package.json
2025-08-10T17:11:22.306Z - Processing batch: files 1400 to 1449
2025-08-10T17:11:22.417Z - Batch checkpoint: processed=1450 nuggets=0 last_path=frontend/node_modules/date-fns/fp/areIntervalsOverlappingWithOptions/package.json
2025-08-10T17:11:22.421Z - Processing batch: files 1450 to 1499
2025-08-10T17:11:22.526Z - Batch checkpoint: processed=1500 nuggets=0 last_path=frontend/node_modules/date-fns/fp/differenceInCalendarISOWeekYears/package.json
2025-08-10T17:11:22.528Z - Processing batch: files 1500 to 1549
2025-08-10T17:11:22.757Z - Batch checkpoint: processed=1550 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/ar-SA/index.js.flow
2025-08-10T17:11:22.760Z - Processing batch: files 1550 to 1599
2025-08-10T17:11:23.061Z - Batch checkpoint: processed=1600 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/be/_lib/formatRelative/index.js
2025-08-10T17:11:23.064Z - Processing batch: files 1600 to 1649
2025-08-10T17:11:23.368Z - Batch checkpoint: processed=1650 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/package.json
2025-08-10T17:11:23.371Z - Processing batch: files 1650 to 1699
2025-08-10T17:11:23.683Z - Batch checkpoint: processed=1700 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/ar-DZ/package.json
2025-08-10T17:11:23.685Z - Processing batch: files 1700 to 1749
2025-08-10T17:11:23.995Z - Batch checkpoint: processed=1750 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/de/package.json
2025-08-10T17:11:24.001Z - Processing batch: files 1750 to 1799
2025-08-10T17:11:24.266Z - Batch checkpoint: processed=1800 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/bs/package.json
2025-08-10T17:11:24.269Z - Processing batch: files 1800 to 1849
2025-08-10T17:11:24.607Z - Batch checkpoint: processed=1850 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/cy/_lib/formatDistance/desktop.ini
2025-08-10T17:11:24.609Z - Processing batch: files 1850 to 1899
2025-08-10T17:11:24.974Z - Batch checkpoint: processed=1900 nuggets=0 last_path=frontend/node_modules/date-fns/esm/hoursToMilliseconds/index.js.flow
2025-08-10T17:11:24.976Z - Processing batch: files 1900 to 1949
2025-08-10T17:11:25.344Z - Batch checkpoint: processed=1950 nuggets=0 last_path=frontend/node_modules/date-fns/esm/isExists/index.js.flow
2025-08-10T17:11:25.347Z - Processing batch: files 1950 to 1999
2025-08-10T17:11:25.741Z - Batch checkpoint: processed=2000 nuggets=0 last_path=frontend/node_modules/date-fns/esm/getISOWeeksInYear/index.js
2025-08-10T17:11:25.743Z - Processing batch: files 2000 to 2049
2025-08-10T17:11:26.045Z - Batch checkpoint: processed=2050 nuggets=0 last_path=frontend/node_modules/date-fns/esm/getWeek/index.js
2025-08-10T17:11:26.049Z - Processing batch: files 2050 to 2099
2025-08-10T17:11:26.402Z - Batch checkpoint: processed=2100 nuggets=0 last_path=frontend/node_modules/date-fns/esm/isTomorrow/desktop.ini
2025-08-10T17:11:26.405Z - Processing batch: files 2100 to 2149
2025-08-10T17:11:26.743Z - Batch checkpoint: processed=2150 nuggets=0 last_path=frontend/node_modules/date-fns/esm/lastDayOfMonth/desktop.ini
2025-08-10T17:11:26.748Z - Processing batch: files 2150 to 2199
2025-08-10T17:11:27.156Z - Batch checkpoint: processed=2200 nuggets=0 last_path=frontend/node_modules/date-fns/esm/isSameDay/index.d.ts
2025-08-10T17:11:27.159Z - Processing batch: files 2200 to 2249
2025-08-10T17:11:27.585Z - Batch checkpoint: processed=2250 nuggets=0 last_path=frontend/node_modules/date-fns/esm/isSameQuarter/index.js.flow
2025-08-10T17:11:27.588Z - Processing batch: files 2250 to 2299
2025-08-10T17:11:27.977Z - Batch checkpoint: processed=2300 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/ko/_lib/localize/index.js
2025-08-10T17:11:27.979Z - Processing batch: files 2300 to 2349
2025-08-10T17:11:28.275Z - Batch checkpoint: processed=2350 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/lv/index.js.flow
2025-08-10T17:11:28.278Z - Processing batch: files 2350 to 2399
2025-08-10T17:11:28.598Z - Batch checkpoint: processed=2400 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/ja/_lib/formatLong/index.js
2025-08-10T17:11:28.601Z - Processing batch: files 2400 to 2449
2025-08-10T17:11:28.897Z - Batch checkpoint: processed=2450 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/km/_lib/formatDistance/index.js
2025-08-10T17:11:28.899Z - Processing batch: files 2450 to 2499
2025-08-10T17:11:29.204Z - Batch checkpoint: processed=2500 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/pl/_lib/formatDistance/index.js
2025-08-10T17:11:29.206Z - Processing batch: files 2500 to 2549
2025-08-10T17:11:29.563Z - Batch checkpoint: processed=2550 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/pt-BR/_lib/localize/index.js
2025-08-10T17:11:29.566Z - Processing batch: files 2550 to 2599
2025-08-10T17:11:29.837Z - Batch checkpoint: processed=2600 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/mt/desktop.ini
2025-08-10T17:11:29.840Z - Processing batch: files 2600 to 2649
2025-08-10T17:11:30.169Z - Batch checkpoint: processed=2650 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/fr/index.js.flow
2025-08-10T17:11:30.177Z - Processing batch: files 2650 to 2699
2025-08-10T17:11:30.475Z - Batch checkpoint: processed=2700 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/gd/index.js.flow
2025-08-10T17:11:30.478Z - Processing batch: files 2700 to 2749
2025-08-10T17:11:30.789Z - Batch checkpoint: processed=2750 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/eo/index.js
2025-08-10T17:11:30.792Z - Processing batch: files 2750 to 2799
2025-08-10T17:11:31.041Z - Batch checkpoint: processed=2800 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/et/_lib/match/desktop.ini
2025-08-10T17:11:31.044Z - Processing batch: files 2800 to 2849
2025-08-10T17:11:31.391Z - Batch checkpoint: processed=2850 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/id/index.js
2025-08-10T17:11:31.394Z - Processing batch: files 2850 to 2899
2025-08-10T17:11:31.702Z - Batch checkpoint: processed=2900 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/ja/index.js
2025-08-10T17:11:31.706Z - Processing batch: files 2900 to 2949
2025-08-10T17:11:31.974Z - Batch checkpoint: processed=2950 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/he/_lib/localize/index.js
2025-08-10T17:11:31.977Z - Processing batch: files 2950 to 2999
2025-08-10T17:11:32.288Z - Batch checkpoint: processed=3000 nuggets=0 last_path=frontend/node_modules/date-fns/esm/locale/ht/index.js.flow
2025-08-10T17:11:32.291Z - Processing batch: files 3000 to 3049
2025-08-10T17:11:32.601Z - Batch checkpoint: processed=3050 nuggets=0 last_path=frontend/node_modules/date-fns/locale/nl/package.json
2025-08-10T17:11:32.604Z - Processing batch: files 3050 to 3099
2025-08-10T17:11:32.911Z - Batch checkpoint: processed=3100 nuggets=0 last_path=frontend/node_modules/date-fns/locale/nl-BE/index.js
2025-08-10T17:11:32.914Z - Processing batch: files 3100 to 3149
2025-08-10T17:11:33.181Z - Batch checkpoint: processed=3150 nuggets=0 last_path=frontend/node_modules/date-fns/locale/lb/_lib/formatLong/desktop.ini
2025-08-10T17:11:33.184Z - Processing batch: files 3150 to 3199
2025-08-10T17:11:33.542Z - Batch checkpoint: processed=3200 nuggets=0 last_path=frontend/node_modules/date-fns/locale/lv/_lib/match/desktop.ini
2025-08-10T17:11:33.545Z - Processing batch: files 3200 to 3249
2025-08-10T17:11:33.854Z - Batch checkpoint: processed=3250 nuggets=0 last_path=frontend/node_modules/date-fns/locale/sk/_lib/match/desktop.ini
2025-08-10T17:11:33.857Z - Processing batch: files 3250 to 3299
2025-08-10T17:11:34.151Z - Batch checkpoint: processed=3300 nuggets=0 last_path=frontend/node_modules/date-fns/locale/sr-Latn/_lib/localize/desktop.ini
2025-08-10T17:11:34.153Z - Processing batch: files 3300 to 3349
2025-08-10T17:11:34.479Z - Batch checkpoint: processed=3350 nuggets=0 last_path=frontend/node_modules/date-fns/locale/pl/_lib/match/index.js
2025-08-10T17:11:34.482Z - Processing batch: files 3350 to 3399
2025-08-10T17:11:34.808Z - Batch checkpoint: processed=3400 nuggets=0 last_path=frontend/node_modules/date-fns/locale/ro/_lib/formatRelative/index.js
2025-08-10T17:11:34.811Z - Processing batch: files 3400 to 3449
2025-08-10T17:11:35.152Z - Batch checkpoint: processed=3450 nuggets=0 last_path=frontend/node_modules/date-fns/locale/gu/_lib/formatLong/index.js
2025-08-10T17:11:35.155Z - Processing batch: files 3450 to 3499
2025-08-10T17:11:35.464Z - Batch checkpoint: processed=3500 nuggets=0 last_path=frontend/node_modules/date-fns/locale/gl/_lib/formatRelative/desktop.ini
2025-08-10T17:11:35.466Z - Processing batch: files 3500 to 3549
2025-08-10T17:11:35.782Z - Batch checkpoint: processed=3550 nuggets=0 last_path=frontend/node_modules/date-fns/locale/gd/_lib/formatRelative/index.js
2025-08-10T17:11:35.785Z - Processing batch: files 3550 to 3599
2025-08-10T17:11:36.093Z - Batch checkpoint: processed=3600 nuggets=0 last_path=frontend/node_modules/date-fns/locale/ka/_lib/localize/index.js
2025-08-10T17:11:36.096Z - Processing batch: files 3600 to 3649
2025-08-10T17:11:36.372Z - Batch checkpoint: processed=3650 nuggets=0 last_path=frontend/node_modules/date-fns/locale/kn/_lib/formatLong/index.js
2025-08-10T17:11:36.374Z - Processing batch: files 3650 to 3699
2025-08-10T17:11:36.690Z - Batch checkpoint: processed=3700 nuggets=0 last_path=frontend/node_modules/date-fns/locale/id/package.json
2025-08-10T17:11:36.693Z - Processing batch: files 3700 to 3749
2025-08-10T17:11:37.132Z - Batch checkpoint: processed=3750 nuggets=0 last_path=frontend/node_modules/date-fns/locale/ja/_lib/formatRelative/desktop.ini
2025-08-10T17:11:37.134Z - Processing batch: files 3750 to 3799
2025-08-10T17:11:37.422Z - Batch checkpoint: processed=3800 nuggets=0 last_path=frontend/node_modules/date-fns/startOfISOWeekYear/index.js.flow
2025-08-10T17:11:37.425Z - Processing batch: files 3800 to 3849
2025-08-10T17:11:37.786Z - Batch checkpoint: processed=3850 nuggets=0 last_path=frontend/node_modules/date-fns/startOfWeekYear/index.d.ts
2025-08-10T17:11:37.796Z - Processing batch: files 3850 to 3899
2025-08-10T17:11:38.113Z - Batch checkpoint: processed=3900 nuggets=0 last_path=frontend/node_modules/date-fns/setDate/desktop.ini
2025-08-10T17:11:38.116Z - Processing batch: files 3900 to 3949
2025-08-10T17:11:38.466Z - Batch checkpoint: processed=3950 nuggets=0 last_path=frontend/node_modules/date-fns/setISOWeek/index.js
2025-08-10T17:11:38.469Z - Processing batch: files 3950 to 3999
2025-08-10T17:11:38.674Z - Batch checkpoint: processed=4000 nuggets=0 last_path=frontend/node_modules/date-fns/_lib/getTimezoneOffsetInMilliseconds/index.js
2025-08-10T17:11:38.676Z - Processing batch: files 4000 to 4049
2025-08-10T17:11:39.112Z - Batch checkpoint: processed=4050 nuggets=0 last_path=frontend/node_modules/debug/src/common.js
2025-08-10T17:11:39.115Z - Processing batch: files 4050 to 4099
2025-08-10T17:11:39.668Z - Batch checkpoint: processed=4100 nuggets=0 last_path=frontend/node_modules/date-fns/startOfYesterday/index.d.ts
2025-08-10T17:11:39.671Z - Processing batch: files 4100 to 4149
2025-08-10T17:11:39.944Z - Batch checkpoint: processed=4150 nuggets=0 last_path=frontend/node_modules/date-fns/subSeconds/index.d.ts
2025-08-10T17:11:39.947Z - Processing batch: files 4150 to 4199
2025-08-10T17:11:40.248Z - Batch checkpoint: processed=4200 nuggets=0 last_path=frontend/node_modules/date-fns/locale/uz-Cyrl/_lib/formatRelative/desktop.ini
2025-08-10T17:11:40.252Z - Processing batch: files 4200 to 4249
2025-08-10T17:11:40.558Z - Batch checkpoint: processed=4250 nuggets=0 last_path=frontend/node_modules/date-fns/locale/zh-HK/_lib/localize/desktop.ini
2025-08-10T17:11:40.561Z - Processing batch: files 4250 to 4299
2025-08-10T17:11:40.867Z - Batch checkpoint: processed=4300 nuggets=0 last_path=frontend/node_modules/date-fns/locale/te/_lib/formatDistance/desktop.ini
2025-08-10T17:11:40.870Z - Processing batch: files 4300 to 4349
2025-08-10T17:11:41.122Z - Batch checkpoint: processed=4350 nuggets=0 last_path=frontend/node_modules/date-fns/locale/ug/_lib/formatLong/desktop.ini
2025-08-10T17:11:41.125Z - Processing batch: files 4350 to 4399
2025-08-10T17:11:42.094Z - Batch checkpoint: processed=4400 nuggets=0 last_path=frontend/node_modules/date-fns/previousSunday/desktop.ini
2025-08-10T17:11:42.096Z - Processing batch: files 4400 to 4449
2025-08-10T17:11:42.432Z - Batch checkpoint: processed=4450 nuggets=0 last_path=frontend/node_modules/date-fns/minutesToHours/index.d.ts
2025-08-10T17:11:42.434Z - Processing batch: files 4450 to 4499
2025-08-10T17:11:42.746Z - Batch checkpoint: processed=4500 nuggets=0 last_path=frontend/node_modules/date-fns/nextMonday/package.json
2025-08-10T17:11:42.749Z - Processing batch: files 4500 to 4549
2025-08-10T17:11:43.053Z - Batch checkpoint: processed=4550 nuggets=0 last_path=frontend/node_modules/date-fns/fp/startOfHour/package.json
2025-08-10T17:11:43.056Z - Processing batch: files 4550 to 4599
2025-08-10T17:11:43.181Z - Batch checkpoint: processed=4600 nuggets=0 last_path=frontend/node_modules/date-fns/fp/sub/desktop.ini
2025-08-10T17:11:43.183Z - Processing batch: files 4600 to 4649
2025-08-10T17:11:43.309Z - Batch checkpoint: processed=4650 nuggets=0 last_path=frontend/node_modules/date-fns/fp/setISODay/desktop.ini
2025-08-10T17:11:43.311Z - Processing batch: files 4650 to 4699
2025-08-10T17:11:43.436Z - Batch checkpoint: processed=4700 nuggets=0 last_path=frontend/node_modules/date-fns/fp/setWeekYear/desktop.ini
2025-08-10T17:11:43.438Z - Processing batch: files 4700 to 4749
2025-08-10T17:11:43.632Z - Batch checkpoint: processed=4750 nuggets=0 last_path=frontend/node_modules/date-fns/getISOWeekYear/desktop.ini
2025-08-10T17:11:43.634Z - Processing batch: files 4750 to 4799
2025-08-10T17:11:43.980Z - Batch checkpoint: processed=4800 nuggets=0 last_path=frontend/node_modules/date-fns/getWeekOfMonth/desktop.ini
2025-08-10T17:11:43.982Z - Processing batch: files 4800 to 4849
2025-08-10T17:11:44.200Z - Batch checkpoint: processed=4850 nuggets=0 last_path=frontend/node_modules/date-fns/fp/subHours/package.json
2025-08-10T17:11:44.204Z - Processing batch: files 4850 to 4899
2025-08-10T17:11:44.443Z - Batch checkpoint: processed=4900 nuggets=0 last_path=frontend/node_modules/date-fns/fp/weeksToDays/package.json
2025-08-10T17:11:44.446Z - Processing batch: files 4900 to 4949
2025-08-10T17:11:44.580Z - Batch checkpoint: processed=4950 nuggets=0 last_path=frontend/node_modules/date-fns/fp/isSameWeekWithOptions/index.d.ts
2025-08-10T17:11:44.583Z - Processing batch: files 4950 to 4999
2025-08-10T17:11:44.718Z - Batch checkpoint: processed=5000 nuggets=0 last_path=frontend/node_modules/date-fns/fp/lastDayOfDecade/index.d.ts
2025-08-10T17:11:44.720Z - Processing batch: files 5000 to 5049
2025-08-10T17:11:44.879Z - Batch checkpoint: processed=5050 nuggets=0 last_path=frontend/node_modules/date-fns/fp/isExists/index.d.ts
2025-08-10T17:11:44.882Z - Processing batch: files 5050 to 5099
2025-08-10T17:11:45.017Z - Batch checkpoint: processed=5100 nuggets=0 last_path=frontend/node_modules/date-fns/fp/isSameISOWeekYear/index.js
2025-08-10T17:11:45.020Z - Processing batch: files 5100 to 5149
2025-08-10T17:11:45.180Z - Batch checkpoint: processed=5150 nuggets=0 last_path=frontend/node_modules/date-fns/fp/parseISOWithOptions/index.d.ts
2025-08-10T17:11:45.183Z - Processing batch: files 5150 to 5199
2025-08-10T17:11:45.306Z - Batch checkpoint: processed=5200 nuggets=0 last_path=frontend/node_modules/date-fns/fp/previousTuesday/desktop.ini
2025-08-10T17:11:45.308Z - Processing batch: files 5200 to 5249
2025-08-10T17:11:45.443Z - Batch checkpoint: processed=5250 nuggets=0 last_path=frontend/node_modules/date-fns/fp/milliseconds/package.json
2025-08-10T17:11:45.445Z - Processing batch: files 5250 to 5299
2025-08-10T17:11:45.581Z - Batch checkpoint: processed=5300 nuggets=0 last_path=frontend/node_modules/date-fns/locale/bs/_lib/match/index.js
2025-08-10T17:11:45.584Z - Processing batch: files 5300 to 5349
2025-08-10T17:11:45.926Z - Batch checkpoint: processed=5350 nuggets=0 last_path=frontend/node_modules/date-fns/locale/da/index.d.ts
2025-08-10T17:11:45.928Z - Processing batch: files 5350 to 5399
2025-08-10T17:11:46.267Z - Batch checkpoint: processed=5400 nuggets=0 last_path=frontend/node_modules/date-fns/locale/ar-TN/_lib/formatRelative/index.js
2025-08-10T17:11:46.269Z - Processing batch: files 5400 to 5449
2025-08-10T17:11:46.566Z - Batch checkpoint: processed=5450 nuggets=0 last_path=frontend/node_modules/date-fns/locale/be-tarask/_lib/localize/index.js
2025-08-10T17:11:46.569Z - Processing batch: files 5450 to 5499
2025-08-10T17:11:46.913Z - Batch checkpoint: processed=5500 nuggets=0 last_path=frontend/node_modules/date-fns/locale/es/_lib/formatRelative/desktop.ini
2025-08-10T17:11:46.916Z - Processing batch: files 5500 to 5549
2025-08-10T17:11:47.252Z - Batch checkpoint: processed=5550 nuggets=0 last_path=frontend/node_modules/date-fns/locale/fa-IR/_lib/localize/desktop.ini
2025-08-10T17:11:47.255Z - Processing batch: files 5550 to 5599
2025-08-10T17:11:47.604Z - Batch checkpoint: processed=5600 nuggets=0 last_path=frontend/node_modules/date-fns/locale/en-AU/desktop.ini
2025-08-10T17:11:47.606Z - Processing batch: files 5600 to 5649
2025-08-10T17:11:47.907Z - Batch checkpoint: processed=5650 nuggets=0 last_path=frontend/node_modules/date-fns/locale/en-NZ/_lib/desktop.ini
2025-08-10T17:11:47.910Z - Processing batch: files 5650 to 5699
2025-08-10T17:11:48.215Z - Batch checkpoint: processed=5700 nuggets=0 last_path=frontend/node_modules/date-fns/isSameISOWeek/index.js
2025-08-10T17:11:48.218Z - Processing batch: files 5700 to 5749
2025-08-10T17:11:48.552Z - Batch checkpoint: processed=5750 nuggets=0 last_path=frontend/node_modules/date-fns/isSameWeek/package.json
2025-08-10T17:11:48.555Z - Processing batch: files 5750 to 5799
2025-08-10T17:11:48.931Z - Batch checkpoint: processed=5800 nuggets=0 last_path=frontend/node_modules/date-fns/getYear/index.js.flow
2025-08-10T17:11:48.934Z - Processing batch: files 5800 to 5849
2025-08-10T17:11:49.278Z - Batch checkpoint: processed=5850 nuggets=0 last_path=frontend/node_modules/date-fns/isBefore/index.d.ts
2025-08-10T17:11:49.281Z - Processing batch: files 5850 to 5899
2025-08-10T17:11:49.611Z - Batch checkpoint: processed=5900 nuggets=0 last_path=frontend/node_modules/date-fns/lightFormat/index.d.ts
2025-08-10T17:11:49.613Z - Processing batch: files 5900 to 5949
2025-08-10T17:11:49.857Z - Batch checkpoint: processed=5950 nuggets=0 last_path=frontend/node_modules/date-fns/locale/ar-EG/_lib/desktop.ini
2025-08-10T17:11:49.859Z - Processing batch: files 5950 to 5999
2025-08-10T17:11:50.239Z - Batch checkpoint: processed=6000 nuggets=0 last_path=frontend/node_modules/date-fns/isToday/desktop.ini
2025-08-10T17:11:50.242Z - Processing batch: files 6000 to 6049
2025-08-10T17:11:50.561Z - Batch checkpoint: processed=6050 nuggets=0 last_path=frontend/node_modules/date-fns/isYesterday/index.js.flow
2025-08-10T17:11:50.564Z - Processing batch: files 6050 to 6099
2025-08-10T17:11:50.897Z - Batch checkpoint: processed=6100 nuggets=0 last_path=frontend/node_modules/@react-stately/utils/dist/module.js.map
2025-08-10T17:11:50.899Z - Processing batch: files 6100 to 6149
2025-08-10T17:11:51.991Z - Batch checkpoint: processed=6150 nuggets=0 last_path=frontend/node_modules/@react-stately/overlays/dist/useOverlayTriggerState.mjs
2025-08-10T17:11:51.995Z - Processing batch: files 6150 to 6199
2025-08-10T17:11:52.422Z - Batch checkpoint: processed=6200 nuggets=0 last_path=frontend/node_modules/@react-stately/flags/src/index.ts
2025-08-10T17:11:52.424Z - Processing batch: files 6200 to 6249
2025-08-10T17:11:52.691Z - Batch checkpoint: processed=6250 nuggets=0 last_path=frontend/node_modules/@swc/helpers/esm/_class_apply_descriptor_get.js
2025-08-10T17:11:52.693Z - Processing batch: files 6250 to 6299
2025-08-10T17:11:52.857Z - Batch checkpoint: processed=6300 nuggets=0 last_path=frontend/node_modules/@swc/helpers/esm/_is_native_function.js
2025-08-10T17:11:52.859Z - Processing batch: files 6300 to 6349
2025-08-10T17:11:52.998Z - Batch checkpoint: processed=6350 nuggets=0 last_path=frontend/node_modules/@swc/helpers/cjs/_class_private_field_destructure.cjs
2025-08-10T17:11:53.000Z - Processing batch: files 6350 to 6399
2025-08-10T17:11:53.219Z - Batch checkpoint: processed=6400 nuggets=0 last_path=frontend/node_modules/@swc/helpers/cjs/_to_consumable_array.cjs
2025-08-10T17:11:53.221Z - Processing batch: files 6400 to 6449
2025-08-10T17:11:53.510Z - Batch checkpoint: processed=6450 nuggets=0 last_path=frontend/node_modules/@react-aria/overlays/src/useCloseOnScroll.ts
2025-08-10T17:11:53.512Z - Processing batch: files 6450 to 6499
2025-08-10T17:11:54.096Z - Batch checkpoint: processed=6500 nuggets=0 last_path=frontend/node_modules/@react-aria/utils/dist/chain.main.js.map
2025-08-10T17:11:54.098Z - Processing batch: files 6500 to 6549
2025-08-10T17:11:54.536Z - Batch checkpoint: processed=6550 nuggets=0 last_path=frontend/node_modules/@react-aria/overlays/dist/pt-PT.module.js.map
2025-08-10T17:11:54.539Z - Processing batch: files 6550 to 6599
2025-08-10T17:11:54.853Z - Batch checkpoint: processed=6600 nuggets=0 last_path=frontend/node_modules/@react-aria/overlays/dist/sk-SK.module.js
2025-08-10T17:11:54.855Z - Processing batch: files 6600 to 6649
2025-08-10T17:11:55.048Z - Batch checkpoint: processed=6650 nuggets=0 last_path=frontend/node_modules/@react-aria/utils/dist/useDeepMemo.module.js.map
2025-08-10T17:11:55.051Z - Processing batch: files 6650 to 6699
2025-08-10T17:11:55.374Z - Batch checkpoint: processed=6700 nuggets=0 last_path=frontend/node_modules/@react-aria/utils/dist/useLabels.mjs
2025-08-10T17:11:55.377Z - Processing batch: files 6700 to 6749
2025-08-10T17:11:55.599Z - Batch checkpoint: processed=6750 nuggets=0 last_path=frontend/node_modules/@react-aria/utils/dist/DOMFunctions.module.js
2025-08-10T17:11:55.601Z - Processing batch: files 6750 to 6799
2025-08-10T17:11:55.969Z - Batch checkpoint: processed=6800 nuggets=0 last_path=frontend/node_modules/@react-aria/utils/dist/keyboard.module.js
2025-08-10T17:11:55.972Z - Processing batch: files 6800 to 6849
2025-08-10T17:11:56.834Z - Batch checkpoint: processed=6850 nuggets=0 last_path=frontend/node_modules/attr-accept/dist/desktop.ini
2025-08-10T17:11:56.837Z - Processing batch: files 6850 to 6899
2025-08-10T17:11:57.286Z - Batch checkpoint: processed=6900 nuggets=0 last_path=frontend/node_modules/axios/lib/core/README.md
2025-08-10T17:11:57.298Z - Processing batch: files 6900 to 6949
2025-08-10T17:11:58.159Z - Batch checkpoint: processed=6950 nuggets=0 last_path=frontend/node_modules/@types/react/package.json
2025-08-10T17:11:58.161Z - Processing batch: files 6950 to 6999
2025-08-10T17:11:59.044Z - Batch checkpoint: processed=7000 nuggets=0 last_path=frontend/node_modules/@vitejs/plugin-react/desktop.ini
2025-08-10T17:11:59.047Z - Processing batch: files 7000 to 7049
2025-08-10T17:11:59.668Z - Batch checkpoint: processed=7050 nuggets=0 last_path=frontend/node_modules/call-bind-apply-helpers/applyBind.d.ts
2025-08-10T17:11:59.671Z - Processing batch: files 7050 to 7099
2025-08-10T17:12:00.199Z - Batch checkpoint: processed=7100 nuggets=0 last_path=frontend/node_modules/babel-plugin-macros/LICENSE
2025-08-10T17:12:00.201Z - Processing batch: files 7100 to 7149
2025-08-10T17:12:00.760Z - Batch checkpoint: processed=7150 nuggets=0 last_path=frontend/node_modules/broadcast-channel/dist/desktop.ini
2025-08-10T17:12:00.762Z - Processing batch: files 7150 to 7199
2025-08-10T17:12:01.301Z - Batch checkpoint: processed=7200 nuggets=0 last_path=frontend/node_modules/@swc/helpers/src/_ts_values.mjs
2025-08-10T17:12:01.302Z - Processing batch: files 7200 to 7249
2025-08-10T17:12:01.416Z - Batch checkpoint: processed=7250 nuggets=0 last_path=frontend/node_modules/@swc/helpers/_/_class_private_field_init/desktop.ini
2025-08-10T17:12:01.418Z - Processing batch: files 7250 to 7299
2025-08-10T17:12:01.526Z - Batch checkpoint: processed=7300 nuggets=0 last_path=frontend/node_modules/@swc/helpers/src/_class_apply_descriptor_get.mjs
2025-08-10T17:12:01.529Z - Processing batch: files 7300 to 7349
2025-08-10T17:12:01.722Z - Batch checkpoint: processed=7350 nuggets=0 last_path=frontend/node_modules/@swc/helpers/src/_is_native_function.mjs
2025-08-10T17:12:01.724Z - Processing batch: files 7350 to 7399
2025-08-10T17:12:01.869Z - Batch checkpoint: processed=7400 nuggets=0 last_path=frontend/node_modules/@swc/helpers/_/_ts_param/package.json
2025-08-10T17:12:01.873Z - Processing batch: files 7400 to 7449
2025-08-10T17:12:02.252Z - Batch checkpoint: processed=7450 nuggets=0 last_path=frontend/node_modules/@types/babel__template/LICENSE
2025-08-10T17:12:02.254Z - Processing batch: files 7450 to 7499
2025-08-10T17:12:02.462Z - Batch checkpoint: processed=7500 nuggets=0 last_path=frontend/node_modules/@swc/helpers/_/_class_private_method_get/desktop.ini
2025-08-10T17:12:02.469Z - Processing batch: files 7500 to 7549
2025-08-10T17:12:02.565Z - Batch checkpoint: processed=7550 nuggets=0 last_path=frontend/node_modules/@swc/helpers/_/_initializer_warning_helper/desktop.ini
2025-08-10T17:12:02.569Z - Processing batch: files 7550 to 7599
2025-08-10T17:12:02.809Z - Batch checkpoint: processed=7600 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/node/internals/components/DateTimeViewWrapper/desktop.ini
2025-08-10T17:12:02.816Z - Processing batch: files 7600 to 7649
2025-08-10T17:12:03.030Z - Batch checkpoint: processed=7650 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/node/internals/hooks/usePicker/usePickerViews.js
2025-08-10T17:12:03.033Z - Processing batch: files 7650 to 7699
2025-08-10T17:12:03.441Z - Batch checkpoint: processed=7700 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/node/AdapterMomentJalaali/index.js
2025-08-10T17:12:03.443Z - Processing batch: files 7700 to 7749
2025-08-10T17:12:03.689Z - Batch checkpoint: processed=7750 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/node/DesktopDateTimePicker/DesktopDateTimePicker.types.js
2025-08-10T17:12:03.692Z - Processing batch: files 7750 to 7799
2025-08-10T17:12:03.957Z - Batch checkpoint: processed=7800 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/node/TimeClock/TimeClock.js
2025-08-10T17:12:03.960Z - Processing batch: files 7800 to 7849
2025-08-10T17:12:04.311Z - Batch checkpoint: processed=7850 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/PickersDay/package.json
2025-08-10T17:12:04.314Z - Processing batch: files 7850 to 7899
2025-08-10T17:12:04.596Z - Batch checkpoint: processed=7900 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/node/locales/roRO.js
2025-08-10T17:12:04.599Z - Processing batch: files 7900 to 7949
2025-08-10T17:12:04.867Z - Batch checkpoint: processed=7950 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/modern/internals/hooks/useValueWithTimezone.js
2025-08-10T17:12:04.869Z - Processing batch: files 7950 to 7999
2025-08-10T17:12:05.099Z - Batch checkpoint: processed=8000 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/modern/internals/utils/getDefaultReferenceDate.js
2025-08-10T17:12:05.102Z - Processing batch: files 8000 to 8049
2025-08-10T17:12:05.415Z - Batch checkpoint: processed=8050 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/modern/DateCalendar/PickersFadeTransitionGroup.js
2025-08-10T17:12:05.417Z - Processing batch: files 8050 to 8099
2025-08-10T17:12:05.797Z - Batch checkpoint: processed=8100 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/modern/DesktopTimePicker/desktop.ini
2025-08-10T17:12:05.800Z - Processing batch: files 8100 to 8149
2025-08-10T17:12:06.042Z - Batch checkpoint: processed=8150 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/modern/YearCalendar/PickersYear.js
2025-08-10T17:12:06.045Z - Processing batch: files 8150 to 8199
2025-08-10T17:12:06.394Z - Batch checkpoint: processed=8200 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/node/AdapterDayjs/desktop.ini
2025-08-10T17:12:06.397Z - Processing batch: files 8200 to 8249
2025-08-10T17:12:06.825Z - Batch checkpoint: processed=8250 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/modern/MobileDatePicker/index.js
2025-08-10T17:12:06.827Z - Processing batch: files 8250 to 8299
2025-08-10T17:12:07.112Z - Batch checkpoint: processed=8300 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/modern/PickersLayout/PickersLayout.types.js
2025-08-10T17:12:07.115Z - Processing batch: files 8300 to 8349
2025-08-10T17:12:07.586Z - Batch checkpoint: processed=8350 nuggets=0 last_path=frontend/node_modules/@react-aria/i18n/server/desktop.ini
2025-08-10T17:12:07.588Z - Processing batch: files 8350 to 8399
2025-08-10T17:12:07.942Z - Batch checkpoint: processed=8400 nuggets=0 last_path=frontend/node_modules/@react-aria/interactions/dist/createEventHandler.module.js
2025-08-10T17:12:07.945Z - Processing batch: files 8400 to 8449
2025-08-10T17:12:08.306Z - Batch checkpoint: processed=8450 nuggets=0 last_path=frontend/node_modules/@react-aria/focus/README.md
2025-08-10T17:12:08.309Z - Processing batch: files 8450 to 8499
2025-08-10T17:12:08.870Z - Batch checkpoint: processed=8500 nuggets=0 last_path=frontend/node_modules/@react-aria/focus/dist/virtualFocus.module.js.map
2025-08-10T17:12:08.880Z - Processing batch: files 8500 to 8549
2025-08-10T17:12:09.135Z - Batch checkpoint: processed=8550 nuggets=0 last_path=frontend/node_modules/@react-aria/overlays/dist/cs-CZ.module.js
2025-08-10T17:12:09.137Z - Processing batch: files 8550 to 8599
2025-08-10T17:12:09.331Z - Batch checkpoint: processed=8600 nuggets=0 last_path=frontend/node_modules/@react-aria/overlays/dist/hu-HU.module.js.map
2025-08-10T17:12:09.334Z - Processing batch: files 8600 to 8649
2025-08-10T17:12:10.012Z - Batch checkpoint: processed=8650 nuggets=0 last_path=frontend/node_modules/@react-aria/interactions/dist/useKeyboard.main.js.map
2025-08-10T17:12:10.013Z - Processing batch: files 8650 to 8699
2025-08-10T17:12:10.616Z - Batch checkpoint: processed=8700 nuggets=0 last_path=frontend/node_modules/@react-aria/interactions/src/useFocusable.tsx
2025-08-10T17:12:10.621Z - Processing batch: files 8700 to 8749
2025-08-10T17:12:11.078Z - Batch checkpoint: processed=8750 nuggets=0 last_path=frontend/node_modules/@popperjs/core/dist/esm/desktop.ini
2025-08-10T17:12:11.092Z - Processing batch: files 8750 to 8799
2025-08-10T17:12:11.479Z - Batch checkpoint: processed=8800 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/TimeClock/ClockPointer.js
2025-08-10T17:12:11.482Z - Processing batch: files 8800 to 8849
2025-08-10T17:12:11.983Z - Batch checkpoint: processed=8850 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/YearCalendar/PickersYear.js
2025-08-10T17:12:11.985Z - Processing batch: files 8850 to 8899
2025-08-10T17:12:12.428Z - Batch checkpoint: processed=8900 nuggets=0 last_path=frontend/node_modules/@popperjs/core/lib/modifiers/index.js.flow
2025-08-10T17:12:12.431Z - Processing batch: files 8900 to 8949
2025-08-10T17:12:12.617Z - Batch checkpoint: processed=8950 nuggets=0 last_path=frontend/node_modules/@popperjs/core/lib/utils/math.d.ts
2025-08-10T17:12:12.621Z - Processing batch: files 8950 to 8999
2025-08-10T17:12:12.783Z - Batch checkpoint: processed=9000 nuggets=0 last_path=frontend/node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js.flow
2025-08-10T17:12:12.786Z - Processing batch: files 9000 to 9049
2025-08-10T17:12:13.077Z - Batch checkpoint: processed=9050 nuggets=0 last_path=frontend/node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js
2025-08-10T17:12:13.080Z - Processing batch: files 9050 to 9099
2025-08-10T17:12:13.214Z - Batch checkpoint: processed=9100 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/eachYearOfInterval/index.js
2025-08-10T17:12:13.216Z - Processing batch: files 9100 to 9149
2025-08-10T17:12:13.357Z - Batch checkpoint: processed=9150 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/endOfQuarter/index.d.ts
2025-08-10T17:12:13.359Z - Processing batch: files 9150 to 9199
2025-08-10T17:12:13.500Z - Batch checkpoint: processed=9200 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/differenceInQuartersWithOptions/desktop.ini
2025-08-10T17:12:13.503Z - Processing batch: files 9200 to 9249
2025-08-10T17:12:13.675Z - Batch checkpoint: processed=9250 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/differenceInSecondsWithOptions/package.json
2025-08-10T17:12:13.678Z - Processing batch: files 9250 to 9299
2025-08-10T17:12:14.024Z - Batch checkpoint: processed=9300 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/formatWithOptions/package.json
2025-08-10T17:12:14.027Z - Processing batch: files 9300 to 9349
2025-08-10T17:12:14.184Z - Batch checkpoint: processed=9350 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/getHours/index.js
2025-08-10T17:12:14.186Z - Processing batch: files 9350 to 9399
2025-08-10T17:12:14.325Z - Batch checkpoint: processed=9400 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/endOfWeek/package.json
2025-08-10T17:12:14.327Z - Processing batch: files 9400 to 9449
2025-08-10T17:12:14.476Z - Batch checkpoint: processed=9450 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/formatISODuration/index.d.ts
2025-08-10T17:12:14.479Z - Processing batch: files 9450 to 9499
2025-08-10T17:12:14.815Z - Batch checkpoint: processed=9500 nuggets=0 last_path=frontend/node_modules/date-fns/esm/endOfISOWeekYear/package.json
2025-08-10T17:12:14.817Z - Processing batch: files 9500 to 9549
2025-08-10T17:12:15.195Z - Batch checkpoint: processed=9550 nuggets=0 last_path=frontend/node_modules/date-fns/esm/endOfYear/index.js
2025-08-10T17:12:15.197Z - Processing batch: files 9550 to 9599
2025-08-10T17:12:15.541Z - Batch checkpoint: processed=9600 nuggets=0 last_path=frontend/node_modules/date-fns/esm/differenceInMinutes/index.d.ts
2025-08-10T17:12:15.544Z - Processing batch: files 9600 to 9649
2025-08-10T17:12:15.913Z - Batch checkpoint: processed=9650 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/compareAsc/index.js.flow
2025-08-10T17:12:15.916Z - Processing batch: files 9650 to 9699
2025-08-10T17:12:16.088Z - Batch checkpoint: processed=9700 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/differenceInCalendarWeeksWithOptions/index.js.flow
2025-08-10T17:12:16.090Z - Processing batch: files 9700 to 9749
2025-08-10T17:12:16.308Z - Batch checkpoint: processed=9750 nuggets=0 last_path=frontend/node_modules/date-fns/esm/formatRFC7231/desktop.ini
2025-08-10T17:12:16.311Z - Processing batch: files 9750 to 9799
2025-08-10T17:12:16.624Z - Batch checkpoint: processed=9800 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/addMonths/desktop.ini
2025-08-10T17:12:16.626Z - Processing batch: files 9800 to 9849
2025-08-10T17:12:16.862Z - Batch checkpoint: processed=9850 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/setDate/desktop.ini
2025-08-10T17:12:16.865Z - Processing batch: files 9850 to 9899
2025-08-10T17:12:17.091Z - Batch checkpoint: processed=9900 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/setSeconds/index.js
2025-08-10T17:12:17.093Z - Processing batch: files 9900 to 9949
2025-08-10T17:12:17.234Z - Batch checkpoint: processed=9950 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/previousDay/package.json
2025-08-10T17:12:17.236Z - Processing batch: files 9950 to 9999
2025-08-10T17:12:17.403Z - Batch checkpoint: processed=10000 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/quartersToMonths/index.js
2025-08-10T17:12:17.405Z - Processing batch: files 10000 to 10049
2025-08-10T17:12:17.554Z - Batch checkpoint: processed=10050 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/subQuarters/index.js
2025-08-10T17:12:17.557Z - Processing batch: files 10050 to 10099
2025-08-10T17:12:17.789Z - Batch checkpoint: processed=10100 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/subYears/index.d.ts
2025-08-10T17:12:17.793Z - Processing batch: files 10100 to 10149
2025-08-10T17:12:17.941Z - Batch checkpoint: processed=10150 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/setWeekYear/desktop.ini
2025-08-10T17:12:17.943Z - Processing batch: files 10150 to 10199
2025-08-10T17:12:18.107Z - Batch checkpoint: processed=10200 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/startOfMinute/package.json
2025-08-10T17:12:18.109Z - Processing batch: files 10200 to 10249
2025-08-10T17:12:18.264Z - Batch checkpoint: processed=10250 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/isAfter/index.js
2025-08-10T17:12:18.267Z - Processing batch: files 10250 to 10299
2025-08-10T17:12:18.433Z - Batch checkpoint: processed=10300 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/isMonday/index.js.flow
2025-08-10T17:12:18.441Z - Processing batch: files 10300 to 10349
2025-08-10T17:12:18.666Z - Batch checkpoint: processed=10350 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/getWeek/index.js.flow
2025-08-10T17:12:18.678Z - Processing batch: files 10350 to 10399
2025-08-10T17:12:18.835Z - Batch checkpoint: processed=10400 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/hoursToMinutes/index.js.flow
2025-08-10T17:12:18.837Z - Processing batch: files 10400 to 10449
2025-08-10T17:12:18.992Z - Batch checkpoint: processed=10450 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/lightFormat/index.d.ts
2025-08-10T17:12:18.995Z - Processing batch: files 10450 to 10499
2025-08-10T17:12:19.137Z - Batch checkpoint: processed=10500 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/monthsToQuarters/index.d.ts
2025-08-10T17:12:19.140Z - Processing batch: files 10500 to 10549
2025-08-10T17:12:19.303Z - Batch checkpoint: processed=10550 nuggets=0 last_path=frontend/node_modules/date-fns/esm/fp/lastDayOfMonth/index.d.ts
2025-08-10T17:12:19.306Z - Processing batch: files 10550 to 10599
2025-08-10T17:12:19.465Z - Batch checkpoint: processed=10600 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/features/tabindex-attr.js
2025-08-10T17:12:19.467Z - Processing batch: files 10600 to 10649
2025-08-10T17:12:19.603Z - Batch checkpoint: processed=10650 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/features/wai-aria.js
2025-08-10T17:12:19.606Z - Processing batch: files 10650 to 10699
2025-08-10T17:12:19.748Z - Batch checkpoint: processed=10700 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/features/namevalue-storage.js
2025-08-10T17:12:19.757Z - Processing batch: files 10700 to 10749
2025-08-10T17:12:19.945Z - Batch checkpoint: processed=10750 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/features/requestanimationframe.js
2025-08-10T17:12:19.948Z - Processing batch: files 10750 to 10799
2025-08-10T17:12:20.237Z - Batch checkpoint: processed=10800 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/regions/IT.js
2025-08-10T17:12:20.240Z - Processing batch: files 10800 to 10849
2025-08-10T17:12:20.377Z - Batch checkpoint: processed=10850 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/regions/MV.js
2025-08-10T17:12:20.380Z - Processing batch: files 10850 to 10899
2025-08-10T17:12:20.581Z - Batch checkpoint: processed=10900 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/regions/AL.js
2025-08-10T17:12:20.589Z - Processing batch: files 10900 to 10949
2025-08-10T17:12:20.753Z - Batch checkpoint: processed=10950 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/regions/CX.js
2025-08-10T17:12:20.755Z - Processing batch: files 10950 to 10999
2025-08-10T17:12:20.915Z - Batch checkpoint: processed=11000 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/features/css-clip-path.js
2025-08-10T17:12:20.918Z - Processing batch: files 11000 to 11049
2025-08-10T17:12:21.072Z - Batch checkpoint: processed=11050 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/features/css-masks.js
2025-08-10T17:12:21.074Z - Processing batch: files 11050 to 11099
2025-08-10T17:12:21.385Z - Batch checkpoint: processed=11100 nuggets=0 last_path=frontend/node_modules/call-bind-apply-helpers/test/desktop.ini
2025-08-10T17:12:21.387Z - Processing batch: files 11100 to 11149
2025-08-10T17:12:21.647Z - Batch checkpoint: processed=11150 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/features/bigint.js
2025-08-10T17:12:21.650Z - Processing batch: files 11150 to 11199
2025-08-10T17:12:21.825Z - Batch checkpoint: processed=11200 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/features/hashchange.js
2025-08-10T17:12:21.827Z - Processing batch: files 11200 to 11249
2025-08-10T17:12:21.978Z - Batch checkpoint: processed=11250 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/features/js-regexp-lookbehind.js
2025-08-10T17:12:21.981Z - Processing batch: files 11250 to 11299
2025-08-10T17:12:22.133Z - Batch checkpoint: processed=11300 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/features/css-touch-action.js
2025-08-10T17:12:22.135Z - Processing batch: files 11300 to 11349
2025-08-10T17:12:22.308Z - Batch checkpoint: processed=11350 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/features/dommatrix.js
2025-08-10T17:12:22.311Z - Processing batch: files 11350 to 11399
2025-08-10T17:12:22.783Z - Batch checkpoint: processed=11400 nuggets=0 last_path=frontend/node_modules/date-fns/eachDayOfInterval/index.d.ts
2025-08-10T17:12:22.786Z - Processing batch: files 11400 to 11449
2025-08-10T17:12:23.250Z - Batch checkpoint: processed=11450 nuggets=0 last_path=frontend/node_modules/date-fns/differenceInCalendarWeeks/index.d.ts
2025-08-10T17:12:23.252Z - Processing batch: files 11450 to 11499
2025-08-10T17:12:23.621Z - Batch checkpoint: processed=11500 nuggets=0 last_path=frontend/node_modules/date-fns/differenceInWeeks/index.d.ts
2025-08-10T17:12:23.624Z - Processing batch: files 11500 to 11549
2025-08-10T17:12:23.992Z - Batch checkpoint: processed=11550 nuggets=0 last_path=frontend/node_modules/date-fns/esm/closestTo/desktop.ini
2025-08-10T17:12:23.995Z - Processing batch: files 11550 to 11599
2025-08-10T17:12:24.380Z - Batch checkpoint: processed=11600 nuggets=0 last_path=frontend/node_modules/date-fns/esm/differenceInCalendarQuarters/index.d.ts
2025-08-10T17:12:24.382Z - Processing batch: files 11600 to 11649
2025-08-10T17:12:24.741Z - Batch checkpoint: processed=11650 nuggets=0 last_path=frontend/node_modules/date-fns/esm/types.js
2025-08-10T17:12:24.743Z - Processing batch: files 11650 to 11699
2025-08-10T17:12:25.191Z - Batch checkpoint: processed=11700 nuggets=0 last_path=frontend/node_modules/date-fns/esm/addSeconds/package.json
2025-08-10T17:12:25.193Z - Processing batch: files 11700 to 11749
2025-08-10T17:12:25.688Z - Batch checkpoint: processed=11750 nuggets=0 last_path=frontend/node_modules/concat-map/example/desktop.ini
2025-08-10T17:12:25.691Z - Processing batch: files 11750 to 11799
2025-08-10T17:12:26.387Z - Batch checkpoint: processed=11800 nuggets=0 last_path=frontend/node_modules/cosmiconfig/dist/types.d.ts
2025-08-10T17:12:26.390Z - Processing batch: files 11800 to 11849
2025-08-10T17:12:26.923Z - Batch checkpoint: processed=11850 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/regions/PW.js
2025-08-10T17:12:26.925Z - Processing batch: files 11850 to 11899
2025-08-10T17:12:27.095Z - Batch checkpoint: processed=11900 nuggets=0 last_path=frontend/node_modules/caniuse-lite/data/regions/TM.js
2025-08-10T17:12:27.098Z - Processing batch: files 11900 to 11949
2025-08-10T17:12:27.398Z - Batch checkpoint: processed=11950 nuggets=0 last_path=frontend/node_modules/date-fns/addDays/index.d.ts
2025-08-10T17:12:27.400Z - Processing batch: files 11950 to 11999
2025-08-10T17:12:27.756Z - Batch checkpoint: processed=12000 nuggets=0 last_path=frontend/node_modules/date-fns/addYears/desktop.ini
2025-08-10T17:12:27.759Z - Processing batch: files 12000 to 12049
2025-08-10T17:12:28.675Z - Batch checkpoint: processed=12050 nuggets=0 last_path=frontend/node_modules/cosmiconfig/node_modules/yaml/package.json
2025-08-10T17:12:28.677Z - Processing batch: files 12050 to 12099
2025-08-10T17:12:30.188Z - Batch checkpoint: processed=12100 nuggets=0 last_path=frontend/node_modules/cross-spawn/package.json
2025-08-10T17:12:30.191Z - Processing batch: files 12100 to 12149
2025-08-10T17:12:30.606Z - Batch checkpoint: processed=12150 nuggets=0 last_path=node_modules/@emotion/utils/dist/emotion-utils.edge-light.cjs.mjs
2025-08-10T17:12:30.609Z - Processing batch: files 12150 to 12199
2025-08-10T17:12:31.229Z - Batch checkpoint: processed=12200 nuggets=0 last_path=node_modules/@esbuild/win32-x64/package.json
2025-08-10T17:12:31.232Z - Processing batch: files 12200 to 12249
2025-08-10T17:12:31.466Z - Batch checkpoint: processed=12250 nuggets=0 last_path=node_modules/@emotion/styled/base/dist/emotion-styled-base.umd.min.js
2025-08-10T17:12:31.471Z - Processing batch: files 12250 to 12299
2025-08-10T17:12:31.988Z - Batch checkpoint: processed=12300 nuggets=0 last_path=node_modules/@inquirer/core/dist/esm/lib/theme.js
2025-08-10T17:12:31.991Z - Processing batch: files 12300 to 12349
2025-08-10T17:12:32.501Z - Batch checkpoint: processed=12350 nuggets=0 last_path=node_modules/@inquirer/core/node_modules/strip-ansi/readme.md
2025-08-10T17:12:32.504Z - Processing batch: files 12350 to 12399
2025-08-10T17:12:33.064Z - Batch checkpoint: processed=12400 nuggets=0 last_path=node_modules/@inquirer/checkbox/desktop.ini
2025-08-10T17:12:33.068Z - Processing batch: files 12400 to 12449
2025-08-10T17:12:33.554Z - Batch checkpoint: processed=12450 nuggets=0 last_path=node_modules/@inquirer/core/dist/commonjs/lib/make-theme.d.ts
2025-08-10T17:12:33.556Z - Processing batch: files 12450 to 12499
2025-08-10T17:12:33.813Z - Batch checkpoint: processed=12500 nuggets=0 last_path=node_modules/@emotion/is-prop-valid/src/index.ts
2025-08-10T17:12:33.816Z - Processing batch: files 12500 to 12549
2025-08-10T17:12:34.262Z - Batch checkpoint: processed=12550 nuggets=0 last_path=node_modules/@emotion/react/dist/emotion-react.development.edge-light.esm.js
2025-08-10T17:12:34.265Z - Processing batch: files 12550 to 12599
2025-08-10T17:12:34.773Z - Batch checkpoint: processed=12600 nuggets=0 last_path=node_modules/@emotion/babel-plugin/src/utils/transform-expression-with-styles.js
2025-08-10T17:12:34.775Z - Processing batch: files 12600 to 12649
2025-08-10T17:12:35.367Z - Batch checkpoint: processed=12650 nuggets=0 last_path=node_modules/@emotion/cache/dist/declarations/src/types.d.ts
2025-08-10T17:12:35.369Z - Processing batch: files 12650 to 12699
2025-08-10T17:12:35.847Z - Batch checkpoint: processed=12700 nuggets=0 last_path=node_modules/@emotion/serialize/dist/emotion-serialize.development.cjs.mjs
2025-08-10T17:12:35.850Z - Processing batch: files 12700 to 12749
2025-08-10T17:12:36.108Z - Batch checkpoint: processed=12750 nuggets=0 last_path=node_modules/@emotion/sheet/README.md
2025-08-10T17:12:36.110Z - Processing batch: files 12750 to 12799
2025-08-10T17:12:36.469Z - Batch checkpoint: processed=12800 nuggets=0 last_path=node_modules/@emotion/react/dist/declarations/src/global.d.ts
2025-08-10T17:12:36.472Z - Processing batch: files 12800 to 12849
2025-08-10T17:12:36.909Z - Batch checkpoint: processed=12850 nuggets=0 last_path=node_modules/@emotion/react/jsx-runtime/dist/emotion-react-jsx-runtime.browser.cjs.js
2025-08-10T17:12:36.911Z - Processing batch: files 12850 to 12899
2025-08-10T17:12:37.568Z - Batch checkpoint: processed=12900 nuggets=0 last_path=node_modules/@modelcontextprotocol/sdk/dist/esm/examples/server/simpleStatelessStreamableHttp.d.ts
2025-08-10T17:12:37.570Z - Processing batch: files 12900 to 12949
2025-08-10T17:12:38.620Z - Batch checkpoint: processed=12950 nuggets=0 last_path=node_modules/@modelcontextprotocol/sdk/dist/esm/server/auth/provider.d.ts.map
2025-08-10T17:12:38.622Z - Processing batch: files 12950 to 12999
2025-08-10T17:12:39.807Z - Batch checkpoint: processed=13000 nuggets=0 last_path=node_modules/@modelcontextprotocol/sdk/dist/cjs/shared/protocol.js.map
2025-08-10T17:12:39.811Z - Processing batch: files 13000 to 13049
2025-08-10T17:12:40.769Z - Batch checkpoint: processed=13050 nuggets=0 last_path=node_modules/@modelcontextprotocol/sdk/dist/esm/client/websocket.js
2025-08-10T17:12:40.773Z - Processing batch: files 13050 to 13099
2025-08-10T17:12:41.480Z - Batch checkpoint: processed=13100 nuggets=0 last_path=node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/.idea/inspectionProfiles/desktop.ini
2025-08-10T17:12:41.483Z - Processing batch: files 13100 to 13149
2025-08-10T17:12:42.568Z - Batch checkpoint: processed=13150 nuggets=0 last_path=node_modules/@modelcontextprotocol/sdk/node_modules/mime-types/mimeScore.js
2025-08-10T17:12:42.573Z - Processing batch: files 13150 to 13199
2025-08-10T17:12:43.585Z - Batch checkpoint: processed=13200 nuggets=0 last_path=node_modules/@modelcontextprotocol/sdk/node_modules/express/lib/request.js
2025-08-10T17:12:43.589Z - Processing batch: files 13200 to 13249
2025-08-10T17:12:44.635Z - Batch checkpoint: processed=13250 nuggets=0 last_path=node_modules/@inquirer/type/dist/commonjs/inquirer.d.ts
2025-08-10T17:12:44.637Z - Processing batch: files 13250 to 13299
2025-08-10T17:12:45.056Z - Batch checkpoint: processed=13300 nuggets=0 last_path=node_modules/@jridgewell/set-array/desktop.ini
2025-08-10T17:12:45.059Z - Processing batch: files 13300 to 13349
2025-08-10T17:12:45.696Z - Batch checkpoint: processed=13350 nuggets=0 last_path=node_modules/@inquirer/figures/dist/esm/index.js
2025-08-10T17:12:45.699Z - Processing batch: files 13350 to 13399
2025-08-10T17:12:46.188Z - Batch checkpoint: processed=13400 nuggets=0 last_path=node_modules/@inquirer/rawlist/dist/commonjs/package.json
2025-08-10T17:12:46.191Z - Processing batch: files 13400 to 13449
2025-08-10T17:12:46.776Z - Batch checkpoint: processed=13450 nuggets=0 last_path=node_modules/@modelcontextprotocol/sdk/dist/cjs/server/auth/clients.js
2025-08-10T17:12:46.779Z - Processing batch: files 13450 to 13499
2025-08-10T17:12:47.763Z - Batch checkpoint: processed=13500 nuggets=0 last_path=node_modules/@modelcontextprotocol/sdk/dist/cjs/server/auth/handlers/token.js
2025-08-10T17:12:47.766Z - Processing batch: files 13500 to 13549
2025-08-10T17:12:48.999Z - Batch checkpoint: processed=13550 nuggets=0 last_path=node_modules/@modelcontextprotocol/sdk/dist/cjs/types.d.ts.map
2025-08-10T17:12:49.003Z - Processing batch: files 13550 to 13599
2025-08-10T17:12:49.737Z - Batch checkpoint: processed=13600 nuggets=0 last_path=node_modules/@modelcontextprotocol/sdk/dist/cjs/examples/client/multipleClientsParallel.js
2025-08-10T17:12:49.739Z - Processing batch: files 13600 to 13649
2025-08-10T17:12:50.617Z - Batch checkpoint: processed=13650 nuggets=0 last_path=node_modules/@babel/core/lib/config/files/import.cjs.map
2025-08-10T17:12:50.620Z - Processing batch: files 13650 to 13699
2025-08-10T17:12:51.378Z - Batch checkpoint: processed=13700 nuggets=0 last_path=node_modules/@babel/core/lib/tools/build-external-helpers.js.map
2025-08-10T17:12:51.382Z - Processing batch: files 13700 to 13749
2025-08-10T17:12:51.741Z - Batch checkpoint: processed=13750 nuggets=0 last_path=node_modules/@anthropic-ai/sdk/_shims/auto/runtime-node.mjs.map
2025-08-10T17:12:51.744Z - Processing batch: files 13750 to 13799
2025-08-10T17:12:52.382Z - Batch checkpoint: processed=13800 nuggets=0 last_path=node_modules/@babel/core/desktop.ini
2025-08-10T17:12:52.384Z - Processing batch: files 13800 to 13849
2025-08-10T17:12:53.027Z - Batch checkpoint: processed=13850 nuggets=0 last_path=node_modules/@babel/helper-string-parser/package.json
2025-08-10T17:12:53.030Z - Processing batch: files 13850 to 13899
2025-08-10T17:12:53.469Z - Batch checkpoint: processed=13900 nuggets=0 last_path=node_modules/@babel/helpers/lib/helpers/arrayLikeToArray.js
2025-08-10T17:12:53.472Z - Processing batch: files 13900 to 13949
2025-08-10T17:12:54.377Z - Batch checkpoint: processed=13950 nuggets=0 last_path=node_modules/@babel/generator/lib/source-map.js.map
2025-08-10T17:12:54.379Z - Processing batch: files 13950 to 13999
2025-08-10T17:12:54.913Z - Batch checkpoint: processed=14000 nuggets=0 last_path=node_modules/@babel/helper-compilation-targets/lib/filter-items.js
2025-08-10T17:12:54.916Z - Processing batch: files 14000 to 14049
2025-08-10T17:12:55.245Z - Batch checkpoint: processed=14050 nuggets=0 last_path=node_modules/@anthropic-ai/sdk/resources/shared.js.map
2025-08-10T17:12:55.247Z - Processing batch: files 14050 to 14099
2025-08-10T17:12:55.928Z - Batch checkpoint: processed=14100 nuggets=0 last_path=node_modules/.bin/update-browserslist-db.cmd
2025-08-10T17:12:55.932Z - Processing batch: files 14100 to 14149
2025-08-10T17:12:56.312Z - Batch checkpoint: processed=14150 nuggets=0 last_path=node_modules/@anthropic-ai/sdk/error.mjs
2025-08-10T17:12:56.314Z - Processing batch: files 14150 to 14199
2025-08-10T17:12:56.897Z - Batch checkpoint: processed=14200 nuggets=0 last_path=node_modules/@anthropic-ai/sdk/src/_shims/web-types.mjs
2025-08-10T17:12:56.899Z - Processing batch: files 14200 to 14249
2025-08-10T17:12:57.269Z - Batch checkpoint: processed=14250 nuggets=0 last_path=node_modules/@anthropic-ai/sdk/_shims/web-types.mjs
2025-08-10T17:12:57.271Z - Processing batch: files 14250 to 14299
2025-08-10T17:12:57.643Z - Batch checkpoint: processed=14300 nuggets=0 last_path=node_modules/@anthropic-ai/sdk/resources/messages/index.mjs
2025-08-10T17:12:57.645Z - Processing batch: files 14300 to 14349
2025-08-10T17:12:58.448Z - Batch checkpoint: processed=14350 nuggets=0 last_path=node_modules/@anthropic-ai/sdk/src/resources/beta/messages/batches.ts
2025-08-10T17:12:58.453Z - Processing batch: files 14350 to 14399
2025-08-10T17:12:58.988Z - Batch checkpoint: processed=14400 nuggets=0 last_path=node_modules/@babel/traverse/lib/path/modification.js
2025-08-10T17:12:58.990Z - Processing batch: files 14400 to 14449
2025-08-10T17:12:59.594Z - Batch checkpoint: processed=14450 nuggets=0 last_path=node_modules/@babel/types/lib/ast-types/generated/index.js
2025-08-10T17:12:59.597Z - Processing batch: files 14450 to 14499
2025-08-10T17:12:59.887Z - Batch checkpoint: processed=14500 nuggets=0 last_path=node_modules/@babel/runtime/helpers/esm/initializerWarningHelper.js
2025-08-10T17:12:59.890Z - Processing batch: files 14500 to 14549
2025-08-10T17:13:00.270Z - Batch checkpoint: processed=14550 nuggets=0 last_path=node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
2025-08-10T17:13:00.273Z - Processing batch: files 14550 to 14599
2025-08-10T17:13:00.533Z - Batch checkpoint: processed=14600 nuggets=0 last_path=node_modules/@babel/types/lib/modifications/prependToMemberExpression.js.map
2025-08-10T17:13:00.536Z - Processing batch: files 14600 to 14649
2025-08-10T17:13:00.886Z - Batch checkpoint: processed=14650 nuggets=0 last_path=node_modules/@babel/types/lib/validators/isImmutable.js
2025-08-10T17:13:00.888Z - Processing batch: files 14650 to 14699
2025-08-10T17:13:01.247Z - Batch checkpoint: processed=14700 nuggets=0 last_path=node_modules/@babel/types/lib/builders/flow/createTypeAnnotationBasedOnTypeof.js.map
2025-08-10T17:13:01.250Z - Processing batch: files 14700 to 14749
2025-08-10T17:13:01.650Z - Batch checkpoint: processed=14750 nuggets=0 last_path=node_modules/@babel/types/lib/converters/toKeyAlias.js
2025-08-10T17:13:01.653Z - Processing batch: files 14750 to 14799
2025-08-10T17:13:01.893Z - Batch checkpoint: processed=14800 nuggets=0 last_path=node_modules/@babel/helpers/lib/helpers/nonIterableSpread.js.map
2025-08-10T17:13:01.896Z - Processing batch: files 14800 to 14849
2025-08-10T17:13:02.240Z - Batch checkpoint: processed=14850 nuggets=0 last_path=node_modules/@babel/helpers/lib/helpers/taggedTemplateLiteral.js.map
2025-08-10T17:13:02.243Z - Processing batch: files 14850 to 14899
2025-08-10T17:13:02.446Z - Batch checkpoint: processed=14900 nuggets=0 last_path=node_modules/@babel/helpers/lib/helpers/classPrivateFieldDestructureSet.js
2025-08-10T17:13:02.450Z - Processing batch: files 14900 to 14949
2025-08-10T17:13:02.754Z - Batch checkpoint: processed=14950 nuggets=0 last_path=node_modules/@babel/runtime/helpers/superPropBase.js
2025-08-10T17:13:02.758Z - Processing batch: files 14950 to 14999
2025-08-10T17:13:03.046Z - Batch checkpoint: processed=15000 nuggets=0 last_path=node_modules/@babel/runtime/helpers/esm/classPrivateFieldGet.js
2025-08-10T17:13:03.048Z - Processing batch: files 15000 to 15049
2025-08-10T17:13:03.365Z - Batch checkpoint: processed=15050 nuggets=0 last_path=node_modules/@babel/plugin-transform-react-jsx-source/lib/index.js
2025-08-10T17:13:03.368Z - Processing batch: files 15050 to 15099
2025-08-10T17:13:03.953Z - Batch checkpoint: processed=15100 nuggets=0 last_path=node_modules/@babel/runtime/helpers/classStaticPrivateMethodGet.js
2025-08-10T17:13:03.956Z - Processing batch: files 15100 to 15149
2025-08-10T17:13:04.135Z - Batch checkpoint: processed=15150 nuggets=0 last_path=node_modules/@mui/icons-material/AirlineSeatLegroomReducedSharp.js
2025-08-10T17:13:04.137Z - Processing batch: files 15150 to 15199
2025-08-10T17:13:04.386Z - Batch checkpoint: processed=15200 nuggets=0 last_path=node_modules/@mui/icons-material/AirplanemodeActiveSharp.js
2025-08-10T17:13:04.388Z - Processing batch: files 15200 to 15249
2025-08-10T17:13:04.549Z - Batch checkpoint: processed=15250 nuggets=0 last_path=node_modules/@mui/icons-material/AdminPanelSettingsRounded.d.ts
2025-08-10T17:13:04.552Z - Processing batch: files 15250 to 15299
2025-08-10T17:13:04.759Z - Batch checkpoint: processed=15300 nuggets=0 last_path=node_modules/@mui/icons-material/AirlineSeatFlatAngledOutlined.d.ts
2025-08-10T17:13:04.763Z - Processing batch: files 15300 to 15349
2025-08-10T17:13:04.952Z - Batch checkpoint: processed=15350 nuggets=0 last_path=node_modules/@mui/icons-material/AlignHorizontalRight.d.ts
2025-08-10T17:13:04.954Z - Processing batch: files 15350 to 15399
2025-08-10T17:13:05.323Z - Batch checkpoint: processed=15400 nuggets=0 last_path=node_modules/@mui/icons-material/AlignVerticalTopOutlined.d.ts
2025-08-10T17:13:05.325Z - Processing batch: files 15400 to 15449
2025-08-10T17:13:05.524Z - Batch checkpoint: processed=15450 nuggets=0 last_path=node_modules/@mui/icons-material/AirplanemodeInactiveSharp.js
2025-08-10T17:13:05.527Z - Processing batch: files 15450 to 15499
2025-08-10T17:13:05.783Z - Batch checkpoint: processed=15500 nuggets=0 last_path=node_modules/@mui/icons-material/AirTwoTone.js
2025-08-10T17:13:05.786Z - Processing batch: files 15500 to 15549
2025-08-10T17:13:06.073Z - Batch checkpoint: processed=15550 nuggets=0 last_path=node_modules/@mui/icons-material/AccountTreeOutlined.js
2025-08-10T17:13:06.077Z - Processing batch: files 15550 to 15599
2025-08-10T17:13:06.351Z - Batch checkpoint: processed=15600 nuggets=0 last_path=node_modules/@mui/icons-material/AddBoxOutlined.js
2025-08-10T17:13:06.353Z - Processing batch: files 15600 to 15649
2025-08-10T17:13:06.561Z - Batch checkpoint: processed=15650 nuggets=0 last_path=node_modules/@mui/icons-material/AccessibleForwardRounded.js
2025-08-10T17:13:06.564Z - Processing batch: files 15650 to 15699
2025-08-10T17:13:06.776Z - Batch checkpoint: processed=15700 nuggets=0 last_path=node_modules/@mui/icons-material/AccountBalanceRounded.js
2025-08-10T17:13:06.779Z - Processing batch: files 15700 to 15749
2025-08-10T17:13:07.001Z - Batch checkpoint: processed=15750 nuggets=0 last_path=node_modules/@mui/icons-material/AddPhotoAlternate.js
2025-08-10T17:13:07.003Z - Processing batch: files 15750 to 15799
2025-08-10T17:13:07.281Z - Batch checkpoint: processed=15800 nuggets=0 last_path=node_modules/@mui/icons-material/AddCommentOutlined.js
2025-08-10T17:13:07.284Z - Processing batch: files 15800 to 15849
2025-08-10T17:13:07.464Z - Batch checkpoint: processed=15850 nuggets=0 last_path=node_modules/@mui/icons-material/AddLocationAlt.js
2025-08-10T17:13:07.467Z - Processing batch: files 15850 to 15899
2025-08-10T17:13:07.650Z - Batch checkpoint: processed=15900 nuggets=0 last_path=node_modules/@mui/icons-material/AssistWalkerOutlined.d.ts
2025-08-10T17:13:07.652Z - Processing batch: files 15900 to 15949
2025-08-10T17:13:07.843Z - Batch checkpoint: processed=15950 nuggets=0 last_path=node_modules/@mui/icons-material/AttachFileSharp.d.ts
2025-08-10T17:13:07.845Z - Processing batch: files 15950 to 15999
2025-08-10T17:13:08.075Z - Batch checkpoint: processed=16000 nuggets=0 last_path=node_modules/@mui/icons-material/AssessmentRounded.js
2025-08-10T17:13:08.077Z - Processing batch: files 16000 to 16049
2025-08-10T17:13:08.292Z - Batch checkpoint: processed=16050 nuggets=0 last_path=node_modules/@mui/icons-material/AssignmentTurnedInTwoTone.js
2025-08-10T17:13:08.295Z - Processing batch: files 16050 to 16099
2025-08-10T17:13:08.535Z - Batch checkpoint: processed=16100 nuggets=0 last_path=node_modules/@mui/icons-material/AutoModeSharp.js
2025-08-10T17:13:08.538Z - Processing batch: files 16100 to 16149
2025-08-10T17:13:08.710Z - Batch checkpoint: processed=16150 nuggets=0 last_path=node_modules/@mui/icons-material/BabyChangingStationRounded.js
2025-08-10T17:13:08.712Z - Processing batch: files 16150 to 16199
2025-08-10T17:13:08.893Z - Batch checkpoint: processed=16200 nuggets=0 last_path=node_modules/@mui/icons-material/AudiotrackSharp.js
2025-08-10T17:13:08.896Z - Processing batch: files 16200 to 16249
2025-08-10T17:13:09.196Z - Batch checkpoint: processed=16250 nuggets=0 last_path=node_modules/@mui/icons-material/AutoAwesomeMosaicTwoTone.js
2025-08-10T17:13:09.198Z - Processing batch: files 16250 to 16299
2025-08-10T17:13:09.403Z - Batch checkpoint: processed=16300 nuggets=0 last_path=node_modules/@mui/icons-material/AppBlockingSharp.js
2025-08-10T17:13:09.405Z - Processing batch: files 16300 to 16349
2025-08-10T17:13:09.607Z - Batch checkpoint: processed=16350 nuggets=0 last_path=node_modules/@mui/icons-material/AppShortcut.js
2025-08-10T17:13:09.611Z - Processing batch: files 16350 to 16399
2025-08-10T17:13:09.777Z - Batch checkpoint: processed=16400 nuggets=0 last_path=node_modules/@mui/icons-material/AlternateEmailRounded.d.ts
2025-08-10T17:13:09.779Z - Processing batch: files 16400 to 16449
2025-08-10T17:13:09.961Z - Batch checkpoint: processed=16450 nuggets=0 last_path=node_modules/@mui/icons-material/Announcement.d.ts
2025-08-10T17:13:09.964Z - Processing batch: files 16450 to 16499
2025-08-10T17:13:10.228Z - Batch checkpoint: processed=16500 nuggets=0 last_path=node_modules/@mui/icons-material/ArrowForwardIosOutlined.d.ts
2025-08-10T17:13:10.230Z - Processing batch: files 16500 to 16549
2025-08-10T17:13:10.449Z - Batch checkpoint: processed=16550 nuggets=0 last_path=node_modules/@mui/icons-material/ArrowRightAltSharp.d.ts
2025-08-10T17:13:10.451Z - Processing batch: files 16550 to 16599
2025-08-10T17:13:10.638Z - Batch checkpoint: processed=16600 nuggets=0 last_path=node_modules/@mui/icons-material/ArrowBackIosNewSharp.js
2025-08-10T17:13:10.640Z - Processing batch: files 16600 to 16649
2025-08-10T17:13:10.817Z - Batch checkpoint: processed=16650 nuggets=0 last_path=node_modules/@mui/icons-material/ArrowCircleUp.js
2025-08-10T17:13:10.826Z - Processing batch: files 16650 to 16699
2025-08-10T17:13:11.078Z - Batch checkpoint: processed=16700 nuggets=0 last_path=node_modules/@mui/base/modern/Snackbar/Snackbar.js
2025-08-10T17:13:11.080Z - Processing batch: files 16700 to 16749
2025-08-10T17:13:11.437Z - Batch checkpoint: processed=16750 nuggets=0 last_path=node_modules/@mui/base/Menu/Menu.d.ts
2025-08-10T17:13:11.439Z - Processing batch: files 16750 to 16799
2025-08-10T17:13:11.718Z - Batch checkpoint: processed=16800 nuggets=0 last_path=node_modules/@mui/base/modern/index.js
2025-08-10T17:13:11.720Z - Processing batch: files 16800 to 16849
2025-08-10T17:13:12.013Z - Batch checkpoint: processed=16850 nuggets=0 last_path=node_modules/@mui/base/modern/useSwitch/useSwitch.types.js
2025-08-10T17:13:12.015Z - Processing batch: files 16850 to 16899
2025-08-10T17:13:12.262Z - Batch checkpoint: processed=16900 nuggets=0 last_path=node_modules/@mui/base/node/Button/buttonClasses.js
2025-08-10T17:13:12.265Z - Processing batch: files 16900 to 16949
2025-08-10T17:13:12.601Z - Batch checkpoint: processed=16950 nuggets=0 last_path=node_modules/@mui/base/modern/unstable_useNumberInput/desktop.ini
2025-08-10T17:13:12.603Z - Processing batch: files 16950 to 16999
2025-08-10T17:13:12.838Z - Batch checkpoint: processed=17000 nuggets=0 last_path=node_modules/@mui/base/modern/useMenu/menuReducer.js
2025-08-10T17:13:12.840Z - Processing batch: files 17000 to 17049
2025-08-10T17:13:13.098Z - Batch checkpoint: processed=17050 nuggets=0 last_path=node_modules/@mui/base/legacy/ClassNameGenerator/desktop.ini
2025-08-10T17:13:13.112Z - Processing batch: files 17050 to 17099
2025-08-10T17:13:13.432Z - Batch checkpoint: processed=17100 nuggets=0 last_path=node_modules/@mui/base/legacy/Menu/index.js
2025-08-10T17:13:13.435Z - Processing batch: files 17100 to 17149
2025-08-10T17:13:14.277Z - Batch checkpoint: processed=17150 nuggets=0 last_path=node_modules/@modelcontextprotocol/sdk/node_modules/raw-body/HISTORY.md
2025-08-10T17:13:14.279Z - Processing batch: files 17150 to 17199
2025-08-10T17:13:14.986Z - Batch checkpoint: processed=17200 nuggets=0 last_path=node_modules/@mui/base/Button/index.d.ts
2025-08-10T17:13:14.989Z - Processing batch: files 17200 to 17249
2025-08-10T17:13:15.273Z - Batch checkpoint: processed=17250 nuggets=0 last_path=node_modules/@mui/base/legacy/unstable_useNumberInput/index.js
2025-08-10T17:13:15.276Z - Processing batch: files 17250 to 17299
2025-08-10T17:13:15.496Z - Batch checkpoint: processed=17300 nuggets=0 last_path=node_modules/@mui/base/legacy/useMenuItem/desktop.ini
2025-08-10T17:13:15.499Z - Processing batch: files 17300 to 17349
2025-08-10T17:13:16.021Z - Batch checkpoint: processed=17350 nuggets=0 last_path=node_modules/@mui/base/legacy/Portal/Portal.js
2025-08-10T17:13:16.023Z - Processing batch: files 17350 to 17399
2025-08-10T17:13:16.266Z - Batch checkpoint: processed=17400 nuggets=0 last_path=node_modules/@mui/base/legacy/TabsList/tabsListClasses.js
2025-08-10T17:13:16.269Z - Processing batch: files 17400 to 17449
2025-08-10T17:13:16.690Z - Batch checkpoint: processed=17450 nuggets=0 last_path=node_modules/@mui/base/useBadge/useBadge.d.ts
2025-08-10T17:13:16.693Z - Processing batch: files 17450 to 17499
2025-08-10T17:13:17.131Z - Batch checkpoint: processed=17500 nuggets=0 last_path=node_modules/@mui/base/useList/listActions.types.js
2025-08-10T17:13:17.133Z - Processing batch: files 17500 to 17549
2025-08-10T17:13:17.702Z - Batch checkpoint: processed=17550 nuggets=0 last_path=node_modules/@mui/base/TabPanel/TabPanel.types.js
2025-08-10T17:13:17.705Z - Processing batch: files 17550 to 17599
2025-08-10T17:13:18.147Z - Batch checkpoint: processed=17600 nuggets=0 last_path=node_modules/@mui/base/utils/omitEventHandlers.js
2025-08-10T17:13:18.150Z - Processing batch: files 17600 to 17649
2025-08-10T17:13:18.426Z - Batch checkpoint: processed=17650 nuggets=0 last_path=node_modules/@mui/icons-material/AccessAlarms.d.ts
2025-08-10T17:13:18.428Z - Processing batch: files 17650 to 17699
2025-08-10T17:13:18.721Z - Batch checkpoint: processed=17700 nuggets=0 last_path=node_modules/@mui/base/useSelect/index.d.ts
2025-08-10T17:13:18.723Z - Processing batch: files 17700 to 17749
2025-08-10T17:13:19.189Z - Batch checkpoint: processed=17750 nuggets=0 last_path=node_modules/@mui/base/useTabPanel/useTabPanel.types.js
2025-08-10T17:13:19.192Z - Processing batch: files 17750 to 17799
2025-08-10T17:13:19.505Z - Batch checkpoint: processed=17800 nuggets=0 last_path=node_modules/@mui/base/node/Unstable_Popup/desktop.ini
2025-08-10T17:13:19.513Z - Processing batch: files 17800 to 17849
2025-08-10T17:13:19.778Z - Batch checkpoint: processed=17850 nuggets=0 last_path=node_modules/@mui/base/node/useList/useListItem.js
2025-08-10T17:13:19.781Z - Processing batch: files 17850 to 17899
2025-08-10T17:13:20.122Z - Batch checkpoint: processed=17900 nuggets=0 last_path=node_modules/@mui/base/node/MenuButton/MenuButton.types.js
2025-08-10T17:13:20.125Z - Processing batch: files 17900 to 17949
2025-08-10T17:13:20.529Z - Batch checkpoint: processed=17950 nuggets=0 last_path=node_modules/@mui/base/node/Snackbar/Snackbar.js
2025-08-10T17:13:20.532Z - Processing batch: files 17950 to 17999
2025-08-10T17:13:20.863Z - Batch checkpoint: processed=18000 nuggets=0 last_path=node_modules/@mui/base/Option/package.json
2025-08-10T17:13:20.865Z - Processing batch: files 18000 to 18049
2025-08-10T17:13:21.267Z - Batch checkpoint: processed=18050 nuggets=0 last_path=node_modules/@mui/base/Snackbar/index.d.ts
2025-08-10T17:13:21.269Z - Processing batch: files 18050 to 18099
2025-08-10T17:13:21.583Z - Batch checkpoint: processed=18100 nuggets=0 last_path=node_modules/@mui/base/node/useMenu/useMenu.types.js
2025-08-10T17:13:21.585Z - Processing batch: files 18100 to 18149
2025-08-10T17:13:21.961Z - Batch checkpoint: processed=18150 nuggets=0 last_path=node_modules/@mui/base/node/useTabs/TabsProvider.js
2025-08-10T17:13:21.963Z - Processing batch: files 18150 to 18199
2025-08-10T17:13:22.254Z - Batch checkpoint: processed=18200 nuggets=0 last_path=frontend/node_modules/react-i18next/dist/commonjs/context.js
2025-08-10T17:13:22.257Z - Processing batch: files 18200 to 18249
2025-08-10T17:13:22.711Z - Batch checkpoint: processed=18250 nuggets=0 last_path=frontend/node_modules/react-is/umd/react-is.development.js
2025-08-10T17:13:22.714Z - Processing batch: files 18250 to 18299
2025-08-10T17:13:23.014Z - Batch checkpoint: processed=18300 nuggets=0 last_path=frontend/node_modules/react-dropzone/examples/theme.css
2025-08-10T17:13:23.017Z - Processing batch: files 18300 to 18349
2025-08-10T17:13:23.761Z - Batch checkpoint: processed=18350 nuggets=0 last_path=frontend/node_modules/react-dropzone/typings/react-dropzone.d.ts
2025-08-10T17:13:23.764Z - Processing batch: files 18350 to 18399
2025-08-10T17:13:24.068Z - Batch checkpoint: processed=18400 nuggets=0 last_path=frontend/node_modules/react-query/lib/core/queryObserver.js
2025-08-10T17:13:24.071Z - Processing batch: files 18400 to 18449
2025-08-10T17:13:24.534Z - Batch checkpoint: processed=18450 nuggets=0 last_path=frontend/node_modules/react-query/es/core/infiniteQueryBehavior.js
2025-08-10T17:13:24.537Z - Processing batch: files 18450 to 18499
2025-08-10T17:13:25.402Z - Batch checkpoint: processed=18500 nuggets=0 last_path=frontend/node_modules/react-query/es/react/useIsMutating.js
2025-08-10T17:13:25.406Z - Processing batch: files 18500 to 18549
2025-08-10T17:13:25.731Z - Batch checkpoint: processed=18550 nuggets=0 last_path=frontend/node_modules/oblivious-set/.github/workflows/desktop.ini
2025-08-10T17:13:25.733Z - Processing batch: files 18550 to 18599
2025-08-10T17:13:26.282Z - Batch checkpoint: processed=18600 nuggets=0 last_path=frontend/node_modules/path-is-absolute/package.json
2025-08-10T17:13:26.285Z - Processing batch: files 18600 to 18649
2025-08-10T17:13:26.789Z - Batch checkpoint: processed=18650 nuggets=0 last_path=frontend/node_modules/minimatch/dist/esm/index.js
2025-08-10T17:13:26.793Z - Processing batch: files 18650 to 18699
2025-08-10T17:13:27.324Z - Batch checkpoint: processed=18700 nuggets=0 last_path=frontend/node_modules/nanoid/non-secure/index.cjs
2025-08-10T17:13:27.327Z - Processing batch: files 18700 to 18749
2025-08-10T17:13:28.042Z - Batch checkpoint: processed=18750 nuggets=0 last_path=frontend/node_modules/react/cjs/react-jsx-dev-runtime.production.min.js
2025-08-10T17:13:28.044Z - Processing batch: files 18750 to 18799
2025-08-10T17:13:29.181Z - Batch checkpoint: processed=18800 nuggets=0 last_path=frontend/node_modules/react-dom/umd/react-dom-test-utils.production.min.js
2025-08-10T17:13:29.183Z - Processing batch: files 18800 to 18849
2025-08-10T17:13:30.487Z - Batch checkpoint: processed=18850 nuggets=0 last_path=frontend/node_modules/picocolors/desktop.ini
2025-08-10T17:13:30.490Z - Processing batch: files 18850 to 18899
2025-08-10T17:13:31.470Z - Batch checkpoint: processed=18900 nuggets=0 last_path=frontend/node_modules/postcss/lib/comment.d.ts
2025-08-10T17:13:31.474Z - Processing batch: files 18900 to 18949
2025-08-10T17:13:32.326Z - Batch checkpoint: processed=18950 nuggets=0 last_path=frontend/node_modules/rollup/dist/es/shared/node-entry.js
2025-08-10T17:13:32.329Z - Processing batch: files 18950 to 18999
2025-08-10T17:13:33.324Z - Batch checkpoint: processed=19000 nuggets=0 last_path=frontend/node_modules/semver/bin/semver.js
2025-08-10T17:13:33.327Z - Processing batch: files 19000 to 19049
2025-08-10T17:13:33.728Z - Batch checkpoint: processed=19050 nuggets=0 last_path=frontend/node_modules/rimraf/dist/commonjs/rimraf-posix.d.ts.map
2025-08-10T17:13:33.730Z - Processing batch: files 19050 to 19099
2025-08-10T17:13:34.300Z - Batch checkpoint: processed=19100 nuggets=0 last_path=frontend/node_modules/rimraf/dist/esm/retry-busy.js.map
2025-08-10T17:13:34.303Z - Processing batch: files 19100 to 19149
2025-08-10T17:13:34.918Z - Batch checkpoint: processed=19150 nuggets=0 last_path=frontend/node_modules/supports-preserve-symlinks-flag/.eslintrc
2025-08-10T17:13:34.921Z - Processing batch: files 19150 to 19199
2025-08-10T17:13:35.495Z - Batch checkpoint: processed=19200 nuggets=0 last_path=frontend/node_modules/ts-retry-promise/dist/timeout.js
2025-08-10T17:13:35.498Z - Processing batch: files 19200 to 19249
2025-08-10T17:13:36.368Z - Batch checkpoint: processed=19250 nuggets=0 last_path=frontend/node_modules/source-map/lib/base64.js
2025-08-10T17:13:36.370Z - Processing batch: files 19250 to 19299
2025-08-10T17:13:37.058Z - Batch checkpoint: processed=19300 nuggets=0 last_path=frontend/node_modules/string-width-cjs/node_modules/emoji-regex/index.js
2025-08-10T17:13:37.061Z - Processing batch: files 19300 to 19349
2025-08-10T17:13:37.504Z - Batch checkpoint: processed=19350 nuggets=0 last_path=frontend/node_modules/resolve/bin/desktop.ini
2025-08-10T17:13:37.508Z - Processing batch: files 19350 to 19399
2025-08-10T17:13:38.025Z - Batch checkpoint: processed=19400 nuggets=0 last_path=frontend/node_modules/react-query/types/ts3.8/index.d.ts
2025-08-10T17:13:38.028Z - Processing batch: files 19400 to 19449
2025-08-10T17:13:38.736Z - Batch checkpoint: processed=19450 nuggets=0 last_path=frontend/node_modules/react-router-dom/desktop.ini
2025-08-10T17:13:38.739Z - Processing batch: files 19450 to 19499
2025-08-10T17:13:39.840Z - Batch checkpoint: processed=19500 nuggets=0 last_path=frontend/node_modules/resolve/test/shadowed_core/desktop.ini
2025-08-10T17:13:39.843Z - Processing batch: files 19500 to 19549
2025-08-10T17:13:40.104Z - Batch checkpoint: processed=19550 nuggets=0 last_path=frontend/node_modules/rimraf/dist/commonjs/rimraf-move-remove.js
2025-08-10T17:13:40.115Z - Processing batch: files 19550 to 19599
2025-08-10T17:13:40.343Z - Batch checkpoint: processed=19600 nuggets=0 last_path=frontend/node_modules/resolve/test/node_path/y/ccc/index.js
2025-08-10T17:13:40.345Z - Processing batch: files 19600 to 19649
2025-08-10T17:13:40.644Z - Batch checkpoint: processed=19650 nuggets=0 last_path=frontend/node_modules/resolve/test/resolver/invalid_main/package.json
2025-08-10T17:13:40.647Z - Processing batch: files 19650 to 19699
2025-08-10T17:13:41.250Z - Batch checkpoint: processed=19700 nuggets=0 last_path=frontend/node_modules/file-selector/src/index.ts
2025-08-10T17:13:41.253Z - Processing batch: files 19700 to 19749
2025-08-10T17:13:41.609Z - Batch checkpoint: processed=19750 nuggets=0 last_path=frontend/node_modules/foreground-child/dist/commonjs/package.json
2025-08-10T17:13:41.611Z - Processing batch: files 19750 to 19799
2025-08-10T17:13:42.010Z - Batch checkpoint: processed=19800 nuggets=0 last_path=frontend/node_modules/es-define-property/CHANGELOG.md
2025-08-10T17:13:42.013Z - Processing batch: files 19800 to 19849
2025-08-10T17:13:42.506Z - Batch checkpoint: processed=19850 nuggets=0 last_path=frontend/node_modules/es-object-atoms/ToObject.js
2025-08-10T17:13:42.510Z - Processing batch: files 19850 to 19899
2025-08-10T17:13:42.920Z - Batch checkpoint: processed=19900 nuggets=0 last_path=frontend/node_modules/framer-motion/dist/es/components/LayoutGroup/index.mjs
2025-08-10T17:13:42.922Z - Processing batch: files 19900 to 19949
2025-08-10T17:13:43.151Z - Batch checkpoint: processed=19950 nuggets=0 last_path=frontend/node_modules/framer-motion/dist/es/frameloop/index-legacy.mjs
2025-08-10T17:13:43.154Z - Processing batch: files 19950 to 19999
2025-08-10T17:13:43.706Z - Batch checkpoint: processed=20000 nuggets=0 last_path=frontend/node_modules/framer-motion/dist/framer-motion.js
2025-08-10T17:13:43.709Z - Processing batch: files 20000 to 20049
2025-08-10T17:13:44.306Z - Batch checkpoint: processed=20050 nuggets=0 last_path=frontend/node_modules/framer-motion/dist/es/animation/optimized-appear/store.mjs
2025-08-10T17:13:44.309Z - Processing batch: files 20050 to 20099
2025-08-10T17:13:44.711Z - Batch checkpoint: processed=20100 nuggets=0 last_path=frontend/node_modules/dom-helpers/cjs/scrollTop.d.ts
2025-08-10T17:13:44.713Z - Processing batch: files 20100 to 20149
2025-08-10T17:13:44.924Z - Batch checkpoint: processed=20150 nuggets=0 last_path=frontend/node_modules/dom-helpers/esm/clear.js
2025-08-10T17:13:44.926Z - Processing batch: files 20150 to 20199
2025-08-10T17:13:45.193Z - Batch checkpoint: processed=20200 nuggets=0 last_path=frontend/node_modules/dom-helpers/cjs/offset.d.ts
2025-08-10T17:13:45.196Z - Processing batch: files 20200 to 20249
2025-08-10T17:13:45.458Z - Batch checkpoint: processed=20250 nuggets=0 last_path=frontend/node_modules/dom-helpers/toggleClass/package.json
2025-08-10T17:13:45.461Z - Processing batch: files 20250 to 20299
2025-08-10T17:13:45.872Z - Batch checkpoint: processed=20300 nuggets=0 last_path=frontend/node_modules/electron-to-chromium/package.json
2025-08-10T17:13:45.874Z - Processing batch: files 20300 to 20349
2025-08-10T17:13:46.393Z - Batch checkpoint: processed=20350 nuggets=0 last_path=frontend/node_modules/dom-helpers/esm/position.js
2025-08-10T17:13:46.396Z - Processing batch: files 20350 to 20399
2025-08-10T17:13:46.633Z - Batch checkpoint: processed=20400 nuggets=0 last_path=frontend/node_modules/dom-helpers/isVisible/desktop.ini
2025-08-10T17:13:46.635Z - Processing batch: files 20400 to 20449
2025-08-10T17:13:47.022Z - Batch checkpoint: processed=20450 nuggets=0 last_path=frontend/node_modules/intl-messageformat/index.js
2025-08-10T17:13:47.024Z - Processing batch: files 20450 to 20499
2025-08-10T17:13:47.954Z - Batch checkpoint: processed=20500 nuggets=0 last_path=frontend/node_modules/isexe/desktop.ini
2025-08-10T17:13:47.956Z - Processing batch: files 20500 to 20549
2025-08-10T17:13:48.432Z - Batch checkpoint: processed=20550 nuggets=0 last_path=frontend/node_modules/has-symbols/test/shams/desktop.ini
2025-08-10T17:13:48.434Z - Processing batch: files 20550 to 20599
2025-08-10T17:13:48.909Z - Batch checkpoint: processed=20600 nuggets=0 last_path=frontend/node_modules/html-parse-stringify/dist/html-parse-stringify.js.map
2025-08-10T17:13:48.911Z - Processing batch: files 20600 to 20649
2025-08-10T17:13:49.352Z - Batch checkpoint: processed=20650 nuggets=0 last_path=frontend/node_modules/match-sorter/README.md
2025-08-10T17:13:49.364Z - Processing batch: files 20650 to 20699
2025-08-10T17:13:50.074Z - Batch checkpoint: processed=20700 nuggets=0 last_path=frontend/node_modules/math-intrinsics/constants/maxValue.js
2025-08-10T17:13:50.077Z - Processing batch: files 20700 to 20749
2025-08-10T17:13:50.861Z - Batch checkpoint: processed=20750 nuggets=0 last_path=frontend/node_modules/isexe/test/basic.js
2025-08-10T17:13:50.863Z - Processing batch: files 20750 to 20799
2025-08-10T17:13:51.525Z - Batch checkpoint: processed=20800 nuggets=0 last_path=frontend/node_modules/json-parse-even-better-errors/CHANGELOG.md
2025-08-10T17:13:51.527Z - Processing batch: files 20800 to 20849
2025-08-10T17:13:52.019Z - Batch checkpoint: processed=20850 nuggets=0 last_path=frontend/node_modules/framer-motion/dist/es/render/svg/utils/transform-origin.mjs
2025-08-10T17:13:52.021Z - Processing batch: files 20850 to 20899
2025-08-10T17:13:52.413Z - Batch checkpoint: processed=20900 nuggets=0 last_path=frontend/node_modules/framer-motion/dist/es/utils/reduced-motion/desktop.ini
2025-08-10T17:13:52.416Z - Processing batch: files 20900 to 20949
2025-08-10T17:13:52.675Z - Batch checkpoint: processed=20950 nuggets=0 last_path=frontend/node_modules/framer-motion/dist/es/projection/styles/desktop.ini
2025-08-10T17:13:52.677Z - Processing batch: files 20950 to 20999
2025-08-10T17:13:52.967Z - Batch checkpoint: processed=21000 nuggets=0 last_path=frontend/node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs
2025-08-10T17:13:52.970Z - Processing batch: files 21000 to 21049
2025-08-10T17:13:53.575Z - Batch checkpoint: processed=21050 nuggets=0 last_path=frontend/node_modules/get-proto/README.md
2025-08-10T17:13:53.578Z - Processing batch: files 21050 to 21099
2025-08-10T17:13:54.172Z - Batch checkpoint: processed=21100 nuggets=0 last_path=frontend/node_modules/framer-motion/node_modules/@emotion/is-prop-valid/dist/is-prop-valid.esm.js
2025-08-10T17:13:54.182Z - Processing batch: files 21100 to 21149
2025-08-10T17:13:54.538Z - Batch checkpoint: processed=21150 nuggets=0 last_path=frontend/node_modules/gensync/index.js
2025-08-10T17:13:54.540Z - Processing batch: files 21150 to 21199
2025-08-10T17:13:55.001Z - Batch checkpoint: processed=21200 nuggets=0 last_path=frontend/venv/Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/desktop.ini
2025-08-10T17:13:55.004Z - Processing batch: files 21200 to 21249
2025-08-10T17:13:55.789Z - Batch checkpoint: processed=21250 nuggets=0 last_path=frontend/venv/Lib/site-packages/setuptools/_distutils/__init__.py
2025-08-10T17:13:55.792Z - Processing batch: files 21250 to 21299
2025-08-10T17:13:56.529Z - Batch checkpoint: processed=21300 nuggets=0 last_path=frontend/venv/Lib/site-packages/setuptools/_deprecation_warning.py
2025-08-10T17:13:56.532Z - Processing batch: files 21300 to 21349
2025-08-10T17:13:56.955Z - Batch checkpoint: processed=21350 nuggets=0 last_path=frontend/venv/Lib/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-311.pyc
2025-08-10T17:13:56.958Z - Processing batch: files 21350 to 21399
2025-08-10T17:13:57.653Z - Batch checkpoint: processed=21400 nuggets=0 last_path=frontend/venv/Lib/site-packages/setuptools/_vendor/packaging/version.py
2025-08-10T17:13:57.657Z - Processing batch: files 21400 to 21449
2025-08-10T17:13:57.984Z - Batch checkpoint: processed=21450 nuggets=0 last_path=frontend/venv/Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-311.pyc
2025-08-10T17:13:57.987Z - Processing batch: files 21450 to 21499
2025-08-10T17:13:58.398Z - Batch checkpoint: processed=21500 nuggets=0 last_path=frontend/venv/Lib/site-packages/setuptools/_distutils/command/__pycache__/py37compat.cpython-311.pyc
2025-08-10T17:13:58.402Z - Processing batch: files 21500 to 21549
2025-08-10T17:13:58.736Z - Batch checkpoint: processed=21550 nuggets=0 last_path=frontend/venv/Lib/site-packages/setuptools/_distutils/__pycache__/util.cpython-311.pyc
2025-08-10T17:13:58.739Z - Processing batch: files 21550 to 21599
2025-08-10T17:13:59.009Z - Batch checkpoint: processed=21600 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/rich/__pycache__/_pick.cpython-311.pyc
2025-08-10T17:13:59.012Z - Processing batch: files 21600 to 21649
2025-08-10T17:13:59.685Z - Batch checkpoint: processed=21650 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/tomli/__pycache__/_re.cpython-311.pyc
2025-08-10T17:13:59.688Z - Processing batch: files 21650 to 21699
2025-08-10T17:14:00.264Z - Batch checkpoint: processed=21700 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/rich/syntax.py
2025-08-10T17:14:00.267Z - Processing batch: files 21700 to 21749
2025-08-10T17:14:00.503Z - Batch checkpoint: processed=21750 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/rich/__pycache__/panel.cpython-311.pyc
2025-08-10T17:14:00.507Z - Processing batch: files 21750 to 21799
2025-08-10T17:14:00.958Z - Batch checkpoint: processed=21800 nuggets=0 last_path=frontend/venv/Lib/site-packages/pkg_resources/_vendor/importlib_resources/__pycache__/simple.cpython-311.pyc
2025-08-10T17:14:00.962Z - Processing batch: files 21800 to 21849
2025-08-10T17:14:01.645Z - Batch checkpoint: processed=21850 nuggets=0 last_path=frontend/venv/Lib/site-packages/pkg_resources/_vendor/packaging/__init__.py
2025-08-10T17:14:01.647Z - Processing batch: files 21850 to 21899
2025-08-10T17:14:02.038Z - Batch checkpoint: processed=21900 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/desktop.ini
2025-08-10T17:14:02.040Z - Processing batch: files 21900 to 21949
2025-08-10T17:14:02.336Z - Batch checkpoint: processed=21950 nuggets=0 last_path=istio/istio-1.20.3/tools/_istioctl
2025-08-10T17:14:02.338Z - Processing batch: files 21950 to 21999
2025-08-10T17:14:03.313Z - Batch checkpoint: processed=22000 nuggets=0 last_path=manifests/charts/gateways/desktop.ini
2025-08-10T17:14:03.316Z - Processing batch: files 22000 to 22049
2025-08-10T17:14:04.094Z - Batch checkpoint: processed=22050 nuggets=0 last_path=istio/istio-1.20.3/samples/helloworld/gen-helloworld.sh
2025-08-10T17:14:04.097Z - Processing batch: files 22050 to 22099
2025-08-10T17:14:04.437Z - Batch checkpoint: processed=22100 nuggets=0 last_path=istio/istio-1.20.3/samples/open-telemetry/loki/otel.yaml
2025-08-10T17:14:04.439Z - Processing batch: files 22100 to 22149
2025-08-10T17:14:04.907Z - Batch checkpoint: processed=22150 nuggets=0 last_path=manifests/profiles/preview.yaml
2025-08-10T17:14:04.909Z - Processing batch: files 22150 to 22199
2025-08-10T17:14:05.351Z - Batch checkpoint: processed=22200 nuggets=0 last_path=node_modules/.bin/node-which.cmd
2025-08-10T17:14:05.354Z - Processing batch: files 22200 to 22249
2025-08-10T17:14:05.952Z - Batch checkpoint: processed=22250 nuggets=0 last_path=manifests/charts/istio-control/istio-discovery/templates/clusterrolebinding.yaml
2025-08-10T17:14:05.954Z - Processing batch: files 22250 to 22299
2025-08-10T17:14:06.717Z - Batch checkpoint: processed=22300 nuggets=0 last_path=manifests/charts/istiod-remote/files/desktop.ini
2025-08-10T17:14:06.721Z - Processing batch: files 22300 to 22349
2025-08-10T17:14:07.401Z - Batch checkpoint: processed=22350 nuggets=0 last_path=istio/istio-1.20.3/manifests/charts/istio-cni/templates/clusterrolebinding.yaml
2025-08-10T17:14:07.404Z - Processing batch: files 22350 to 22399
2025-08-10T17:14:08.212Z - Batch checkpoint: processed=22400 nuggets=0 last_path=istio/istio-1.20.3/manifests/charts/istio-control/istio-discovery/files/desktop.ini
2025-08-10T17:14:08.215Z - Processing batch: files 22400 to 22449
2025-08-10T17:14:08.700Z - Batch checkpoint: processed=22450 nuggets=0 last_path=frontend/venv/Lib/site-packages/setuptools/__pycache__/glob.cpython-311.pyc
2025-08-10T17:14:08.703Z - Processing batch: files 22450 to 22499
2025-08-10T17:14:08.998Z - Batch checkpoint: processed=22500 nuggets=0 last_path=frontend/venv/Scripts/pip.exe
2025-08-10T17:14:09.000Z - Processing batch: files 22500 to 22549
2025-08-10T17:14:09.555Z - Batch checkpoint: processed=22550 nuggets=0 last_path=istio/istio-1.20.3/samples/bookinfo/networking/virtual-service-reviews-jason-v2-v3.yaml
2025-08-10T17:14:09.558Z - Processing batch: files 22550 to 22599
2025-08-10T17:14:10.209Z - Batch checkpoint: processed=22600 nuggets=0 last_path=istio/istio-1.20.3/samples/bookinfo/src/reviews/reviews-wlpcfg/desktop.ini
2025-08-10T17:14:10.212Z - Processing batch: files 22600 to 22649
2025-08-10T17:14:10.781Z - Batch checkpoint: processed=22650 nuggets=0 last_path=istio/istio-1.20.3/manifests/charts/istiod-remote/templates/reader-clusterrolebinding.yaml
2025-08-10T17:14:10.785Z - Processing batch: files 22650 to 22699
2025-08-10T17:14:11.467Z - Batch checkpoint: processed=22700 nuggets=0 last_path=istio/istio-1.20.3/samples/addons/extras/desktop.ini
2025-08-10T17:14:11.470Z - Processing batch: files 22700 to 22749
2025-08-10T17:14:12.499Z - Batch checkpoint: processed=22750 nuggets=0 last_path=frontend/node_modules/yaml/dist/parse/cst-visit.d.ts
2025-08-10T17:14:12.501Z - Processing batch: files 22750 to 22799
2025-08-10T17:14:13.468Z - Batch checkpoint: processed=22800 nuggets=0 last_path=frontend/node_modules/yaml/dist/schema/yaml-1.1/omap.d.ts
2025-08-10T17:14:13.470Z - Processing batch: files 22800 to 22849
2025-08-10T17:14:14.408Z - Batch checkpoint: processed=22850 nuggets=0 last_path=frontend/node_modules/yaml/dist/doc/desktop.ini
2025-08-10T17:14:14.410Z - Processing batch: files 22850 to 22899
2025-08-10T17:14:15.204Z - Batch checkpoint: processed=22900 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_internal/cli/__pycache__/status_codes.cpython-311.pyc
2025-08-10T17:14:15.207Z - Processing batch: files 22900 to 22949
2025-08-10T17:14:15.767Z - Batch checkpoint: processed=22950 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_internal/commands/__pycache__/wheel.cpython-311.pyc
2025-08-10T17:14:15.770Z - Processing batch: files 22950 to 22999
2025-08-10T17:14:16.313Z - Batch checkpoint: processed=23000 nuggets=0 last_path=frontend/src/components/EHP/EHPReview.tsx
2025-08-10T17:14:16.315Z - Processing batch: files 23000 to 23049
2025-08-10T17:14:17.096Z - Batch checkpoint: processed=23050 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/py.typed
2025-08-10T17:14:17.098Z - Processing batch: files 23050 to 23099
2025-08-10T17:14:17.857Z - Batch checkpoint: processed=23100 nuggets=0 last_path=frontend/node_modules/unload/dist/browserify.js
2025-08-10T17:14:17.860Z - Processing batch: files 23100 to 23149
2025-08-10T17:14:22.832Z - Batch checkpoint: processed=23150 nuggets=0 last_path=frontend/node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.development.js
2025-08-10T17:14:22.834Z - Processing batch: files 23150 to 23199
2025-08-10T17:14:23.886Z - Batch checkpoint: processed=23200 nuggets=0 last_path=frontend/node_modules/typescript/lib/lib.es2019.d.ts
2025-08-10T17:14:23.888Z - Processing batch: files 23200 to 23249
2025-08-10T17:14:25.149Z - Batch checkpoint: processed=23250 nuggets=0 last_path=frontend/node_modules/typescript/lib/lib.es2023.d.ts
2025-08-10T17:14:25.152Z - Processing batch: files 23250 to 23299
2025-08-10T17:14:26.131Z - Batch checkpoint: processed=23300 nuggets=0 last_path=frontend/node_modules/wrap-ansi-cjs/node_modules/strip-ansi/package.json
2025-08-10T17:14:26.133Z - Processing batch: files 23300 to 23349
2025-08-10T17:14:26.971Z - Batch checkpoint: processed=23350 nuggets=0 last_path=frontend/node_modules/yaml/browser/dist/compose/util-flow-indent-check.js
2025-08-10T17:14:26.983Z - Processing batch: files 23350 to 23399
2025-08-10T17:14:28.649Z - Batch checkpoint: processed=23400 nuggets=0 last_path=frontend/node_modules/use-sync-external-store/shim/with-selector.js
2025-08-10T17:14:28.651Z - Processing batch: files 23400 to 23449
2025-08-10T17:14:29.470Z - Batch checkpoint: processed=23450 nuggets=0 last_path=frontend/node_modules/void-elements/README.md
2025-08-10T17:14:29.472Z - Processing batch: files 23450 to 23499
2025-08-10T17:14:29.888Z - Batch checkpoint: processed=23500 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/packaging/version.py
2025-08-10T17:14:29.891Z - Processing batch: files 23500 to 23549
2025-08-10T17:14:31.279Z - Batch checkpoint: processed=23550 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/pygments/scanner.py
2025-08-10T17:14:31.282Z - Processing batch: files 23550 to 23599
2025-08-10T17:14:31.806Z - Batch checkpoint: processed=23600 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/colorama/tests/utils.py
2025-08-10T17:14:31.809Z - Processing batch: files 23600 to 23649
2025-08-10T17:14:32.504Z - Batch checkpoint: processed=23650 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/distlib/__pycache__/markers.cpython-311.pyc
2025-08-10T17:14:32.507Z - Processing batch: files 23650 to 23699
2025-08-10T17:14:32.885Z - Batch checkpoint: processed=23700 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/rich/padding.py
2025-08-10T17:14:32.888Z - Processing batch: files 23700 to 23749
2025-08-10T17:14:33.759Z - Batch checkpoint: processed=23750 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/pygments/__pycache__/filter.cpython-311.pyc
2025-08-10T17:14:33.761Z - Processing batch: files 23750 to 23799
2025-08-10T17:14:34.127Z - Batch checkpoint: processed=23800 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/pyproject_hooks/__init__.py
2025-08-10T17:14:34.130Z - Processing batch: files 23800 to 23849
2025-08-10T17:14:34.693Z - Batch checkpoint: processed=23850 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_internal/req/__pycache__/req_set.cpython-311.pyc
2025-08-10T17:14:34.696Z - Processing batch: files 23850 to 23899
2025-08-10T17:14:35.315Z - Batch checkpoint: processed=23900 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_internal/utils/unpacking.py
2025-08-10T17:14:35.318Z - Processing batch: files 23900 to 23949
2025-08-10T17:14:35.888Z - Batch checkpoint: processed=23950 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_internal/models/__init__.py
2025-08-10T17:14:35.892Z - Processing batch: files 23950 to 23999
2025-08-10T17:14:36.408Z - Batch checkpoint: processed=24000 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_internal/operations/build/__pycache__/metadata_legacy.cpython-311.pyc
2025-08-10T17:14:36.410Z - Processing batch: files 24000 to 24049
2025-08-10T17:14:37.098Z - Batch checkpoint: processed=24050 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/chardet/sjisprober.py
2025-08-10T17:14:37.101Z - Processing batch: files 24050 to 24099
2025-08-10T17:14:37.961Z - Batch checkpoint: processed=24100 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/chardet/__pycache__/macromanprober.cpython-311.pyc
2025-08-10T17:14:37.963Z - Processing batch: files 24100 to 24149
2025-08-10T17:14:38.318Z - Batch checkpoint: processed=24150 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_internal/utils/__pycache__/glibc.cpython-311.pyc
2025-08-10T17:14:38.321Z - Processing batch: files 24150 to 24199
2025-08-10T17:14:38.565Z - Batch checkpoint: processed=24200 nuggets=0 last_path=frontend/venv/Lib/site-packages/pip/_vendor/cachecontrol/heuristics.py
2025-08-10T17:14:38.568Z - Processing batch: files 24200 to 24249
2025-08-10T17:14:38.954Z - Batch checkpoint: processed=24250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LoyaltySharp.d.ts
2025-08-10T17:14:38.956Z - Processing batch: files 24250 to 24299
2025-08-10T17:14:39.229Z - Batch checkpoint: processed=24300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LyricsSharp.d.ts
2025-08-10T17:14:39.231Z - Processing batch: files 24300 to 24349
2025-08-10T17:14:39.563Z - Batch checkpoint: processed=24350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Looks4TwoTone.js
2025-08-10T17:14:39.575Z - Processing batch: files 24350 to 24399
2025-08-10T17:14:39.799Z - Batch checkpoint: processed=24400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LoopSharp.js
2025-08-10T17:14:39.801Z - Processing batch: files 24400 to 24449
2025-08-10T17:14:40.065Z - Batch checkpoint: processed=24450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Margin.js
2025-08-10T17:14:40.068Z - Processing batch: files 24450 to 24499
2025-08-10T17:14:40.411Z - Batch checkpoint: processed=24500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/MarkEmailReadRounded.js
2025-08-10T17:14:40.414Z - Processing batch: files 24500 to 24549
2025-08-10T17:14:40.670Z - Batch checkpoint: processed=24550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Man2Outlined.d.ts
2025-08-10T17:14:40.683Z - Processing batch: files 24550 to 24599
2025-08-10T17:14:40.947Z - Batch checkpoint: processed=24600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LocalOfferTwoTone.js
2025-08-10T17:14:40.949Z - Processing batch: files 24600 to 24649
2025-08-10T17:14:41.186Z - Batch checkpoint: processed=24650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LocalPlayOutlined.js
2025-08-10T17:14:41.189Z - Processing batch: files 24650 to 24699
2025-08-10T17:14:41.536Z - Batch checkpoint: processed=24700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LocalFireDepartmentSharp.d.ts
2025-08-10T17:14:41.539Z - Processing batch: files 24700 to 24749
2025-08-10T17:14:41.769Z - Batch checkpoint: processed=24750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LocalHotelSharp.d.ts
2025-08-10T17:14:41.771Z - Processing batch: files 24750 to 24799
2025-08-10T17:14:42.012Z - Batch checkpoint: processed=24800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LockResetRounded.d.ts
2025-08-10T17:14:42.014Z - Processing batch: files 24800 to 24849
2025-08-10T17:14:42.272Z - Batch checkpoint: processed=24850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Looks3Sharp.d.ts
2025-08-10T17:14:42.282Z - Processing batch: files 24850 to 24899
2025-08-10T17:14:42.722Z - Batch checkpoint: processed=24900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LocalShippingTwoTone.d.ts
2025-08-10T17:14:42.725Z - Processing batch: files 24900 to 24949
2025-08-10T17:14:42.965Z - Batch checkpoint: processed=24950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LocationOnTwoTone.d.ts
2025-08-10T17:14:42.968Z - Processing batch: files 24950 to 24999
2025-08-10T17:14:43.204Z - Batch checkpoint: processed=25000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/MonitorWeightOutlined.js
2025-08-10T17:14:43.216Z - Processing batch: files 25000 to 25049
2025-08-10T17:14:43.453Z - Batch checkpoint: processed=25050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/MoodRounded.js
2025-08-10T17:14:43.456Z - Processing batch: files 25050 to 25099
2025-08-10T17:14:43.940Z - Batch checkpoint: processed=25100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ModelTrainingOutlined.d.ts
2025-08-10T17:14:43.943Z - Processing batch: files 25100 to 25149
2025-08-10T17:14:44.318Z - Batch checkpoint: processed=25150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ModeStandbyRounded.d.ts
2025-08-10T17:14:44.320Z - Processing batch: files 25150 to 25199
2025-08-10T17:14:44.635Z - Batch checkpoint: processed=25200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/MovieFilter.d.ts
2025-08-10T17:14:44.637Z - Processing batch: files 25200 to 25249
2025-08-10T17:14:44.930Z - Batch checkpoint: processed=25250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/MuseumRounded.d.ts
2025-08-10T17:14:44.933Z - Processing batch: files 25250 to 25299
2025-08-10T17:14:45.317Z - Batch checkpoint: processed=25300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/MosqueSharp.d.ts
2025-08-10T17:14:45.319Z - Processing batch: files 25300 to 25349
2025-08-10T17:14:45.621Z - Batch checkpoint: processed=25350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/MoveDownRounded.d.ts
2025-08-10T17:14:45.623Z - Processing batch: files 25350 to 25399
2025-08-10T17:14:45.869Z - Batch checkpoint: processed=25400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Memory.d.ts
2025-08-10T17:14:45.871Z - Processing batch: files 25400 to 25449
2025-08-10T17:14:46.219Z - Batch checkpoint: processed=25450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/MediaBluetoothOff.d.ts
2025-08-10T17:14:46.222Z - Processing batch: files 25450 to 25499
2025-08-10T17:14:46.476Z - Batch checkpoint: processed=25500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Medication.d.ts
2025-08-10T17:14:46.480Z - Processing batch: files 25500 to 25549
2025-08-10T17:14:46.796Z - Batch checkpoint: processed=25550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/MobiledataOffSharp.d.ts
2025-08-10T17:14:46.799Z - Processing batch: files 25550 to 25599
2025-08-10T17:14:47.100Z - Batch checkpoint: processed=25600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ModeEditOutlined.d.ts
2025-08-10T17:14:47.103Z - Processing batch: files 25600 to 25649
2025-08-10T17:14:47.479Z - Batch checkpoint: processed=25650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/MicOutlined.js
2025-08-10T17:14:47.482Z - Processing batch: files 25650 to 25699
2025-08-10T17:14:47.729Z - Batch checkpoint: processed=25700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/MiscellaneousServicesSharp.js
2025-08-10T17:14:47.731Z - Processing batch: files 25700 to 25749
2025-08-10T17:14:47.985Z - Batch checkpoint: processed=25750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/InsertDriveFile.js
2025-08-10T17:14:47.987Z - Processing batch: files 25750 to 25799
2025-08-10T17:14:48.243Z - Batch checkpoint: processed=25800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/InsertLinkTwoTone.js
2025-08-10T17:14:48.246Z - Processing batch: files 25800 to 25849
2025-08-10T17:14:48.598Z - Batch checkpoint: processed=25850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ImportantDevices.js
2025-08-10T17:14:48.600Z - Processing batch: files 25850 to 25899
2025-08-10T17:14:50.113Z - Batch checkpoint: processed=25900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ImportContactsRounded.js
2025-08-10T17:14:50.115Z - Processing batch: files 25900 to 25949
2025-08-10T17:14:50.373Z - Batch checkpoint: processed=25950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/InvertColorsOutlined.js
2025-08-10T17:14:50.376Z - Processing batch: files 25950 to 25999
2025-08-10T17:14:50.883Z - Batch checkpoint: processed=26000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Javascript.js
2025-08-10T17:14:50.887Z - Processing batch: files 26000 to 26049
2025-08-10T17:14:51.117Z - Batch checkpoint: processed=26050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/InsertPhotoOutlined.d.ts
2025-08-10T17:14:51.119Z - Processing batch: files 26050 to 26099
2025-08-10T17:14:51.335Z - Batch checkpoint: processed=26100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/InterestsSharp.d.ts
2025-08-10T17:14:51.339Z - Processing batch: files 26100 to 26149
2025-08-10T17:14:51.543Z - Batch checkpoint: processed=26150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/HomeMiniSharp.js
2025-08-10T17:14:51.546Z - Processing batch: files 26150 to 26199
2025-08-10T17:14:52.090Z - Batch checkpoint: processed=26200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/HorizontalSplitOutlined.js
2025-08-10T17:14:52.094Z - Processing batch: files 26200 to 26249
2025-08-10T17:14:52.317Z - Batch checkpoint: processed=26250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/HikingSharp.d.ts
2025-08-10T17:14:52.319Z - Processing batch: files 26250 to 26299
2025-08-10T17:14:52.569Z - Batch checkpoint: processed=26300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/HlsOffRounded.d.ts
2025-08-10T17:14:52.572Z - Processing batch: files 26300 to 26349
2025-08-10T17:14:52.813Z - Batch checkpoint: processed=26350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/IceSkatingSharp.js
2025-08-10T17:14:52.815Z - Processing batch: files 26350 to 26399
2025-08-10T17:14:53.189Z - Batch checkpoint: processed=26400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/HourglassFullSharp.d.ts
2025-08-10T17:14:53.194Z - Processing batch: files 26400 to 26449
2025-08-10T17:14:53.448Z - Batch checkpoint: processed=26450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/HouseSidingSharp.d.ts
2025-08-10T17:14:53.451Z - Processing batch: files 26450 to 26499
2025-08-10T17:14:53.687Z - Batch checkpoint: processed=26500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LibraryBooksTwoTone.js
2025-08-10T17:14:53.689Z - Processing batch: files 26500 to 26549
2025-08-10T17:14:53.960Z - Batch checkpoint: processed=26550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LineAxis.d.ts
2025-08-10T17:14:53.962Z - Processing batch: files 26550 to 26599
2025-08-10T17:14:54.342Z - Batch checkpoint: processed=26600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LeakAdd.d.ts
2025-08-10T17:14:54.354Z - Processing batch: files 26600 to 26649
2025-08-10T17:14:54.590Z - Batch checkpoint: processed=26650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LensRounded.d.ts
2025-08-10T17:14:54.592Z - Processing batch: files 26650 to 26699
2025-08-10T17:14:54.870Z - Batch checkpoint: processed=26700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LocalAirportRounded.js
2025-08-10T17:14:54.873Z - Processing batch: files 26700 to 26749
2025-08-10T17:14:55.249Z - Batch checkpoint: processed=26750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LocalCafeSharp.js
2025-08-10T17:14:55.252Z - Processing batch: files 26750 to 26799
2025-08-10T17:14:55.505Z - Batch checkpoint: processed=26800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LineStyle.d.ts
2025-08-10T17:14:55.508Z - Processing batch: files 26800 to 26849
2025-08-10T17:14:55.773Z - Batch checkpoint: processed=26850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LinkTwoTone.d.ts
2025-08-10T17:14:55.785Z - Processing batch: files 26850 to 26899
2025-08-10T17:14:56.038Z - Batch checkpoint: processed=26900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/KeyboardCapslockTwoTone.js
2025-08-10T17:14:56.040Z - Processing batch: files 26900 to 26949
2025-08-10T17:14:56.398Z - Batch checkpoint: processed=26950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/KeyboardDoubleArrowRightTwoTone.js
2025-08-10T17:14:56.400Z - Processing batch: files 26950 to 26999
2025-08-10T17:14:56.648Z - Batch checkpoint: processed=27000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/KebabDiningSharp.js
2025-08-10T17:14:56.651Z - Processing batch: files 27000 to 27049
2025-08-10T17:14:56.890Z - Batch checkpoint: processed=27050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/KeyboardArrowUpOutlined.js
2025-08-10T17:14:56.893Z - Processing batch: files 27050 to 27099
2025-08-10T17:14:57.146Z - Batch checkpoint: processed=27100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LandscapeOutlined.js
2025-08-10T17:14:57.149Z - Processing batch: files 27100 to 27149
2025-08-10T17:14:57.510Z - Batch checkpoint: processed=27150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LaptopMacOutlined.js
2025-08-10T17:14:57.513Z - Processing batch: files 27150 to 27199
2025-08-10T17:14:57.764Z - Batch checkpoint: processed=27200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/LabelImportantOutlined.js
2025-08-10T17:14:57.767Z - Processing batch: files 27200 to 27249
2025-08-10T17:14:58.020Z - Batch checkpoint: processed=27250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Pix.js
2025-08-10T17:14:58.029Z - Processing batch: files 27250 to 27299
2025-08-10T17:14:58.367Z - Batch checkpoint: processed=27300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PlayArrowTwoTone.js
2025-08-10T17:14:58.370Z - Processing batch: files 27300 to 27349
2025-08-10T17:14:58.637Z - Batch checkpoint: processed=27350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PianoOffTwoTone.d.ts
2025-08-10T17:14:58.640Z - Processing batch: files 27350 to 27399
2025-08-10T17:14:58.902Z - Batch checkpoint: processed=27400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PieChartSharp.d.ts
2025-08-10T17:14:58.905Z - Processing batch: files 27400 to 27449
2025-08-10T17:14:59.155Z - Batch checkpoint: processed=27450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PointOfSale.d.ts
2025-08-10T17:14:59.158Z - Processing batch: files 27450 to 27499
2025-08-10T17:14:59.528Z - Batch checkpoint: processed=27500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PoolRounded.d.ts
2025-08-10T17:14:59.531Z - Processing batch: files 27500 to 27549
2025-08-10T17:14:59.871Z - Batch checkpoint: processed=27550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PlayForWorkSharp.js
2025-08-10T17:14:59.880Z - Processing batch: files 27550 to 27599
2025-08-10T17:15:00.142Z - Batch checkpoint: processed=27600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PlaylistAddSharp.js
2025-08-10T17:15:00.144Z - Processing batch: files 27600 to 27649
2025-08-10T17:15:00.402Z - Batch checkpoint: processed=27650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PhishingSharp.js
2025-08-10T17:15:00.404Z - Processing batch: files 27650 to 27699
2025-08-10T17:15:00.759Z - Batch checkpoint: processed=27700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PhoneEnabledRounded.js
2025-08-10T17:15:00.762Z - Processing batch: files 27700 to 27749
2025-08-10T17:15:01.011Z - Batch checkpoint: processed=27750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PersonOutlineOutlined.js
2025-08-10T17:15:01.014Z - Processing batch: files 27750 to 27799
2025-08-10T17:15:01.279Z - Batch checkpoint: processed=27800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PersonSearch.js
2025-08-10T17:15:01.281Z - Processing batch: files 27800 to 27849
2025-08-10T17:15:01.646Z - Batch checkpoint: processed=27850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PhotoCameraBackRounded.js
2025-08-10T17:15:01.649Z - Processing batch: files 27850 to 27899
2025-08-10T17:15:01.931Z - Batch checkpoint: processed=27900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PhotoSizeSelectActual.js
2025-08-10T17:15:01.933Z - Processing batch: files 27900 to 27949
2025-08-10T17:15:02.238Z - Batch checkpoint: processed=27950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PhonelinkLockTwoTone.d.ts
2025-08-10T17:15:02.240Z - Processing batch: files 27950 to 27999
2025-08-10T17:15:02.490Z - Batch checkpoint: processed=28000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PhoneLockedRounded.d.ts
2025-08-10T17:15:02.494Z - Processing batch: files 28000 to 28049
2025-08-10T17:15:02.850Z - Batch checkpoint: processed=28050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RateReviewTwoTone.js
2025-08-10T17:15:02.853Z - Processing batch: files 28050 to 28099
2025-08-10T17:15:03.112Z - Batch checkpoint: processed=28100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RadarTwoTone.js
2025-08-10T17:15:03.114Z - Processing batch: files 28100 to 28149
2025-08-10T17:15:03.359Z - Batch checkpoint: processed=28150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RamenDiningTwoTone.js
2025-08-10T17:15:03.361Z - Processing batch: files 28150 to 28199
2025-08-10T17:15:03.608Z - Batch checkpoint: processed=28200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RemoveModeratorOutlined.d.ts
2025-08-10T17:15:03.610Z - Processing batch: files 28200 to 28249
2025-08-10T17:15:03.999Z - Batch checkpoint: processed=28250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ReorderRounded.d.ts
2025-08-10T17:15:04.002Z - Processing batch: files 28250 to 28299
2025-08-10T17:15:04.272Z - Batch checkpoint: processed=28300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RedoTwoTone.d.ts
2025-08-10T17:15:04.275Z - Processing batch: files 28300 to 28349
2025-08-10T17:15:04.536Z - Batch checkpoint: processed=28350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RemoveCircleSharp.d.ts
2025-08-10T17:15:04.539Z - Processing batch: files 28350 to 28399
2025-08-10T17:15:04.880Z - Batch checkpoint: processed=28400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PrintDisabled.js
2025-08-10T17:15:04.883Z - Processing batch: files 28400 to 28449
2025-08-10T17:15:05.167Z - Batch checkpoint: processed=28450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PropaneOutlined.js
2025-08-10T17:15:05.170Z - Processing batch: files 28450 to 28499
2025-08-10T17:15:05.429Z - Batch checkpoint: processed=28500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PostAddRounded.d.ts
2025-08-10T17:15:05.432Z - Processing batch: files 28500 to 28549
2025-08-10T17:15:05.677Z - Batch checkpoint: processed=28550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PowerSettingsNew.d.ts
2025-08-10T17:15:05.679Z - Processing batch: files 28550 to 28599
2025-08-10T17:15:06.080Z - Batch checkpoint: processed=28600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/QrCode2TwoTone.d.ts
2025-08-10T17:15:06.084Z - Processing batch: files 28600 to 28649
2025-08-10T17:15:06.382Z - Batch checkpoint: processed=28650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/QuestionAnswerRounded.d.ts
2025-08-10T17:15:06.384Z - Processing batch: files 28650 to 28699
2025-08-10T17:15:06.685Z - Batch checkpoint: processed=28700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PropaneTankRounded.js
2025-08-10T17:15:06.697Z - Processing batch: files 28700 to 28749
2025-08-10T17:15:07.047Z - Batch checkpoint: processed=28750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PublishRounded.js
2025-08-10T17:15:07.049Z - Processing batch: files 28750 to 28799
2025-08-10T17:15:07.452Z - Batch checkpoint: processed=28800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NoMeetingRoomTwoTone.d.ts
2025-08-10T17:15:07.454Z - Processing batch: files 28800 to 28849
2025-08-10T17:15:07.714Z - Batch checkpoint: processed=28850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NorthSharp.d.ts
2025-08-10T17:15:07.717Z - Processing batch: files 28850 to 28899
2025-08-10T17:15:07.965Z - Batch checkpoint: processed=28900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NoCrashRounded.js
2025-08-10T17:15:07.967Z - Processing batch: files 28900 to 28949
2025-08-10T17:15:08.336Z - Batch checkpoint: processed=28950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NotInterestedSharp.d.ts
2025-08-10T17:15:08.339Z - Processing batch: files 28950 to 28999
2025-08-10T17:15:08.611Z - Batch checkpoint: processed=29000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/OfflineBoltSharp.d.ts
2025-08-10T17:15:08.613Z - Processing batch: files 29000 to 29049
2025-08-10T17:15:08.863Z - Batch checkpoint: processed=29050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NoteAltSharp.js
2025-08-10T17:15:08.865Z - Processing batch: files 29050 to 29099
2025-08-10T17:15:09.151Z - Batch checkpoint: processed=29100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NotificationsActive.js
2025-08-10T17:15:09.154Z - Processing batch: files 29100 to 29149
2025-08-10T17:15:09.545Z - Batch checkpoint: processed=29150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NetworkCellOutlined.js
2025-08-10T17:15:09.548Z - Processing batch: files 29150 to 29199
2025-08-10T17:15:09.839Z - Batch checkpoint: processed=29200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NetworkWifi2BarSharp.js
2025-08-10T17:15:09.842Z - Processing batch: files 29200 to 29249
2025-08-10T17:15:10.131Z - Batch checkpoint: processed=29250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NatureTwoTone.d.ts
2025-08-10T17:15:10.133Z - Processing batch: files 29250 to 29299
2025-08-10T17:15:10.407Z - Batch checkpoint: processed=29300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NearbyErrorSharp.d.ts
2025-08-10T17:15:10.410Z - Processing batch: files 29300 to 29349
2025-08-10T17:15:10.781Z - Batch checkpoint: processed=29350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NineKSharp.d.ts
2025-08-10T17:15:10.783Z - Processing batch: files 29350 to 29399
2025-08-10T17:15:11.049Z - Batch checkpoint: processed=29400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NineteenMp.d.ts
2025-08-10T17:15:11.051Z - Processing batch: files 29400 to 29449
2025-08-10T17:15:11.304Z - Batch checkpoint: processed=29450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NetworkWifiOutlined.d.ts
2025-08-10T17:15:11.306Z - Processing batch: files 29450 to 29499
2025-08-10T17:15:11.698Z - Batch checkpoint: processed=29500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/NextWeek.d.ts
2025-08-10T17:15:11.700Z - Processing batch: files 29500 to 29549
2025-08-10T17:15:11.964Z - Batch checkpoint: processed=29550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PauseRounded.d.ts
2025-08-10T17:15:11.966Z - Processing batch: files 29550 to 29599
2025-08-10T17:15:12.243Z - Batch checkpoint: processed=29600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Pentagon.d.ts
2025-08-10T17:15:12.245Z - Processing batch: files 29600 to 29649
2025-08-10T17:15:12.616Z - Batch checkpoint: processed=29650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ParkRounded.js
2025-08-10T17:15:12.618Z - Processing batch: files 29650 to 29699
2025-08-10T17:15:12.983Z - Batch checkpoint: processed=29700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PauseCircleOutline.js
2025-08-10T17:15:12.986Z - Processing batch: files 29700 to 29749
2025-08-10T17:15:13.298Z - Batch checkpoint: processed=29750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Person3.js
2025-08-10T17:15:13.300Z - Processing batch: files 29750 to 29799
2025-08-10T17:15:13.559Z - Batch checkpoint: processed=29800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PersonAddDisabledTwoTone.js
2025-08-10T17:15:13.563Z - Processing batch: files 29800 to 29849
2025-08-10T17:15:13.931Z - Batch checkpoint: processed=29850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PermPhoneMsgOutlined.js
2025-08-10T17:15:13.933Z - Processing batch: files 29850 to 29899
2025-08-10T17:15:14.217Z - Batch checkpoint: processed=29900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/OpenInNewTwoTone.d.ts
2025-08-10T17:15:14.219Z - Processing batch: files 29900 to 29949
2025-08-10T17:15:14.495Z - Batch checkpoint: processed=29950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/OutboxSharp.d.ts
2025-08-10T17:15:14.497Z - Processing batch: files 29950 to 29999
2025-08-10T17:15:14.761Z - Batch checkpoint: processed=30000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/OndemandVideoTwoTone.d.ts
2025-08-10T17:15:14.764Z - Processing batch: files 30000 to 30049
2025-08-10T17:15:15.128Z - Batch checkpoint: processed=30050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/OpenInBrowser.d.ts
2025-08-10T17:15:15.138Z - Processing batch: files 30050 to 30099
2025-08-10T17:15:15.415Z - Batch checkpoint: processed=30100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PanoramaVerticalSelectOutlined.js
2025-08-10T17:15:15.418Z - Processing batch: files 30100 to 30149
2025-08-10T17:15:15.676Z - Batch checkpoint: processed=30150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PanToolAltRounded.js
2025-08-10T17:15:15.686Z - Processing batch: files 30150 to 30199
2025-08-10T17:15:15.960Z - Batch checkpoint: processed=30200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PagesSharp.d.ts
2025-08-10T17:15:15.963Z - Processing batch: files 30200 to 30249
2025-08-10T17:15:16.358Z - Batch checkpoint: processed=30250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/PaletteTwoTone.d.ts
2025-08-10T17:15:16.361Z - Processing batch: files 30250 to 30299
2025-08-10T17:15:16.619Z - Batch checkpoint: processed=30300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DescriptionSharp.js
2025-08-10T17:15:16.622Z - Processing batch: files 30300 to 30349
2025-08-10T17:15:16.892Z - Batch checkpoint: processed=30350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DesktopMacTwoTone.d.ts
2025-08-10T17:15:16.895Z - Processing batch: files 30350 to 30399
2025-08-10T17:15:17.278Z - Batch checkpoint: processed=30400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DehazeSharp.js
2025-08-10T17:15:17.281Z - Processing batch: files 30400 to 30449
2025-08-10T17:15:17.574Z - Batch checkpoint: processed=30450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DeliveryDiningSharp.js
2025-08-10T17:15:17.577Z - Processing batch: files 30450 to 30499
2025-08-10T17:15:17.869Z - Batch checkpoint: processed=30500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DifferenceTwoTone.d.ts
2025-08-10T17:15:17.872Z - Processing batch: files 30500 to 30549
2025-08-10T17:15:18.179Z - Batch checkpoint: processed=30550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DirectionsBoatSharp.d.ts
2025-08-10T17:15:18.182Z - Processing batch: files 30550 to 30599
2025-08-10T17:15:18.579Z - Batch checkpoint: processed=30600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DeviceHub.js
2025-08-10T17:15:18.581Z - Processing batch: files 30600 to 30649
2025-08-10T17:15:18.852Z - Batch checkpoint: processed=30650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DeviceThermostatRounded.js
2025-08-10T17:15:18.854Z - Processing batch: files 30650 to 30699
2025-08-10T17:15:19.303Z - Batch checkpoint: processed=30700 nuggets=0 last_path=frontend/node_modules/@mui/base/useTabsList/useTabsList.js
2025-08-10T17:15:19.306Z - Processing batch: files 30700 to 30749
2025-08-10T17:15:19.884Z - Batch checkpoint: processed=30750 nuggets=0 last_path=frontend/node_modules/@mui/base/useDropdown/useDropdown.types.d.ts
2025-08-10T17:15:19.887Z - Processing batch: files 30750 to 30799
2025-08-10T17:15:20.381Z - Batch checkpoint: processed=30800 nuggets=0 last_path=frontend/node_modules/@mui/base/useMenuButton/useMenuButton.d.ts
2025-08-10T17:15:20.383Z - Processing batch: files 30800 to 30849
2025-08-10T17:15:20.837Z - Batch checkpoint: processed=30850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DatasetLinkedSharp.d.ts
2025-08-10T17:15:20.840Z - Processing batch: files 30850 to 30899
2025-08-10T17:15:21.153Z - Batch checkpoint: processed=30900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DeckRounded.d.ts
2025-08-10T17:15:21.156Z - Processing batch: files 30900 to 30949
2025-08-10T17:15:21.675Z - Batch checkpoint: processed=30950 nuggets=0 last_path=frontend/node_modules/@mui/core-downloads-tracker/LICENSE
2025-08-10T17:15:21.677Z - Processing batch: files 30950 to 30999
2025-08-10T17:15:22.078Z - Batch checkpoint: processed=31000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DataObjectTwoTone.js
2025-08-10T17:15:22.081Z - Processing batch: files 31000 to 31049
2025-08-10T17:15:22.392Z - Batch checkpoint: processed=31050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DownloadingOutlined.d.ts
2025-08-10T17:15:22.395Z - Processing batch: files 31050 to 31099
2025-08-10T17:15:22.800Z - Batch checkpoint: processed=31100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DrawRounded.d.ts
2025-08-10T17:15:22.802Z - Processing batch: files 31100 to 31149
2025-08-10T17:15:23.088Z - Batch checkpoint: processed=31150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DoNotTouchRounded.js
2025-08-10T17:15:23.091Z - Processing batch: files 31150 to 31199
2025-08-10T17:15:23.434Z - Batch checkpoint: processed=31200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DoorBackSharp.js
2025-08-10T17:15:23.438Z - Processing batch: files 31200 to 31249
2025-08-10T17:15:23.781Z - Batch checkpoint: processed=31250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Earbuds.js
2025-08-10T17:15:23.784Z - Processing batch: files 31250 to 31299
2025-08-10T17:15:24.156Z - Batch checkpoint: processed=31300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/EdgesensorLowRounded.js
2025-08-10T17:15:24.159Z - Processing batch: files 31300 to 31349
2025-08-10T17:15:24.442Z - Batch checkpoint: processed=31350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DriveEtaRounded.d.ts
2025-08-10T17:15:24.445Z - Processing batch: files 31350 to 31399
2025-08-10T17:15:24.715Z - Batch checkpoint: processed=31400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DuoSharp.d.ts
2025-08-10T17:15:24.717Z - Processing batch: files 31400 to 31449
2025-08-10T17:15:25.101Z - Batch checkpoint: processed=31450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DirtyLens.d.ts
2025-08-10T17:15:25.103Z - Processing batch: files 31450 to 31499
2025-08-10T17:15:25.384Z - Batch checkpoint: processed=31500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DiscountTwoTone.d.ts
2025-08-10T17:15:25.386Z - Processing batch: files 31500 to 31549
2025-08-10T17:15:25.663Z - Batch checkpoint: processed=31550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DirectionsCarTwoTone.d.ts
2025-08-10T17:15:25.666Z - Processing batch: files 31550 to 31599
2025-08-10T17:15:25.932Z - Batch checkpoint: processed=31600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DoneAllSharp.d.ts
2025-08-10T17:15:25.935Z - Processing batch: files 31600 to 31649
2025-08-10T17:15:26.323Z - Batch checkpoint: processed=31650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DoNotDisturbOnSharp.d.ts
2025-08-10T17:15:26.325Z - Processing batch: files 31650 to 31699
2025-08-10T17:15:26.600Z - Batch checkpoint: processed=31700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DockTwoTone.d.ts
2025-08-10T17:15:26.602Z - Processing batch: files 31700 to 31749
2025-08-10T17:15:26.881Z - Batch checkpoint: processed=31750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/DoDisturbTwoTone.d.ts
2025-08-10T17:15:26.883Z - Processing batch: files 31750 to 31799
2025-08-10T17:15:27.299Z - Batch checkpoint: processed=31800 nuggets=0 last_path=frontend/node_modules/@mui/base/node/TabsList/desktop.ini
2025-08-10T17:15:27.301Z - Processing batch: files 31800 to 31849
2025-08-10T17:15:27.652Z - Batch checkpoint: processed=31850 nuggets=0 last_path=frontend/node_modules/@mui/base/node/useDropdown/useDropdown.types.js
2025-08-10T17:15:27.655Z - Processing batch: files 31850 to 31899
2025-08-10T17:15:27.982Z - Batch checkpoint: processed=31900 nuggets=0 last_path=frontend/node_modules/@mui/base/node/MenuButton/desktop.ini
2025-08-10T17:15:27.985Z - Processing batch: files 31900 to 31949
2025-08-10T17:15:28.349Z - Batch checkpoint: processed=31950 nuggets=0 last_path=frontend/node_modules/@mui/base/node/Portal/index.js
2025-08-10T17:15:28.352Z - Processing batch: files 31950 to 31999
2025-08-10T17:15:28.791Z - Batch checkpoint: processed=32000 nuggets=0 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/ClassNameGenerator/index.d.ts
2025-08-10T17:15:28.793Z - Processing batch: files 32000 to 32049
2025-08-10T17:15:29.226Z - Batch checkpoint: processed=32050 nuggets=0 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/createChainedFunction/package.json
2025-08-10T17:15:29.229Z - Processing batch: files 32050 to 32099
2025-08-10T17:15:29.598Z - Batch checkpoint: processed=32100 nuggets=0 last_path=frontend/node_modules/@mui/base/node/useList/useListItem.types.js
2025-08-10T17:15:29.601Z - Processing batch: files 32100 to 32149
2025-08-10T17:15:30.055Z - Batch checkpoint: processed=32150 nuggets=0 last_path=frontend/node_modules/@mui/base/node/useSwitch/useSwitch.js
2025-08-10T17:15:30.057Z - Processing batch: files 32150 to 32199
2025-08-10T17:15:30.404Z - Batch checkpoint: processed=32200 nuggets=0 last_path=frontend/node_modules/@mui/base/modern/composeClasses/index.js
2025-08-10T17:15:30.406Z - Processing batch: files 32200 to 32249
2025-08-10T17:15:30.696Z - Batch checkpoint: processed=32250 nuggets=0 last_path=frontend/node_modules/@mui/base/modern/Popper/desktop.ini
2025-08-10T17:15:30.698Z - Processing batch: files 32250 to 32299
2025-08-10T17:15:31.097Z - Batch checkpoint: processed=32300 nuggets=0 last_path=frontend/node_modules/@mui/base/FormControl/package.json
2025-08-10T17:15:31.100Z - Processing batch: files 32300 to 32349
2025-08-10T17:15:31.711Z - Batch checkpoint: processed=32350 nuggets=0 last_path=frontend/node_modules/@mui/base/MenuItem/MenuItem.d.ts
2025-08-10T17:15:31.713Z - Processing batch: files 32350 to 32399
2025-08-10T17:15:32.049Z - Batch checkpoint: processed=32400 nuggets=0 last_path=frontend/node_modules/@mui/base/modern/useMenuItem/useMenuItemContextStabilizer.js
2025-08-10T17:15:32.053Z - Processing batch: files 32400 to 32449
2025-08-10T17:15:32.391Z - Batch checkpoint: processed=32450 nuggets=0 last_path=frontend/node_modules/@mui/base/modern/useList/useListItem.types.js
2025-08-10T17:15:32.393Z - Processing batch: files 32450 to 32499
2025-08-10T17:15:32.820Z - Batch checkpoint: processed=32500 nuggets=0 last_path=frontend/node_modules/@mui/base/modern/useDropdown/index.js
2025-08-10T17:15:32.823Z - Processing batch: files 32500 to 32549
2025-08-10T17:15:33.217Z - Batch checkpoint: processed=32550 nuggets=0 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/useOnMount/index.d.ts
2025-08-10T17:15:33.220Z - Processing batch: files 32550 to 32599
2025-08-10T17:15:33.577Z - Batch checkpoint: processed=32600 nuggets=0 last_path=frontend/node_modules/@mui/base/NoSsr/index.d.ts
2025-08-10T17:15:33.580Z - Processing batch: files 32600 to 32649
2025-08-10T17:15:34.003Z - Batch checkpoint: processed=32650 nuggets=0 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/ownerDocument/ownerDocument.d.ts
2025-08-10T17:15:34.006Z - Processing batch: files 32650 to 32699
2025-08-10T17:15:34.411Z - Batch checkpoint: processed=32700 nuggets=0 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/useEnhancedEffect/useEnhancedEffect.js
2025-08-10T17:15:34.413Z - Processing batch: files 32700 to 32749
2025-08-10T17:15:34.815Z - Batch checkpoint: processed=32750 nuggets=0 last_path=frontend/node_modules/@mui/base/Unstable_NumberInput/NumberInput.types.d.ts
2025-08-10T17:15:34.817Z - Processing batch: files 32750 to 32799
2025-08-10T17:15:35.364Z - Batch checkpoint: processed=32800 nuggets=0 last_path=frontend/node_modules/@mui/base/unstable_useNumberInput/useNumberInput.types.js
2025-08-10T17:15:35.367Z - Processing batch: files 32800 to 32849
2025-08-10T17:15:36.036Z - Batch checkpoint: processed=32850 nuggets=0 last_path=frontend/node_modules/@mui/base/Slider/Slider.types.d.ts
2025-08-10T17:15:36.040Z - Processing batch: files 32850 to 32899
2025-08-10T17:15:36.549Z - Batch checkpoint: processed=32900 nuggets=0 last_path=frontend/node_modules/@mui/base/Tab/tabClasses.d.ts
2025-08-10T17:15:36.647Z - Processing batch: files 32900 to 32949
2025-08-10T17:15:37.125Z - Batch checkpoint: processed=32950 nuggets=0 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/esm/unsupportedProp/desktop.ini
2025-08-10T17:15:37.128Z - Processing batch: files 32950 to 32999
2025-08-10T17:15:37.526Z - Batch checkpoint: processed=33000 nuggets=0 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/exactProp/exactProp.js
2025-08-10T17:15:37.528Z - Processing batch: files 33000 to 33049
2025-08-10T17:15:37.819Z - Batch checkpoint: processed=33050 nuggets=0 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/esm/chainPropTypes/index.js
2025-08-10T17:15:37.822Z - Processing batch: files 33050 to 33099
2025-08-10T17:15:38.199Z - Batch checkpoint: processed=33100 nuggets=0 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/esm/getReactNodeRef/getReactNodeRef.js
2025-08-10T17:15:38.201Z - Processing batch: files 33100 to 33149
2025-08-10T17:15:38.513Z - Batch checkpoint: processed=33150 nuggets=0 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/modern/getReactNodeRef/getReactNodeRef.js
2025-08-10T17:15:38.524Z - Processing batch: files 33150 to 33199
2025-08-10T17:15:38.939Z - Batch checkpoint: processed=33200 nuggets=0 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/modern/setRef/desktop.ini
2025-08-10T17:15:38.942Z - Processing batch: files 33200 to 33249
2025-08-10T17:15:39.220Z - Batch checkpoint: processed=33250 nuggets=0 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/getValidReactChildren/index.d.ts
2025-08-10T17:15:39.222Z - Processing batch: files 33250 to 33299
2025-08-10T17:15:39.660Z - Batch checkpoint: processed=33300 nuggets=0 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/modern/chainPropTypes/index.js
2025-08-10T17:15:39.677Z - Processing batch: files 33300 to 33349
2025-08-10T17:15:39.980Z - Batch checkpoint: processed=33350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FormatTextdirectionRToLRounded.d.ts
2025-08-10T17:15:39.982Z - Processing batch: files 33350 to 33399
2025-08-10T17:15:40.439Z - Batch checkpoint: processed=33400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FormatIndentDecreaseSharp.d.ts
2025-08-10T17:15:40.441Z - Processing batch: files 33400 to 33449
2025-08-10T17:15:40.734Z - Batch checkpoint: processed=33450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FormatListBulletedRounded.d.ts
2025-08-10T17:15:40.737Z - Processing batch: files 33450 to 33499
2025-08-10T17:15:41.141Z - Batch checkpoint: processed=33500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FourMpRounded.d.ts
2025-08-10T17:15:41.144Z - Processing batch: files 33500 to 33549
2025-08-10T17:15:41.456Z - Batch checkpoint: processed=33550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/GamepadSharp.d.ts
2025-08-10T17:15:41.459Z - Processing batch: files 33550 to 33599
2025-08-10T17:15:41.870Z - Batch checkpoint: processed=33600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Forward5Outlined.js
2025-08-10T17:15:41.873Z - Processing batch: files 33600 to 33649
2025-08-10T17:15:42.163Z - Batch checkpoint: processed=33650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FourGMobiledataTwoTone.js
2025-08-10T17:15:42.165Z - Processing batch: files 33650 to 33699
2025-08-10T17:15:42.626Z - Batch checkpoint: processed=33700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FluorescentSharp.js
2025-08-10T17:15:42.629Z - Processing batch: files 33700 to 33749
2025-08-10T17:15:42.936Z - Batch checkpoint: processed=33750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FmdGoodTwoTone.js
2025-08-10T17:15:42.939Z - Processing batch: files 33750 to 33799
2025-08-10T17:15:43.352Z - Batch checkpoint: processed=33800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FlashOn.d.ts
2025-08-10T17:15:43.356Z - Processing batch: files 33800 to 33849
2025-08-10T17:15:43.669Z - Batch checkpoint: processed=33850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FlightTakeoffOutlined.d.ts
2025-08-10T17:15:43.672Z - Processing batch: files 33850 to 33899
2025-08-10T17:15:44.091Z - Batch checkpoint: processed=33900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FoodBankTwoTone.d.ts
2025-08-10T17:15:44.094Z - Processing batch: files 33900 to 33949
2025-08-10T17:15:44.390Z - Batch checkpoint: processed=33950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FormatAlignJustifyTwoTone.d.ts
2025-08-10T17:15:44.393Z - Processing batch: files 33950 to 33999
2025-08-10T17:15:44.834Z - Batch checkpoint: processed=34000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FolderOutlined.d.ts
2025-08-10T17:15:44.836Z - Processing batch: files 34000 to 34049
2025-08-10T17:15:45.231Z - Batch checkpoint: processed=34050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FontDownloadOff.d.ts
2025-08-10T17:15:45.234Z - Processing batch: files 34050 to 34099
2025-08-10T17:15:45.566Z - Batch checkpoint: processed=34100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/HdrOnSelectSharp.js
2025-08-10T17:15:45.568Z - Processing batch: files 34100 to 34149
2025-08-10T17:15:45.967Z - Batch checkpoint: processed=34150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Headphones.js
2025-08-10T17:15:45.970Z - Processing batch: files 34150 to 34199
2025-08-10T17:15:46.284Z - Batch checkpoint: processed=34200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Handshake.d.ts
2025-08-10T17:15:46.297Z - Processing batch: files 34200 to 34249
2025-08-10T17:15:46.719Z - Batch checkpoint: processed=34250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Hexagon.js
2025-08-10T17:15:46.721Z - Processing batch: files 34250 to 34299
2025-08-10T17:15:47.030Z - Batch checkpoint: processed=34300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/HighlightOffOutlined.js
2025-08-10T17:15:47.034Z - Processing batch: files 34300 to 34349
2025-08-10T17:15:47.478Z - Batch checkpoint: processed=34350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/HealingTwoTone.d.ts
2025-08-10T17:15:47.480Z - Processing batch: files 34350 to 34399
2025-08-10T17:15:47.777Z - Batch checkpoint: processed=34400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/HeatPumpTwoTone.d.ts
2025-08-10T17:15:47.779Z - Processing batch: files 34400 to 34449
2025-08-10T17:15:48.203Z - Batch checkpoint: processed=34450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/GppGoodRounded.d.ts
2025-08-10T17:15:48.206Z - Processing batch: files 34450 to 34499
2025-08-10T17:15:48.516Z - Batch checkpoint: processed=34500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/GradeRounded.d.ts
2025-08-10T17:15:48.518Z - Processing batch: files 34500 to 34549
2025-08-10T17:15:48.943Z - Batch checkpoint: processed=34550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/GavelTwoTone.d.ts
2025-08-10T17:15:48.946Z - Processing batch: files 34550 to 34599
2025-08-10T17:15:49.257Z - Batch checkpoint: processed=34600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/GirlTwoTone.d.ts
2025-08-10T17:15:49.269Z - Processing batch: files 34600 to 34649
2025-08-10T17:15:49.721Z - Batch checkpoint: processed=34650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/GridOnTwoTone.d.ts
2025-08-10T17:15:49.724Z - Processing batch: files 34650 to 34699
2025-08-10T17:15:50.027Z - Batch checkpoint: processed=34700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Groups3.d.ts
2025-08-10T17:15:50.030Z - Processing batch: files 34700 to 34749
2025-08-10T17:15:50.457Z - Batch checkpoint: processed=34750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Gradient.js
2025-08-10T17:15:50.460Z - Processing batch: files 34750 to 34799
2025-08-10T17:15:50.770Z - Batch checkpoint: processed=34800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/GrassRounded.js
2025-08-10T17:15:50.784Z - Processing batch: files 34800 to 34849
2025-08-10T17:15:51.246Z - Batch checkpoint: processed=34850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/EuroSharp.d.ts
2025-08-10T17:15:51.250Z - Processing batch: files 34850 to 34899
2025-08-10T17:15:51.589Z - Batch checkpoint: processed=34900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/EventSeatOutlined.d.ts
2025-08-10T17:15:51.591Z - Processing batch: files 34900 to 34949
2025-08-10T17:15:52.020Z - Batch checkpoint: processed=34950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Engineering.d.ts
2025-08-10T17:15:52.023Z - Processing batch: files 34950 to 34999
2025-08-10T17:15:52.436Z - Batch checkpoint: processed=35000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ErrorOutlineRounded.d.ts
2025-08-10T17:15:52.438Z - Processing batch: files 35000 to 35049
2025-08-10T17:15:52.853Z - Batch checkpoint: processed=35050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Face2Sharp.d.ts
2025-08-10T17:15:52.856Z - Processing batch: files 35050 to 35099
2025-08-10T17:15:53.314Z - Batch checkpoint: processed=35100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ExpandMoreSharp.d.ts
2025-08-10T17:15:53.316Z - Processing batch: files 35100 to 35149
2025-08-10T17:15:53.725Z - Batch checkpoint: processed=35150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ExposureTwoTone.d.ts
2025-08-10T17:15:53.739Z - Processing batch: files 35150 to 35199
2025-08-10T17:15:54.184Z - Batch checkpoint: processed=35200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/EightteenMp.d.ts
2025-08-10T17:15:54.186Z - Processing batch: files 35200 to 35249
2025-08-10T17:15:54.525Z - Batch checkpoint: processed=35250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ElectricBike.d.ts
2025-08-10T17:15:54.527Z - Processing batch: files 35250 to 35299
2025-08-10T17:15:54.965Z - Batch checkpoint: processed=35300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/EditRoad.js
2025-08-10T17:15:54.968Z - Processing batch: files 35300 to 35349
2025-08-10T17:15:55.289Z - Batch checkpoint: processed=35350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/EightK.js
2025-08-10T17:15:55.291Z - Processing batch: files 35350 to 35399
2025-08-10T17:15:55.738Z - Batch checkpoint: processed=35400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/EmojiFlagsRounded.js
2025-08-10T17:15:55.739Z - Processing batch: files 35400 to 35449
2025-08-10T17:15:56.050Z - Batch checkpoint: processed=35450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/EmojiPeopleTwoTone.js
2025-08-10T17:15:56.053Z - Processing batch: files 35450 to 35499
2025-08-10T17:15:56.503Z - Batch checkpoint: processed=35500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ElectricRickshawTwoTone.d.ts
2025-08-10T17:15:56.505Z - Processing batch: files 35500 to 35549
2025-08-10T17:15:56.827Z - Batch checkpoint: processed=35550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ElevatorOutlined.d.ts
2025-08-10T17:15:56.829Z - Processing batch: files 35550 to 35599
2025-08-10T17:15:57.256Z - Batch checkpoint: processed=35600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FilterListTwoTone.d.ts
2025-08-10T17:15:57.259Z - Processing batch: files 35600 to 35649
2025-08-10T17:15:57.574Z - Batch checkpoint: processed=35650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FilterVintageTwoTone.d.ts
2025-08-10T17:15:57.576Z - Processing batch: files 35650 to 35699
2025-08-10T17:15:58.010Z - Batch checkpoint: processed=35700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Filter9Plus.js
2025-08-10T17:15:58.022Z - Processing batch: files 35700 to 35749
2025-08-10T17:15:58.328Z - Batch checkpoint: processed=35750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FilterDrama.js
2025-08-10T17:15:58.331Z - Processing batch: files 35750 to 35799
2025-08-10T17:15:58.799Z - Batch checkpoint: processed=35800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FivteenMpTwoTone.js
2025-08-10T17:15:58.802Z - Processing batch: files 35800 to 35849
2025-08-10T17:15:59.115Z - Batch checkpoint: processed=35850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FlashAutoOutlined.js
2025-08-10T17:15:59.118Z - Processing batch: files 35850 to 35899
2025-08-10T17:15:59.560Z - Batch checkpoint: processed=35900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FireplaceSharp.d.ts
2025-08-10T17:15:59.562Z - Processing batch: files 35900 to 35949
2025-08-10T17:15:59.991Z - Batch checkpoint: processed=35950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FitScreenSharp.d.ts
2025-08-10T17:15:59.994Z - Processing batch: files 35950 to 35999
2025-08-10T17:16:00.440Z - Batch checkpoint: processed=36000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FiberManualRecord.js
2025-08-10T17:16:00.443Z - Processing batch: files 36000 to 36049
2025-08-10T17:16:00.903Z - Batch checkpoint: processed=36050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Fastfood.d.ts
2025-08-10T17:16:00.906Z - Processing batch: files 36050 to 36099
2025-08-10T17:16:01.224Z - Batch checkpoint: processed=36100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FavoriteBorderSharp.d.ts
2025-08-10T17:16:01.227Z - Processing batch: files 36100 to 36149
2025-08-10T17:16:01.748Z - Batch checkpoint: processed=36150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Filter2Sharp.d.ts
2025-08-10T17:16:01.750Z - Processing batch: files 36150 to 36199
2025-08-10T17:16:02.158Z - Batch checkpoint: processed=36200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Filter8TwoTone.d.ts
2025-08-10T17:16:02.173Z - Processing batch: files 36200 to 36249
2025-08-10T17:16:02.668Z - Batch checkpoint: processed=36250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FileDownloadDone.d.ts
2025-08-10T17:16:02.670Z - Processing batch: files 36250 to 36299
2025-08-10T17:16:03.030Z - Batch checkpoint: processed=36300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/FilePresent.d.ts
2025-08-10T17:16:03.032Z - Processing batch: files 36300 to 36349
2025-08-10T17:16:03.543Z - Batch checkpoint: processed=36350 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/IconButton/index.js
2025-08-10T17:16:03.545Z - Processing batch: files 36350 to 36399
2025-08-10T17:16:03.897Z - Batch checkpoint: processed=36400 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/InputLabel/InputLabel.js
2025-08-10T17:16:03.900Z - Processing batch: files 36400 to 36449
2025-08-10T17:16:04.426Z - Batch checkpoint: processed=36450 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/Collapse/index.js
2025-08-10T17:16:04.428Z - Processing batch: files 36450 to 36499
2025-08-10T17:16:04.903Z - Batch checkpoint: processed=36500 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/DialogContentText/dialogContentTextClasses.js
2025-08-10T17:16:04.906Z - Processing batch: files 36500 to 36549
2025-08-10T17:16:05.554Z - Batch checkpoint: processed=36550 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/PaginationItem/PaginationItem.js
2025-08-10T17:16:05.556Z - Processing batch: files 36550 to 36599
2025-08-10T17:16:05.983Z - Batch checkpoint: processed=36600 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/Slide/Slide.js
2025-08-10T17:16:05.985Z - Processing batch: files 36600 to 36649
2025-08-10T17:16:06.476Z - Batch checkpoint: processed=36650 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/List/index.js
2025-08-10T17:16:06.478Z - Processing batch: files 36650 to 36699
2025-08-10T17:16:07.163Z - Batch checkpoint: processed=36700 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/Modal/index.js
2025-08-10T17:16:07.166Z - Processing batch: files 36700 to 36749
2025-08-10T17:16:07.572Z - Batch checkpoint: processed=36750 nuggets=0 last_path=frontend/node_modules/@mui/material/legacy/utils/useEnhancedEffect.js
2025-08-10T17:16:07.574Z - Processing batch: files 36750 to 36799
2025-08-10T17:16:08.331Z - Batch checkpoint: processed=36800 nuggets=0 last_path=frontend/node_modules/@mui/material/ListItemAvatar/index.d.ts
2025-08-10T17:16:08.334Z - Processing batch: files 36800 to 36849
2025-08-10T17:16:08.740Z - Batch checkpoint: processed=36850 nuggets=0 last_path=frontend/node_modules/@mui/material/legacy/useMediaQuery/useMediaQueryTheme.js
2025-08-10T17:16:08.743Z - Processing batch: files 36850 to 36899
2025-08-10T17:16:09.245Z - Batch checkpoint: processed=36900 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/Breadcrumbs/BreadcrumbCollapsed.js
2025-08-10T17:16:09.247Z - Processing batch: files 36900 to 36949
2025-08-10T17:16:09.734Z - Batch checkpoint: processed=36950 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/CardHeader/CardHeader.js
2025-08-10T17:16:09.737Z - Processing batch: files 36950 to 36999
2025-08-10T17:16:10.323Z - Batch checkpoint: processed=37000 nuggets=0 last_path=frontend/node_modules/@mui/material/Menu/menuClasses.js
2025-08-10T17:16:10.334Z - Processing batch: files 37000 to 37049
2025-08-10T17:16:10.985Z - Batch checkpoint: processed=37050 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/AccordionSummary/index.js
2025-08-10T17:16:10.987Z - Processing batch: files 37050 to 37099
2025-08-10T17:16:11.610Z - Batch checkpoint: processed=37100 nuggets=0 last_path=frontend/node_modules/@mui/material/node/internal/switchBaseClasses.js
2025-08-10T17:16:11.612Z - Processing batch: files 37100 to 37149
2025-08-10T17:16:11.995Z - Batch checkpoint: processed=37150 nuggets=0 last_path=frontend/node_modules/@mui/material/node/ListItemButton/desktop.ini
2025-08-10T17:16:11.998Z - Processing batch: files 37150 to 37199
2025-08-10T17:16:12.470Z - Batch checkpoint: processed=37200 nuggets=0 last_path=frontend/node_modules/@mui/material/node/Fab/desktop.ini
2025-08-10T17:16:12.472Z - Processing batch: files 37200 to 37249
2025-08-10T17:16:12.869Z - Batch checkpoint: processed=37250 nuggets=0 last_path=frontend/node_modules/@mui/material/node/Hidden/Hidden.js
2025-08-10T17:16:12.872Z - Processing batch: files 37250 to 37299
2025-08-10T17:16:13.391Z - Batch checkpoint: processed=37300 nuggets=0 last_path=frontend/node_modules/@mui/material/node/Select/index.js
2025-08-10T17:16:13.394Z - Processing batch: files 37300 to 37349
2025-08-10T17:16:13.977Z - Batch checkpoint: processed=37350 nuggets=0 last_path=frontend/node_modules/@mui/material/node/StepButton/desktop.ini
2025-08-10T17:16:13.980Z - Processing batch: files 37350 to 37399
2025-08-10T17:16:14.382Z - Batch checkpoint: processed=37400 nuggets=0 last_path=frontend/node_modules/@mui/material/node/ListItemIcon/listItemIconClasses.js
2025-08-10T17:16:14.384Z - Processing batch: files 37400 to 37449
2025-08-10T17:16:15.021Z - Batch checkpoint: processed=37450 nuggets=0 last_path=frontend/node_modules/@mui/material/node/NoSsr/NoSsr.js
2025-08-10T17:16:15.024Z - Processing batch: files 37450 to 37499
2025-08-10T17:16:15.438Z - Batch checkpoint: processed=37500 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/TableSortLabel/tableSortLabelClasses.js
2025-08-10T17:16:15.441Z - Processing batch: files 37500 to 37549
2025-08-10T17:16:15.880Z - Batch checkpoint: processed=37550 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/useAutocomplete/desktop.ini
2025-08-10T17:16:15.882Z - Processing batch: files 37550 to 37599
2025-08-10T17:16:16.321Z - Batch checkpoint: processed=37600 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/StyledEngineProvider/index.js
2025-08-10T17:16:16.324Z - Processing batch: files 37600 to 37649
2025-08-10T17:16:16.800Z - Batch checkpoint: processed=37650 nuggets=0 last_path=frontend/node_modules/@mui/material/modern/SwipeableDrawer/SwipeArea.js
2025-08-10T17:16:16.803Z - Processing batch: files 37650 to 37699
2025-08-10T17:16:17.201Z - Batch checkpoint: processed=37700 nuggets=0 last_path=frontend/node_modules/@mui/material/node/ButtonBase/TouchRipple.js
2025-08-10T17:16:17.203Z - Processing batch: files 37700 to 37749
2025-08-10T17:16:17.691Z - Batch checkpoint: processed=37750 nuggets=0 last_path=frontend/node_modules/@mui/material/NativeSelect/package.json
2025-08-10T17:16:17.693Z - Processing batch: files 37750 to 37799
2025-08-10T17:16:18.187Z - Batch checkpoint: processed=37800 nuggets=0 last_path=frontend/node_modules/@mui/material/node/Badge/desktop.ini
2025-08-10T17:16:18.190Z - Processing batch: files 37800 to 37849
2025-08-10T17:16:18.730Z - Batch checkpoint: processed=37850 nuggets=0 last_path=frontend/node_modules/@mui/material/CardActionArea/package.json
2025-08-10T17:16:18.734Z - Processing batch: files 37850 to 37899
2025-08-10T17:16:19.341Z - Batch checkpoint: processed=37900 nuggets=0 last_path=frontend/node_modules/@mui/material/CircularProgress/CircularProgress.js
2025-08-10T17:16:19.344Z - Processing batch: files 37900 to 37949
2025-08-10T17:16:20.107Z - Batch checkpoint: processed=37950 nuggets=0 last_path=frontend/node_modules/@mui/material/Badge/useBadge.d.ts
2025-08-10T17:16:20.110Z - Processing batch: files 37950 to 37999
2025-08-10T17:16:20.812Z - Batch checkpoint: processed=38000 nuggets=0 last_path=frontend/node_modules/@mui/material/ButtonBase/touchRippleClasses.d.ts
2025-08-10T17:16:20.815Z - Processing batch: files 38000 to 38049
2025-08-10T17:16:21.491Z - Batch checkpoint: processed=38050 nuggets=0 last_path=frontend/node_modules/@mui/material/Fab/fabClasses.js
2025-08-10T17:16:21.494Z - Processing batch: files 38050 to 38099
2025-08-10T17:16:22.241Z - Batch checkpoint: processed=38100 nuggets=0 last_path=frontend/node_modules/@mui/material/FormGroup/index.d.ts
2025-08-10T17:16:22.245Z - Processing batch: files 38100 to 38149
2025-08-10T17:16:22.779Z - Batch checkpoint: processed=38150 nuggets=0 last_path=frontend/node_modules/@mui/material/colors/package.json
2025-08-10T17:16:22.781Z - Processing batch: files 38150 to 38199
2025-08-10T17:16:23.368Z - Batch checkpoint: processed=38200 nuggets=0 last_path=frontend/node_modules/@mui/material/Container/containerClasses.js
2025-08-10T17:16:23.370Z - Processing batch: files 38200 to 38249
2025-08-10T17:16:23.761Z - Batch checkpoint: processed=38250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Window.d.ts
2025-08-10T17:16:23.765Z - Processing batch: files 38250 to 38299
2025-08-10T17:16:24.237Z - Batch checkpoint: processed=38300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Woman2Sharp.d.ts
2025-08-10T17:16:24.239Z - Processing batch: files 38300 to 38349
2025-08-10T17:16:24.660Z - Batch checkpoint: processed=38350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/WifiChannelSharp.d.ts
2025-08-10T17:16:24.663Z - Processing batch: files 38350 to 38399
2025-08-10T17:16:25.187Z - Batch checkpoint: processed=38400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/WifiTetheringError.d.ts
2025-08-10T17:16:25.191Z - Processing batch: files 38400 to 38449
2025-08-10T17:16:25.959Z - Batch checkpoint: processed=38450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ZoomOutTwoTone.d.ts
2025-08-10T17:16:25.962Z - Processing batch: files 38450 to 38499
2025-08-10T17:16:26.702Z - Batch checkpoint: processed=38500 nuggets=0 last_path=frontend/node_modules/@mui/material/AccordionSummary/accordionSummaryClasses.js
2025-08-10T17:16:26.704Z - Processing batch: files 38500 to 38549
2025-08-10T17:16:27.029Z - Batch checkpoint: processed=38550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Workspaces.js
2025-08-10T17:16:27.031Z - Processing batch: files 38550 to 38599
2025-08-10T17:16:27.502Z - Batch checkpoint: processed=38600 nuggets=0 last_path=frontend/node_modules/@mui/material/FormHelperText/index.js
2025-08-10T17:16:27.504Z - Processing batch: files 38600 to 38649
2025-08-10T17:16:27.841Z - Batch checkpoint: processed=38650 nuggets=0 last_path=frontend/node_modules/@mui/material/legacy/Modal/useModal.types.js
2025-08-10T17:16:27.844Z - Processing batch: files 38650 to 38699
2025-08-10T17:16:28.498Z - Batch checkpoint: processed=38700 nuggets=0 last_path=frontend/node_modules/@mui/material/legacy/Hidden/hiddenCssClasses.js
2025-08-10T17:16:28.518Z - Processing batch: files 38700 to 38749
2025-08-10T17:16:29.053Z - Batch checkpoint: processed=38750 nuggets=0 last_path=frontend/node_modules/@mui/material/legacy/InputBase/InputBase.js
2025-08-10T17:16:29.065Z - Processing batch: files 38750 to 38799
2025-08-10T17:16:29.423Z - Batch checkpoint: processed=38800 nuggets=0 last_path=frontend/node_modules/@mui/material/legacy/StyledEngineProvider/index.js
2025-08-10T17:16:29.425Z - Processing batch: files 38800 to 38849
2025-08-10T17:16:29.936Z - Batch checkpoint: processed=38850 nuggets=0 last_path=frontend/node_modules/@mui/material/legacy/TableContainer/index.js
2025-08-10T17:16:29.938Z - Processing batch: files 38850 to 38899
2025-08-10T17:16:30.340Z - Batch checkpoint: processed=38900 nuggets=0 last_path=frontend/node_modules/@mui/material/legacy/Select/index.js
2025-08-10T17:16:30.342Z - Processing batch: files 38900 to 38949
2025-08-10T17:16:30.863Z - Batch checkpoint: processed=38950 nuggets=0 last_path=frontend/node_modules/@mui/material/legacy/SpeedDialIcon/index.js
2025-08-10T17:16:30.874Z - Processing batch: files 38950 to 38999
2025-08-10T17:16:31.410Z - Batch checkpoint: processed=39000 nuggets=0 last_path=frontend/node_modules/@mui/material/internal/switchBaseClasses.d.ts
2025-08-10T17:16:31.413Z - Processing batch: files 39000 to 39049
2025-08-10T17:16:32.037Z - Batch checkpoint: processed=39050 nuggets=0 last_path=frontend/node_modules/@mui/material/internal/svg-icons/InfoOutlined.js
2025-08-10T17:16:32.040Z - Processing batch: files 39050 to 39099
2025-08-10T17:16:32.559Z - Batch checkpoint: processed=39100 nuggets=0 last_path=frontend/node_modules/@mui/material/FormLabel/package.json
2025-08-10T17:16:32.563Z - Processing batch: files 39100 to 39149
2025-08-10T17:16:33.238Z - Batch checkpoint: processed=39150 nuggets=0 last_path=frontend/node_modules/@mui/material/Icon/Icon.d.ts
2025-08-10T17:16:33.241Z - Processing batch: files 39150 to 39199
2025-08-10T17:16:33.687Z - Batch checkpoint: processed=39200 nuggets=0 last_path=frontend/node_modules/@mui/material/legacy/CircularProgress/index.js
2025-08-10T17:16:33.689Z - Processing batch: files 39200 to 39249
2025-08-10T17:16:34.199Z - Batch checkpoint: processed=39250 nuggets=0 last_path=frontend/node_modules/@mui/material/legacy/DialogContent/index.js
2025-08-10T17:16:34.210Z - Processing batch: files 39250 to 39299
2025-08-10T17:16:34.638Z - Batch checkpoint: processed=39300 nuggets=0 last_path=frontend/node_modules/@mui/material/legacy/Backdrop/Backdrop.js
2025-08-10T17:16:34.641Z - Processing batch: files 39300 to 39349
2025-08-10T17:16:35.227Z - Batch checkpoint: processed=39350 nuggets=0 last_path=frontend/node_modules/@mui/material/legacy/CardActionArea/desktop.ini
2025-08-10T17:16:35.230Z - Processing batch: files 39350 to 39399
2025-08-10T17:16:35.785Z - Batch checkpoint: processed=39400 nuggets=0 last_path=frontend/node_modules/@mui/utils/modern/useLazyRef/useLazyRef.js
2025-08-10T17:16:35.788Z - Processing batch: files 39400 to 39449
2025-08-10T17:16:36.185Z - Batch checkpoint: processed=39450 nuggets=0 last_path=frontend/node_modules/@mui/utils/ponyfillGlobal/ponyfillGlobal.d.ts
2025-08-10T17:16:36.188Z - Processing batch: files 39450 to 39499
2025-08-10T17:16:36.651Z - Batch checkpoint: processed=39500 nuggets=0 last_path=frontend/node_modules/@mui/utils/modern/resolveProps/resolveProps.js
2025-08-10T17:16:36.654Z - Processing batch: files 39500 to 39549
2025-08-10T17:16:37.029Z - Batch checkpoint: processed=39550 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/AdapterLuxon/AdapterLuxon.js
2025-08-10T17:16:37.032Z - Processing batch: files 39550 to 39599
2025-08-10T17:16:37.985Z - Batch checkpoint: processed=39600 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/DateCalendar/PickersFadeTransitionGroup.js
2025-08-10T17:16:37.988Z - Processing batch: files 39600 to 39649
2025-08-10T17:16:38.608Z - Batch checkpoint: processed=39650 nuggets=0 last_path=frontend/node_modules/@mui/utils/useEventCallback/useEventCallback.d.ts
2025-08-10T17:16:38.610Z - Processing batch: files 39650 to 39699
2025-08-10T17:16:39.203Z - Batch checkpoint: processed=39700 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/CHANGELOG.md
2025-08-10T17:16:39.207Z - Processing batch: files 39700 to 39749
2025-08-10T17:16:39.602Z - Batch checkpoint: processed=39750 nuggets=0 last_path=frontend/node_modules/@mui/utils/generateUtilityClasses/index.d.ts
2025-08-10T17:16:39.604Z - Processing batch: files 39750 to 39799
2025-08-10T17:16:40.110Z - Batch checkpoint: processed=39800 nuggets=0 last_path=frontend/node_modules/@mui/utils/integerPropType/package.json
2025-08-10T17:16:40.112Z - Processing batch: files 39800 to 39849
2025-08-10T17:16:40.433Z - Batch checkpoint: processed=39850 nuggets=0 last_path=frontend/node_modules/@mui/utils/esm/isMuiElement/isMuiElement.js
2025-08-10T17:16:40.436Z - Processing batch: files 39850 to 39899
2025-08-10T17:16:40.914Z - Batch checkpoint: processed=39900 nuggets=0 last_path=frontend/node_modules/@mui/utils/esm/setRef/index.js
2025-08-10T17:16:40.918Z - Processing batch: files 39900 to 39949
2025-08-10T17:16:41.364Z - Batch checkpoint: processed=39950 nuggets=0 last_path=frontend/node_modules/@mui/utils/legacy/resolveComponentProps/index.js
2025-08-10T17:16:41.366Z - Processing batch: files 39950 to 39999
2025-08-10T17:16:41.686Z - Batch checkpoint: processed=40000 nuggets=0 last_path=frontend/node_modules/@mui/utils/legacy/useSlotProps/desktop.ini
2025-08-10T17:16:41.689Z - Processing batch: files 40000 to 40049
2025-08-10T17:16:42.145Z - Batch checkpoint: processed=40050 nuggets=0 last_path=frontend/node_modules/@mui/utils/isMuiElement/isMuiElement.d.ts
2025-08-10T17:16:42.149Z - Processing batch: files 40050 to 40099
2025-08-10T17:16:42.480Z - Batch checkpoint: processed=40100 nuggets=0 last_path=frontend/node_modules/@mui/utils/legacy/formatMuiErrorMessage/index.js
2025-08-10T17:16:42.482Z - Processing batch: files 40100 to 40149
2025-08-10T17:16:43.050Z - Batch checkpoint: processed=40150 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/legacy/locales/mk.js
2025-08-10T17:16:43.053Z - Processing batch: files 40150 to 40199
2025-08-10T17:16:43.512Z - Batch checkpoint: processed=40200 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/legacy/MultiSectionDigitalClock/index.js
2025-08-10T17:16:43.515Z - Processing batch: files 40200 to 40249
2025-08-10T17:16:44.039Z - Batch checkpoint: processed=40250 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/legacy/internals/components/PickersArrowSwitcher/index.js
2025-08-10T17:16:44.041Z - Processing batch: files 40250 to 40299
2025-08-10T17:16:44.631Z - Batch checkpoint: processed=40300 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/legacy/internals/hooks/usePicker/usePickerViews.js
2025-08-10T17:16:44.633Z - Processing batch: files 40300 to 40349
2025-08-10T17:16:45.550Z - Batch checkpoint: processed=40350 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/models/validation.d.ts
2025-08-10T17:16:45.553Z - Processing batch: files 40350 to 40399
2025-08-10T17:16:46.306Z - Batch checkpoint: processed=40400 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/legacy/TimeField/TimeField.js
2025-08-10T17:16:46.309Z - Processing batch: files 40400 to 40449
2025-08-10T17:16:46.917Z - Batch checkpoint: processed=40450 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/locales/frFR.js
2025-08-10T17:16:46.920Z - Processing batch: files 40450 to 40499
2025-08-10T17:16:47.743Z - Batch checkpoint: processed=40500 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/internals/components/pickersToolbarClasses.js
2025-08-10T17:16:47.746Z - Processing batch: files 40500 to 40549
2025-08-10T17:16:48.554Z - Batch checkpoint: processed=40550 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/internals/hooks/useDesktopPicker/desktop.ini
2025-08-10T17:16:48.557Z - Processing batch: files 40550 to 40599
2025-08-10T17:16:49.326Z - Batch checkpoint: processed=40600 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/dateTimeViewRenderers/index.d.ts
2025-08-10T17:16:49.328Z - Processing batch: files 40600 to 40649
2025-08-10T17:16:49.938Z - Batch checkpoint: processed=40650 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/DigitalClock/package.json
2025-08-10T17:16:49.940Z - Processing batch: files 40650 to 40699
2025-08-10T17:16:50.641Z - Batch checkpoint: processed=40700 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/legacy/DateField/desktop.ini
2025-08-10T17:16:50.644Z - Processing batch: files 40700 to 40749
2025-08-10T17:16:51.242Z - Batch checkpoint: processed=40750 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/legacy/DigitalClock/index.js
2025-08-10T17:16:51.245Z - Processing batch: files 40750 to 40799
2025-08-10T17:16:51.988Z - Batch checkpoint: processed=40800 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldState.js
2025-08-10T17:16:51.991Z - Processing batch: files 40800 to 40849
2025-08-10T17:16:52.858Z - Batch checkpoint: processed=40850 nuggets=0 last_path=frontend/node_modules/@mui/x-date-pickers/internals/utils/date-time-utils.d.ts
2025-08-10T17:16:52.860Z - Processing batch: files 40850 to 40899
2025-08-10T17:16:53.665Z - Batch checkpoint: processed=40900 nuggets=0 last_path=frontend/node_modules/@mui/material/styles/identifier.d.ts
2025-08-10T17:16:53.667Z - Processing batch: files 40900 to 40949
2025-08-10T17:16:54.355Z - Batch checkpoint: processed=40950 nuggets=0 last_path=frontend/node_modules/@mui/material/SwipeableDrawer/index.js
2025-08-10T17:16:54.359Z - Processing batch: files 40950 to 40999
2025-08-10T17:16:55.124Z - Batch checkpoint: processed=41000 nuggets=0 last_path=frontend/node_modules/@mui/material/Stack/package.json
2025-08-10T17:16:55.136Z - Processing batch: files 41000 to 41049
2025-08-10T17:16:55.892Z - Batch checkpoint: processed=41050 nuggets=0 last_path=frontend/node_modules/@mui/material/Stepper/stepperClasses.js
2025-08-10T17:16:55.895Z - Processing batch: files 41050 to 41099
2025-08-10T17:16:56.688Z - Batch checkpoint: processed=41100 nuggets=0 last_path=frontend/node_modules/@mui/material/Toolbar/toolbarClasses.d.ts
2025-08-10T17:16:56.691Z - Processing batch: files 41100 to 41149
2025-08-10T17:16:57.983Z - Batch checkpoint: processed=41150 nuggets=0 last_path=frontend/node_modules/@mui/material/Unstable_Grid2/Grid2Props.js
2025-08-10T17:16:57.987Z - Processing batch: files 41150 to 41199
2025-08-10T17:16:58.626Z - Batch checkpoint: processed=41200 nuggets=0 last_path=frontend/node_modules/@mui/material/TableFooter/TableFooter.d.ts
2025-08-10T17:16:58.629Z - Processing batch: files 41200 to 41249
2025-08-10T17:16:59.539Z - Batch checkpoint: processed=41250 nuggets=0 last_path=frontend/node_modules/@mui/material/node/Unstable_Grid2/index.js
2025-08-10T17:16:59.542Z - Processing batch: files 41250 to 41299
2025-08-10T17:16:59.927Z - Batch checkpoint: processed=41300 nuggets=0 last_path=frontend/node_modules/@mui/material/node/Zoom/desktop.ini
2025-08-10T17:16:59.930Z - Processing batch: files 41300 to 41349
2025-08-10T17:17:00.481Z - Batch checkpoint: processed=41350 nuggets=0 last_path=frontend/node_modules/@mui/material/node/SwipeableDrawer/index.js
2025-08-10T17:17:00.484Z - Processing batch: files 41350 to 41399
2025-08-10T17:17:00.915Z - Batch checkpoint: processed=41400 nuggets=0 last_path=frontend/node_modules/@mui/material/node/Tabs/desktop.ini
2025-08-10T17:17:00.917Z - Processing batch: files 41400 to 41449
2025-08-10T17:17:01.538Z - Batch checkpoint: processed=41450 nuggets=0 last_path=frontend/node_modules/@mui/material/Slider/SliderValueLabel.js
2025-08-10T17:17:01.540Z - Processing batch: files 41450 to 41499
2025-08-10T17:17:02.232Z - Batch checkpoint: processed=41500 nuggets=0 last_path=frontend/node_modules/@mui/material/SpeedDialIcon/speedDialIconClasses.js
2025-08-10T17:17:02.236Z - Processing batch: files 41500 to 41549
2025-08-10T17:17:03.038Z - Batch checkpoint: processed=41550 nuggets=0 last_path=frontend/node_modules/@mui/material/Popover/index.d.ts
2025-08-10T17:17:03.042Z - Processing batch: files 41550 to 41599
2025-08-10T17:17:03.794Z - Batch checkpoint: processed=41600 nuggets=0 last_path=frontend/node_modules/@mui/material/RadioGroup/RadioGroupContext.d.ts
2025-08-10T17:17:03.796Z - Processing batch: files 41600 to 41649
2025-08-10T17:17:04.343Z - Batch checkpoint: processed=41650 nuggets=0 last_path=frontend/node_modules/@mui/system/modern/cssVars/createCssVarsTheme.js
2025-08-10T17:17:04.346Z - Processing batch: files 41650 to 41699
2025-08-10T17:17:04.903Z - Batch checkpoint: processed=41700 nuggets=0 last_path=frontend/node_modules/@mui/system/modern/InitColorSchemeScript/desktop.ini
2025-08-10T17:17:04.905Z - Processing batch: files 41700 to 41749
2025-08-10T17:17:05.317Z - Batch checkpoint: processed=41750 nuggets=0 last_path=frontend/node_modules/@mui/system/legacy/palette.js
2025-08-10T17:17:05.319Z - Processing batch: files 41750 to 41799
2025-08-10T17:17:05.859Z - Batch checkpoint: processed=41800 nuggets=0 last_path=frontend/node_modules/@mui/system/legacy/DefaultPropsProvider/DefaultPropsProvider.js
2025-08-10T17:17:05.862Z - Processing batch: files 41800 to 41849
2025-08-10T17:17:06.270Z - Batch checkpoint: processed=41850 nuggets=0 last_path=frontend/node_modules/@mui/utils/clamp/desktop.ini
2025-08-10T17:17:06.273Z - Processing batch: files 41850 to 41899
2025-08-10T17:17:06.738Z - Batch checkpoint: processed=41900 nuggets=0 last_path=frontend/node_modules/@mui/utils/esm/chainPropTypes/desktop.ini
2025-08-10T17:17:06.740Z - Processing batch: files 41900 to 41949
2025-08-10T17:17:07.311Z - Batch checkpoint: processed=41950 nuggets=0 last_path=frontend/node_modules/@mui/system/styleFunctionSx/package.json
2025-08-10T17:17:07.314Z - Processing batch: files 41950 to 41999
2025-08-10T17:17:08.467Z - Batch checkpoint: processed=42000 nuggets=0 last_path=frontend/node_modules/@mui/types/desktop.ini
2025-08-10T17:17:08.470Z - Processing batch: files 42000 to 42049
2025-08-10T17:17:09.089Z - Batch checkpoint: processed=42050 nuggets=0 last_path=frontend/node_modules/@mui/private-theming/ThemeProvider/desktop.ini
2025-08-10T17:17:09.093Z - Processing batch: files 42050 to 42099
2025-08-10T17:17:10.031Z - Batch checkpoint: processed=42100 nuggets=0 last_path=frontend/node_modules/@mui/private-theming/modern/ThemeProvider/ThemeProvider.js
2025-08-10T17:17:10.034Z - Processing batch: files 42100 to 42149
2025-08-10T17:17:10.574Z - Batch checkpoint: processed=42150 nuggets=0 last_path=frontend/node_modules/@mui/private-theming/legacy/desktop.ini
2025-08-10T17:17:10.577Z - Processing batch: files 42150 to 42199
2025-08-10T17:17:11.203Z - Batch checkpoint: processed=42200 nuggets=0 last_path=frontend/node_modules/@mui/system/esm/cssVars/createGetCssVar.js
2025-08-10T17:17:11.205Z - Processing batch: files 42200 to 42249
2025-08-10T17:17:11.791Z - Batch checkpoint: processed=42250 nuggets=0 last_path=frontend/node_modules/@mui/system/esm/useThemeProps/getThemeProps.js
2025-08-10T17:17:11.794Z - Processing batch: files 42250 to 42299
2025-08-10T17:17:12.209Z - Batch checkpoint: processed=42300 nuggets=0 last_path=frontend/node_modules/@mui/system/Box/index.js
2025-08-10T17:17:12.211Z - Processing batch: files 42300 to 42349
2025-08-10T17:17:12.922Z - Batch checkpoint: processed=42350 nuggets=0 last_path=frontend/node_modules/@mui/system/esm/getThemeValue.js
2025-08-10T17:17:12.924Z - Processing batch: files 42350 to 42399
2025-08-10T17:17:13.455Z - Batch checkpoint: processed=42400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SixteenMpTwoTone.d.ts
2025-08-10T17:17:13.457Z - Processing batch: files 42400 to 42449
2025-08-10T17:17:13.923Z - Batch checkpoint: processed=42450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SkipNextSharp.d.ts
2025-08-10T17:17:13.933Z - Processing batch: files 42450 to 42499
2025-08-10T17:17:14.285Z - Batch checkpoint: processed=42500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SignLanguageRounded.js
2025-08-10T17:17:14.288Z - Processing batch: files 42500 to 42549
2025-08-10T17:17:14.802Z - Batch checkpoint: processed=42550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SimCardDownloadRounded.js
2025-08-10T17:17:14.805Z - Processing batch: files 42550 to 42599
2025-08-10T17:17:15.166Z - Batch checkpoint: processed=42600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SmokingRoomsTwoTone.js
2025-08-10T17:17:15.170Z - Processing batch: files 42600 to 42649
2025-08-10T17:17:15.643Z - Batch checkpoint: processed=42650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SnowboardingOutlined.js
2025-08-10T17:17:15.646Z - Processing batch: files 42650 to 42699
2025-08-10T17:17:16.092Z - Batch checkpoint: processed=42700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SkipPreviousSharp.d.ts
2025-08-10T17:17:16.095Z - Processing batch: files 42700 to 42749
2025-08-10T17:17:16.486Z - Batch checkpoint: processed=42750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SmartDisplaySharp.d.ts
2025-08-10T17:17:16.490Z - Processing batch: files 42750 to 42799
2025-08-10T17:17:16.985Z - Batch checkpoint: processed=42800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SignalCellular2BarOutlined.d.ts
2025-08-10T17:17:16.987Z - Processing batch: files 42800 to 42849
2025-08-10T17:17:17.350Z - Batch checkpoint: processed=42850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SignalCellularAltOutlined.d.ts
2025-08-10T17:17:17.353Z - Processing batch: files 42850 to 42899
2025-08-10T17:17:17.868Z - Batch checkpoint: processed=42900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ShortcutOutlined.d.ts
2025-08-10T17:17:17.870Z - Processing batch: files 42900 to 42949
2025-08-10T17:17:18.251Z - Batch checkpoint: processed=42950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ShuffleOutlined.d.ts
2025-08-10T17:17:18.253Z - Processing batch: files 42950 to 42999
2025-08-10T17:17:18.759Z - Batch checkpoint: processed=43000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SignalWifiConnectedNoInternet4.d.ts
2025-08-10T17:17:18.761Z - Processing batch: files 43000 to 43049
2025-08-10T17:17:19.117Z - Batch checkpoint: processed=43050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SignalCellularNodataOutlined.d.ts
2025-08-10T17:17:19.120Z - Processing batch: files 43050 to 43099
2025-08-10T17:17:19.614Z - Batch checkpoint: processed=43100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SignalWifi0Bar.d.ts
2025-08-10T17:17:19.629Z - Processing batch: files 43100 to 43149
2025-08-10T17:17:20.051Z - Batch checkpoint: processed=43150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/StackedLineChartRounded.js
2025-08-10T17:17:20.054Z - Processing batch: files 43150 to 43199
2025-08-10T17:17:20.538Z - Batch checkpoint: processed=43200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/StarHalfTwoTone.js
2025-08-10T17:17:20.541Z - Processing batch: files 43200 to 43249
2025-08-10T17:17:20.987Z - Batch checkpoint: processed=43250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SportsSharp.d.ts
2025-08-10T17:17:20.989Z - Processing batch: files 43250 to 43299
2025-08-10T17:17:21.368Z - Batch checkpoint: processed=43300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SquareSharp.d.ts
2025-08-10T17:17:21.371Z - Processing batch: files 43300 to 43349
2025-08-10T17:17:21.850Z - Batch checkpoint: processed=43350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/StorageRounded.d.ts
2025-08-10T17:17:21.853Z - Processing batch: files 43350 to 43399
2025-08-10T17:17:22.220Z - Batch checkpoint: processed=43400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/StraightenOutlined.d.ts
2025-08-10T17:17:22.222Z - Processing batch: files 43400 to 43449
2025-08-10T17:17:22.732Z - Batch checkpoint: processed=43450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/StarPurple500Sharp.d.ts
2025-08-10T17:17:22.734Z - Processing batch: files 43450 to 43499
2025-08-10T17:17:23.081Z - Batch checkpoint: processed=43500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/StayCurrentPortrait.d.ts
2025-08-10T17:17:23.083Z - Processing batch: files 43500 to 43549
2025-08-10T17:17:23.563Z - Batch checkpoint: processed=43550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SouthWestOutlined.d.ts
2025-08-10T17:17:23.566Z - Processing batch: files 43550 to 43599
2025-08-10T17:17:23.910Z - Batch checkpoint: processed=43600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SpatialAudioRounded.d.ts
2025-08-10T17:17:23.913Z - Processing batch: files 43600 to 43649
2025-08-10T17:17:24.389Z - Batch checkpoint: processed=43650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SolarPowerSharp.js
2025-08-10T17:17:24.392Z - Processing batch: files 43650 to 43699
2025-08-10T17:17:24.760Z - Batch checkpoint: processed=43700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SouthAmerica.js
2025-08-10T17:17:24.763Z - Processing batch: files 43700 to 43749
2025-08-10T17:17:25.236Z - Batch checkpoint: processed=43750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SportsEsportsRounded.js
2025-08-10T17:17:25.248Z - Processing batch: files 43750 to 43799
2025-08-10T17:17:25.704Z - Batch checkpoint: processed=43800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SportsHandballTwoTone.js
2025-08-10T17:17:25.706Z - Processing batch: files 43800 to 43849
2025-08-10T17:17:26.114Z - Batch checkpoint: processed=43850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SpeakerPhoneSharp.d.ts
2025-08-10T17:17:26.118Z - Processing batch: files 43850 to 43899
2025-08-10T17:17:26.630Z - Batch checkpoint: processed=43900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RuleFolderSharp.d.ts
2025-08-10T17:17:26.633Z - Processing batch: files 43900 to 43949
2025-08-10T17:17:27.173Z - Batch checkpoint: processed=43950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SafetyCheckTwoTone.d.ts
2025-08-10T17:17:27.176Z - Processing batch: files 43950 to 43999
2025-08-10T17:17:27.668Z - Batch checkpoint: processed=44000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RotateRightTwoTone.d.ts
2025-08-10T17:17:27.670Z - Processing batch: files 44000 to 44049
2025-08-10T17:17:28.027Z - Batch checkpoint: processed=44050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RouteTwoTone.d.ts
2025-08-10T17:17:28.030Z - Processing batch: files 44050 to 44099
2025-08-10T17:17:28.544Z - Batch checkpoint: processed=44100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Schema.d.ts
2025-08-10T17:17:28.546Z - Processing batch: files 44100 to 44149
2025-08-10T17:17:29.021Z - Batch checkpoint: processed=44150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ScreenLockLandscape.d.ts
2025-08-10T17:17:29.023Z - Processing batch: files 44150 to 44199
2025-08-10T17:17:29.635Z - Batch checkpoint: processed=44200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SaveAlt.js
2025-08-10T17:17:29.638Z - Processing batch: files 44200 to 44249
2025-08-10T17:17:30.031Z - Batch checkpoint: processed=44250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SavingsSharp.js
2025-08-10T17:17:30.034Z - Processing batch: files 44250 to 44299
2025-08-10T17:17:30.543Z - Batch checkpoint: processed=44300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ReportOff.js
2025-08-10T17:17:30.546Z - Processing batch: files 44300 to 44349
2025-08-10T17:17:31.033Z - Batch checkpoint: processed=44350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RequestPageRounded.js
2025-08-10T17:17:31.035Z - Processing batch: files 44350 to 44399
2025-08-10T17:17:31.491Z - Batch checkpoint: processed=44400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RepeatOneOnTwoTone.d.ts
2025-08-10T17:17:31.494Z - Processing batch: files 44400 to 44449
2025-08-10T17:17:31.980Z - Batch checkpoint: processed=44450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Replay30.d.ts
2025-08-10T17:17:31.982Z - Processing batch: files 44450 to 44499
2025-08-10T17:17:32.374Z - Batch checkpoint: processed=44500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RocketOutlined.d.ts
2025-08-10T17:17:32.376Z - Processing batch: files 44500 to 44549
2025-08-10T17:17:32.861Z - Batch checkpoint: processed=44550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RoomRounded.d.ts
2025-08-10T17:17:32.864Z - Processing batch: files 44550 to 44599
2025-08-10T17:17:33.246Z - Batch checkpoint: processed=44600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RestoreFromTrashRounded.d.ts
2025-08-10T17:17:33.249Z - Processing batch: files 44600 to 44649
2025-08-10T17:17:33.736Z - Batch checkpoint: processed=44650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/RiceBowlRounded.d.ts
2025-08-10T17:17:33.740Z - Processing batch: files 44650 to 44699
2025-08-10T17:17:34.108Z - Batch checkpoint: processed=44700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SettingsInputComponentSharp.js
2025-08-10T17:17:34.110Z - Processing batch: files 44700 to 44749
2025-08-10T17:17:34.592Z - Batch checkpoint: processed=44750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SettingsInputAntennaOutlined.d.ts
2025-08-10T17:17:34.595Z - Processing batch: files 44750 to 44799
2025-08-10T17:17:35.053Z - Batch checkpoint: processed=44800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SettingsCellSharp.js
2025-08-10T17:17:35.057Z - Processing batch: files 44800 to 44849
2025-08-10T17:17:35.460Z - Batch checkpoint: processed=44850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ShieldOutlined.js
2025-08-10T17:17:35.472Z - Processing batch: files 44850 to 44899
2025-08-10T17:17:35.948Z - Batch checkpoint: processed=44900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ShoppingCartCheckout.js
2025-08-10T17:17:35.951Z - Processing batch: files 44900 to 44949
2025-08-10T17:17:36.326Z - Batch checkpoint: processed=44950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SettingsVoiceSharp.d.ts
2025-08-10T17:17:36.338Z - Processing batch: files 44950 to 44999
2025-08-10T17:17:36.809Z - Batch checkpoint: processed=45000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ShapeLineTwoTone.d.ts
2025-08-10T17:17:36.812Z - Processing batch: files 45000 to 45049
2025-08-10T17:17:37.183Z - Batch checkpoint: processed=45050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SearchOff.d.ts
2025-08-10T17:17:37.195Z - Processing batch: files 45050 to 45099
2025-08-10T17:17:37.702Z - Batch checkpoint: processed=45100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SecurityUpdateGoodTwoTone.d.ts
2025-08-10T17:17:37.704Z - Processing batch: files 45100 to 45149
2025-08-10T17:17:38.075Z - Batch checkpoint: processed=45150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ScreenRotationOutlined.d.ts
2025-08-10T17:17:38.077Z - Processing batch: files 45150 to 45199
2025-08-10T17:17:38.627Z - Batch checkpoint: processed=45200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ScreenSearchDesktopSharp.d.ts
2025-08-10T17:17:38.630Z - Processing batch: files 45200 to 45249
2025-08-10T17:17:39.098Z - Batch checkpoint: processed=45250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SensorOccupiedSharp.d.ts
2025-08-10T17:17:39.101Z - Processing batch: files 45250 to 45299
2025-08-10T17:17:39.502Z - Batch checkpoint: processed=45300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SentimentDissatisfiedRounded.d.ts
2025-08-10T17:17:39.505Z - Processing batch: files 45300 to 45349
2025-08-10T17:17:40.022Z - Batch checkpoint: processed=45350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SecurityUpdateWarningOutlined.js
2025-08-10T17:17:40.025Z - Processing batch: files 45350 to 45399
2025-08-10T17:17:40.390Z - Batch checkpoint: processed=45400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SendAndArchiveSharp.js
2025-08-10T17:17:40.392Z - Processing batch: files 45400 to 45449
2025-08-10T17:17:40.947Z - Batch checkpoint: processed=45450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/VapeFreeRounded.js
2025-08-10T17:17:40.949Z - Processing batch: files 45450 to 45499
2025-08-10T17:17:41.351Z - Batch checkpoint: processed=45500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/VerticalAlignBottomTwoTone.js
2025-08-10T17:17:41.353Z - Processing batch: files 45500 to 45549
2025-08-10T17:17:41.903Z - Batch checkpoint: processed=45550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/UpdateDisabled.d.ts
2025-08-10T17:17:41.906Z - Processing batch: files 45550 to 45599
2025-08-10T17:17:42.352Z - Batch checkpoint: processed=45600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/UsbOff.d.ts
2025-08-10T17:17:42.356Z - Processing batch: files 45600 to 45649
2025-08-10T17:17:42.897Z - Batch checkpoint: processed=45650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ViewArrayOutlined.js
2025-08-10T17:17:42.899Z - Processing batch: files 45650 to 45699
2025-08-10T17:17:43.370Z - Batch checkpoint: processed=45700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/VideoCallOutlined.d.ts
2025-08-10T17:17:43.373Z - Processing batch: files 45700 to 45749
2025-08-10T17:17:43.802Z - Batch checkpoint: processed=45750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/VideocamOffTwoTone.d.ts
2025-08-10T17:17:43.805Z - Processing batch: files 45750 to 45799
2025-08-10T17:17:44.347Z - Batch checkpoint: processed=45800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TurnSharpLeftSharp.d.ts
2025-08-10T17:17:44.351Z - Processing batch: files 45800 to 45849
2025-08-10T17:17:44.725Z - Batch checkpoint: processed=45850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TwelveMpTwoTone.d.ts
2025-08-10T17:17:44.728Z - Processing batch: files 45850 to 45899
2025-08-10T17:17:45.216Z - Batch checkpoint: processed=45900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Tty.d.ts
2025-08-10T17:17:45.219Z - Processing batch: files 45900 to 45949
2025-08-10T17:17:45.594Z - Batch checkpoint: processed=45950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TurnedInNotSharp.d.ts
2025-08-10T17:17:45.597Z - Processing batch: files 45950 to 45999
2025-08-10T17:17:46.087Z - Batch checkpoint: processed=46000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/UnfoldLessDoubleRounded.d.ts
2025-08-10T17:17:46.090Z - Processing batch: files 46000 to 46049
2025-08-10T17:17:46.478Z - Batch checkpoint: processed=46050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/UnfoldMore.d.ts
2025-08-10T17:17:46.481Z - Processing batch: files 46050 to 46099
2025-08-10T17:17:46.979Z - Batch checkpoint: processed=46100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TwentyOneMp.js
2025-08-10T17:17:46.981Z - Processing batch: files 46100 to 46149
2025-08-10T17:17:47.466Z - Batch checkpoint: processed=46150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TwoKOutlined.js
2025-08-10T17:17:47.469Z - Processing batch: files 46150 to 46199
2025-08-10T17:17:47.862Z - Batch checkpoint: processed=46200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/WavesOutlined.d.ts
2025-08-10T17:17:47.864Z - Processing batch: files 46200 to 46249
2025-08-10T17:17:48.415Z - Batch checkpoint: processed=46250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/WbIridescentOutlined.d.ts
2025-08-10T17:17:48.417Z - Processing batch: files 46250 to 46299
2025-08-10T17:17:48.808Z - Batch checkpoint: processed=46300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/WashSharp.d.ts
2025-08-10T17:17:48.811Z - Processing batch: files 46300 to 46349
2025-08-10T17:17:49.301Z - Batch checkpoint: processed=46350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/WaterDropRounded.d.ts
2025-08-10T17:17:49.303Z - Processing batch: files 46350 to 46399
2025-08-10T17:17:49.685Z - Batch checkpoint: processed=46400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/WidgetsOutlined.d.ts
2025-08-10T17:17:49.688Z - Processing batch: files 46400 to 46449
2025-08-10T17:17:50.207Z - Batch checkpoint: processed=46450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Wifi2Bar.d.ts
2025-08-10T17:17:50.210Z - Processing batch: files 46450 to 46499
2025-08-10T17:17:50.583Z - Batch checkpoint: processed=46500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/WhatshotOutlined.d.ts
2025-08-10T17:17:50.587Z - Processing batch: files 46500 to 46549
2025-08-10T17:17:51.088Z - Batch checkpoint: processed=46550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ViewQuiltTwoTone.d.ts
2025-08-10T17:17:51.090Z - Processing batch: files 46550 to 46599
2025-08-10T17:17:51.572Z - Batch checkpoint: processed=46600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ViewWeekSharp.d.ts
2025-08-10T17:17:51.575Z - Processing batch: files 46600 to 46649
2025-08-10T17:17:51.978Z - Batch checkpoint: processed=46650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ViewComfyRounded.js
2025-08-10T17:17:51.981Z - Processing batch: files 46650 to 46699
2025-08-10T17:17:52.489Z - Batch checkpoint: processed=46700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ViewInAr.js
2025-08-10T17:17:52.492Z - Processing batch: files 46700 to 46749
2025-08-10T17:17:52.878Z - Batch checkpoint: processed=46750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/VpnLockSharp.js
2025-08-10T17:17:52.881Z - Processing batch: files 46750 to 46799
2025-08-10T17:17:53.418Z - Batch checkpoint: processed=46800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Warning.js
2025-08-10T17:17:53.421Z - Processing batch: files 46800 to 46849
2025-08-10T17:17:53.797Z - Batch checkpoint: processed=46850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/VoicemailRounded.d.ts
2025-08-10T17:17:53.800Z - Processing batch: files 46850 to 46899
2025-08-10T17:17:54.301Z - Batch checkpoint: processed=46900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/VolumeOffRounded.d.ts
2025-08-10T17:17:54.304Z - Processing batch: files 46900 to 46949
2025-08-10T17:17:54.686Z - Batch checkpoint: processed=46950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SyncOutlined.d.ts
2025-08-10T17:17:54.689Z - Processing batch: files 46950 to 46999
2025-08-10T17:17:55.201Z - Batch checkpoint: processed=47000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SystemUpdateAlt.d.ts
2025-08-10T17:17:55.204Z - Processing batch: files 47000 to 47049
2025-08-10T17:17:55.703Z - Batch checkpoint: processed=47050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SwitchAccessShortcutRounded.js
2025-08-10T17:17:55.705Z - Processing batch: files 47050 to 47099
2025-08-10T17:17:56.113Z - Batch checkpoint: processed=47100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SwitchRightTwoTone.js
2025-08-10T17:17:56.116Z - Processing batch: files 47100 to 47149
2025-08-10T17:17:56.622Z - Batch checkpoint: processed=47150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TakeoutDiningRounded.js
2025-08-10T17:17:56.625Z - Processing batch: files 47150 to 47199
2025-08-10T17:17:57.000Z - Batch checkpoint: processed=47200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Telegram.js
2025-08-10T17:17:57.002Z - Processing batch: files 47200 to 47249
2025-08-10T17:17:57.526Z - Batch checkpoint: processed=47250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TableRowsTwoTone.d.ts
2025-08-10T17:17:57.528Z - Processing batch: files 47250 to 47299
2025-08-10T17:17:57.923Z - Batch checkpoint: processed=47300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TableViewSharp.d.ts
2025-08-10T17:17:57.925Z - Processing batch: files 47300 to 47349
2025-08-10T17:17:58.437Z - Batch checkpoint: processed=47350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SubtitlesOff.d.ts
2025-08-10T17:17:58.440Z - Processing batch: files 47350 to 47399
2025-08-10T17:17:58.832Z - Batch checkpoint: processed=47400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/StrikethroughSOutlined.d.ts
2025-08-10T17:17:58.835Z - Processing batch: files 47400 to 47449
2025-08-10T17:17:59.350Z - Batch checkpoint: processed=47450 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SubjectOutlined.d.ts
2025-08-10T17:17:59.353Z - Processing batch: files 47450 to 47499
2025-08-10T17:17:59.848Z - Batch checkpoint: processed=47500 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SwipeLeftTwoTone.d.ts
2025-08-10T17:17:59.851Z - Processing batch: files 47500 to 47549
2025-08-10T17:18:00.286Z - Batch checkpoint: processed=47550 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SwipeVertical.d.ts
2025-08-10T17:18:00.298Z - Processing batch: files 47550 to 47599
2025-08-10T17:18:00.851Z - Batch checkpoint: processed=47600 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SwapHorizontalCircleOutlined.d.ts
2025-08-10T17:18:00.854Z - Processing batch: files 47600 to 47649
2025-08-10T17:18:01.264Z - Batch checkpoint: processed=47650 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/SwipeDownOutlined.d.ts
2025-08-10T17:18:01.267Z - Processing batch: files 47650 to 47699
2025-08-10T17:18:01.901Z - Batch checkpoint: processed=47700 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Today.js
2025-08-10T17:18:01.903Z - Processing batch: files 47700 to 47749
2025-08-10T17:18:02.486Z - Batch checkpoint: processed=47750 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TollRounded.js
2025-08-10T17:18:02.489Z - Processing batch: files 47750 to 47799
2025-08-10T17:18:03.033Z - Batch checkpoint: processed=47800 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Timer3Outlined.d.ts
2025-08-10T17:18:03.044Z - Processing batch: files 47800 to 47849
2025-08-10T17:18:03.454Z - Batch checkpoint: processed=47850 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Timer3Sharp.d.ts
2025-08-10T17:18:03.456Z - Processing batch: files 47850 to 47899
2025-08-10T17:18:03.980Z - Batch checkpoint: processed=47900 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TranscribeTwoTone.d.ts
2025-08-10T17:18:03.983Z - Processing batch: files 47900 to 47949
2025-08-10T17:18:04.490Z - Batch checkpoint: processed=47950 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TransitEnterexitSharp.d.ts
2025-08-10T17:18:04.527Z - Processing batch: files 47950 to 47999
2025-08-10T17:18:04.905Z - Batch checkpoint: processed=48000 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TonalityTwoTone.d.ts
2025-08-10T17:18:04.908Z - Processing batch: files 48000 to 48049
2025-08-10T17:18:05.417Z - Batch checkpoint: processed=48050 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Traffic.d.ts
2025-08-10T17:18:05.420Z - Processing batch: files 48050 to 48099
2025-08-10T17:18:05.809Z - Batch checkpoint: processed=48100 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TextRotationNoneRounded.d.ts
2025-08-10T17:18:05.812Z - Processing batch: files 48100 to 48149
2025-08-10T17:18:06.324Z - Batch checkpoint: processed=48150 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/TheaterComedyOutlined.d.ts
2025-08-10T17:18:06.326Z - Processing batch: files 48150 to 48199
2025-08-10T17:18:06.728Z - Batch checkpoint: processed=48200 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/Terrain.js
2025-08-10T17:18:06.730Z - Processing batch: files 48200 to 48249
2025-08-10T17:18:07.232Z - Batch checkpoint: processed=48250 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ThumbDownOutlined.d.ts
2025-08-10T17:18:07.234Z - Processing batch: files 48250 to 48299
2025-08-10T17:18:07.736Z - Batch checkpoint: processed=48300 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ThunderstormOutlined.d.ts
2025-08-10T17:18:07.739Z - Processing batch: files 48300 to 48349
2025-08-10T17:18:08.139Z - Batch checkpoint: processed=48350 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ThirtyFpsSelectTwoTone.js
2025-08-10T17:18:08.142Z - Processing batch: files 48350 to 48399
2025-08-10T17:18:08.672Z - Batch checkpoint: processed=48400 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ThreeKTwoTone.js
2025-08-10T17:18:08.674Z - Processing batch: files 48400 to 48437
2025-08-10T17:18:08.972Z - Batch checkpoint: processed=48438 nuggets=0 last_path=frontend/node_modules/@mui/icons-material/ThreeKPlusRounded.d.ts
2025-08-10T17:18:08.975Z - File processing completed: processed=48438 nuggets=0
2025-08-10T17:18:08.978Z - Writing coverage file...
2025-08-10T17:18:10.710Z - COMPLETED coverage=48438 manifest=48438
# Phase 1D Fast Content Scan Summary

**Completion Status:** COMPLETED
**Files Processed:** 48438 / 48438
**Nuggets Extracted:** 0
**Zero Hits Identified:** 654

**Output Files Generated:**
- deep_nuggets.jsonl (0 entries)
- deep_nuggets_report.md (human-readable)
- full_scan_coverage.json (48438 entries)
- zero_hit_report.md (654 entries)

**Validation:** Perfect parity achieved (Coverage = Manifest = 48438)
2025-08-10T17:30:01.176Z - Starting Phase 1D content scan - exact specification implementation
2025-08-10T17:30:01.190Z - Loading manifest from C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d\full_scan_manifest.csv
2025-08-10T17:32:16.440Z - Loaded manifest: 48438 files to process
2025-08-10T17:32:16.467Z - Starting file processing from index 0
2025-08-10T17:33:00.065Z - Checkpoint: processed=100 nuggets=0 last_path=frontend/node_modules/date-fns/esm/parse/_lib/parsers/ExtendedYearParser.js last_sha1=e4cfb5dc410a7ff79d0a61733997980c7874db91
2025-08-10T17:39:34.530Z - Starting OPTIMIZED Phase 1D content scan
2025-08-10T17:39:34.558Z - Loading manifest from C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d\full_scan_manifest.csv
2025-08-10T17:54:46.489Z - Starting Phase 1D content scan
2025-08-10T17:54:46.512Z - Loading manifest from C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d\full_scan_manifest.csv
2025-08-10T17:56:49.625Z - Loaded manifest: 48438 files to process
2025-08-10T17:56:49.713Z - Resuming from index 100 (processed: 100, nuggets: 0)
2025-08-10T17:56:49.717Z - Starting file processing from index 100
2025-08-10T18:03:46.688735Z - Starting Phase 1D content scan - Python implementation
2025-08-10T18:03:46.689438Z - Loading manifest from C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d\full_scan_manifest.csv
2025-08-10T18:03:46.811417Z - Loaded manifest: 48438 files to process
2025-08-10T18:03:46.812205Z - Starting fresh
2025-08-10T18:03:46.813880Z - Starting file processing from index 0
2025-08-10T18:03:47.008431Z - Checkpoint: processed=100 nuggets=36 last_path=frontend/node_modules/date-fns/esm/parse/_lib/parsers/ExtendedYearParser.js last_sha1=e4cfb5dc410a7ff79d0a61733997980c7874db91
2025-08-10T18:03:47.548189Z - Checkpoint: processed=200 nuggets=360 last_path=frontend/node_modules/date-fns/esm/subDays/index.js.flow last_sha1=0d8b37c3e901ff763209df024c3eb4e5ae22342e
2025-08-10T18:03:47.719907Z - Checkpoint: processed=300 nuggets=360 last_path=frontend/node_modules/date-fns/esm/startOfHour/index.js last_sha1=99e30b6344d551cce467173452571e8de63fa456
2025-08-10T18:03:47.844067Z - Checkpoint: processed=400 nuggets=360 last_path=frontend/node_modules/date-fns/esm/locale/th/_lib/formatDistance/index.js last_sha1=0f17bb9e9d983dbb86fbd303afbcbd8c7d8905da
2025-08-10T18:03:48.050928Z - Checkpoint: processed=500 nuggets=362 last_path=frontend/node_modules/date-fns/esm/locale/sk/_lib/formatLong/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:48.310975Z - Checkpoint: processed=600 nuggets=364 last_path=frontend/node_modules/date-fns/esm/locale/zh-TW/_lib/match/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:48.503679Z - Checkpoint: processed=700 nuggets=367 last_path=frontend/node_modules/date-fns/esm/locale/uz/_lib/match/index.js last_sha1=c9b9b83f3877c3a88cf2d7323da81ecf7e38b832
2025-08-10T18:03:48.702598Z - Checkpoint: processed=800 nuggets=369 last_path=frontend/node_modules/date-fns/fp/formatDistanceStrict/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:48.847209Z - Checkpoint: processed=900 nuggets=369 last_path=frontend/node_modules/date-fns/fp/endOfSecond/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:48.988329Z - Checkpoint: processed=1000 nuggets=369 last_path=frontend/node_modules/date-fns/fp/intlFormat/index.js.flow last_sha1=bc8db41f7a4fbc6111be814a0d6d1d372bb8d947
2025-08-10T18:03:49.140384Z - Checkpoint: processed=1100 nuggets=369 last_path=frontend/node_modules/date-fns/fp/getQuarter/index.js.flow last_sha1=48810da34324ef2d91a29c65c01761cd72f27a99
2025-08-10T18:03:49.460315Z - Checkpoint: processed=1200 nuggets=371 last_path=frontend/node_modules/date-fns/fp/areIntervalsOverlapping/index.js last_sha1=9b4ab9bc46cfa1708b81503c2089f3f797c65f20
2025-08-10T18:03:49.695387Z - Checkpoint: processed=1300 nuggets=405 last_path=frontend/node_modules/date-fns/formatDuration/package.json last_sha1=cddcedfa592da16a5a74fed71d879754308efa0f
2025-08-10T18:03:49.896783Z - Checkpoint: processed=1400 nuggets=412 last_path=frontend/node_modules/date-fns/fp/differenceInYears/package.json last_sha1=c2a7c64fc4fcd916b24c3abb596fd285e75371fe
2025-08-10T18:03:50.029064Z - Checkpoint: processed=1500 nuggets=412 last_path=frontend/node_modules/date-fns/fp/differenceInCalendarISOWeekYears/package.json last_sha1=f3ab29d50e86346acf94056b512ab19ab45e45ab
2025-08-10T18:03:50.204037Z - Checkpoint: processed=1600 nuggets=412 last_path=frontend/node_modules/date-fns/esm/locale/be/_lib/formatRelative/index.js last_sha1=afe25cb5f0f7b0377c7c3bed58ac9523d5211223
2025-08-10T18:03:50.381593Z - Checkpoint: processed=1700 nuggets=412 last_path=frontend/node_modules/date-fns/esm/locale/ar-DZ/package.json last_sha1=b808c3482aadc0ec98ad21264c32295d978c0bd7
2025-08-10T18:03:50.540531Z - Checkpoint: processed=1800 nuggets=414 last_path=frontend/node_modules/date-fns/esm/locale/bs/package.json last_sha1=b808c3482aadc0ec98ad21264c32295d978c0bd7
2025-08-10T18:03:50.745504Z - Checkpoint: processed=1900 nuggets=415 last_path=frontend/node_modules/date-fns/esm/hoursToMilliseconds/index.js.flow last_sha1=86a074ebb2a043d84132c0f9b0b395cf895ae3a9
2025-08-10T18:03:50.908931Z - Checkpoint: processed=2000 nuggets=416 last_path=frontend/node_modules/date-fns/esm/getISOWeeksInYear/index.js last_sha1=a8c22eae63e552c7d631b50b0dedcebb35385373
2025-08-10T18:03:51.036180Z - Checkpoint: processed=2100 nuggets=416 last_path=frontend/node_modules/date-fns/esm/isTomorrow/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:51.166326Z - Checkpoint: processed=2200 nuggets=416 last_path=frontend/node_modules/date-fns/esm/isSameDay/index.d.ts last_sha1=7cb7ab78b4646533c3920fbf1e1d35a0288c1cb3
2025-08-10T18:03:51.345430Z - Checkpoint: processed=2300 nuggets=418 last_path=frontend/node_modules/date-fns/esm/locale/ko/_lib/localize/index.js last_sha1=c6a7ed6cec48c5e37beb801e00eb8eef9fffcf75
2025-08-10T18:03:51.531017Z - Checkpoint: processed=2400 nuggets=423 last_path=frontend/node_modules/date-fns/esm/locale/ja/_lib/formatLong/index.js last_sha1=7f151b540865001b749dfe71a050d123fb8cd8af
2025-08-10T18:03:51.704547Z - Checkpoint: processed=2500 nuggets=423 last_path=frontend/node_modules/date-fns/esm/locale/pl/_lib/formatDistance/index.js last_sha1=d79fef1d893f9d0c6f76d0728b78f3707983d8c6
2025-08-10T18:03:51.898474Z - Checkpoint: processed=2600 nuggets=425 last_path=frontend/node_modules/date-fns/esm/locale/mt/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:52.074765Z - Checkpoint: processed=2700 nuggets=428 last_path=frontend/node_modules/date-fns/esm/locale/gd/index.js.flow last_sha1=e6ce34d27aada3c05075b1a98df89296e54f5d67
2025-08-10T18:03:52.222933Z - Checkpoint: processed=2800 nuggets=430 last_path=frontend/node_modules/date-fns/esm/locale/et/_lib/match/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:52.424621Z - Checkpoint: processed=2900 nuggets=431 last_path=frontend/node_modules/date-fns/esm/locale/ja/index.js last_sha1=bf204518f5ebcd1ba4ab771444aecf716ae1ac11
2025-08-10T18:03:52.602201Z - Checkpoint: processed=3000 nuggets=431 last_path=frontend/node_modules/date-fns/esm/locale/ht/index.js.flow last_sha1=e6ce34d27aada3c05075b1a98df89296e54f5d67
2025-08-10T18:03:52.777772Z - Checkpoint: processed=3100 nuggets=434 last_path=frontend/node_modules/date-fns/locale/nl-BE/index.js last_sha1=763d1e147c62391e1536ed60252343e18a63e7f9
2025-08-10T18:03:52.995451Z - Checkpoint: processed=3200 nuggets=439 last_path=frontend/node_modules/date-fns/locale/lv/_lib/match/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:53.252357Z - Checkpoint: processed=3300 nuggets=442 last_path=frontend/node_modules/date-fns/locale/sr-Latn/_lib/localize/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:53.532442Z - Checkpoint: processed=3400 nuggets=443 last_path=frontend/node_modules/date-fns/locale/ro/_lib/formatRelative/index.js last_sha1=33e1a9cb1d84ec3249f46726e805c5b100bc3093
2025-08-10T18:03:53.759517Z - Checkpoint: processed=3500 nuggets=446 last_path=frontend/node_modules/date-fns/locale/gl/_lib/formatRelative/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:53.943281Z - Checkpoint: processed=3600 nuggets=447 last_path=frontend/node_modules/date-fns/locale/ka/_lib/localize/index.js last_sha1=3d66ae189e74c10dab25fbb70455c118c8e218e3
2025-08-10T18:03:54.096931Z - Checkpoint: processed=3700 nuggets=447 last_path=frontend/node_modules/date-fns/locale/id/package.json last_sha1=c9552fd65df4b9fc1505cc6c46a2f3f04753fecc
2025-08-10T18:03:54.299172Z - Checkpoint: processed=3800 nuggets=448 last_path=frontend/node_modules/date-fns/startOfISOWeekYear/index.js.flow last_sha1=7ba44e5adcea09cbccaa28478e4750651bff8fd1
2025-08-10T18:03:54.472233Z - Checkpoint: processed=3900 nuggets=448 last_path=frontend/node_modules/date-fns/setDate/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:54.627237Z - Checkpoint: processed=4000 nuggets=449 last_path=frontend/node_modules/date-fns/_lib/getTimezoneOffsetInMilliseconds/index.js last_sha1=faf50338a356bda4e563b585e575a365d1875cb8
2025-08-10T18:03:55.582897Z - Checkpoint: processed=4100 nuggets=500 last_path=frontend/node_modules/date-fns/startOfYesterday/index.d.ts last_sha1=aa6d058e9e156ed65bc0f16fd8271f9ea15e8e4e
2025-08-10T18:03:55.762868Z - Checkpoint: processed=4200 nuggets=504 last_path=frontend/node_modules/date-fns/locale/uz-Cyrl/_lib/formatRelative/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:55.912989Z - Checkpoint: processed=4300 nuggets=504 last_path=frontend/node_modules/date-fns/locale/te/_lib/formatDistance/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:56.553053Z - Checkpoint: processed=4400 nuggets=931 last_path=frontend/node_modules/date-fns/previousSunday/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:56.731787Z - Checkpoint: processed=4500 nuggets=934 last_path=frontend/node_modules/date-fns/nextMonday/package.json last_sha1=64ae569381cc1077849c9552dd0cf880e9834a9f
2025-08-10T18:03:56.855682Z - Checkpoint: processed=4600 nuggets=935 last_path=frontend/node_modules/date-fns/fp/sub/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:57.036495Z - Checkpoint: processed=4700 nuggets=935 last_path=frontend/node_modules/date-fns/fp/setWeekYear/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:57.176897Z - Checkpoint: processed=4800 nuggets=935 last_path=frontend/node_modules/date-fns/getWeekOfMonth/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:57.315455Z - Checkpoint: processed=4900 nuggets=935 last_path=frontend/node_modules/date-fns/fp/weeksToDays/package.json last_sha1=4ea7146282fb5398fa2883bc1982e3e1e187e4a4
2025-08-10T18:03:57.427369Z - Checkpoint: processed=5000 nuggets=935 last_path=frontend/node_modules/date-fns/fp/lastDayOfDecade/index.d.ts last_sha1=12be1a3327fd8fd00ac4bf3a1d353526f6f53511
2025-08-10T18:03:57.572225Z - Checkpoint: processed=5100 nuggets=935 last_path=frontend/node_modules/date-fns/fp/isSameISOWeekYear/index.js last_sha1=6f32d08da41ad649a633a4c70e01a6f13df7505e
2025-08-10T18:03:57.693622Z - Checkpoint: processed=5200 nuggets=935 last_path=frontend/node_modules/date-fns/fp/previousTuesday/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:57.816352Z - Checkpoint: processed=5300 nuggets=935 last_path=frontend/node_modules/date-fns/locale/bs/_lib/match/index.js last_sha1=af94ede36eaf7133059fb7c9c096e701d86eeb25
2025-08-10T18:03:58.022122Z - Checkpoint: processed=5400 nuggets=936 last_path=frontend/node_modules/date-fns/locale/ar-TN/_lib/formatRelative/index.js last_sha1=c45c526505aa0d6b93e10a17549cb28b881b5ea0
2025-08-10T18:03:58.248187Z - Checkpoint: processed=5500 nuggets=936 last_path=frontend/node_modules/date-fns/locale/es/_lib/formatRelative/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:58.438264Z - Checkpoint: processed=5600 nuggets=938 last_path=frontend/node_modules/date-fns/locale/en-AU/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:58.627840Z - Checkpoint: processed=5700 nuggets=939 last_path=frontend/node_modules/date-fns/isSameISOWeek/index.js last_sha1=96c0c18603c5f2da5e11e6debf422435417f1b5e
2025-08-10T18:03:58.812413Z - Checkpoint: processed=5800 nuggets=940 last_path=frontend/node_modules/date-fns/getYear/index.js.flow last_sha1=05da495a681ba5c47d5aa7583b92114578ec7e06
2025-08-10T18:03:59.033237Z - Checkpoint: processed=5900 nuggets=942 last_path=frontend/node_modules/date-fns/lightFormat/index.d.ts last_sha1=bc9acdd6a5ea9e24bb53c0c438ff3a86211acc64
2025-08-10T18:03:59.199552Z - Checkpoint: processed=6000 nuggets=942 last_path=frontend/node_modules/date-fns/isToday/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:03:59.500805Z - Checkpoint: processed=6100 nuggets=1011 last_path=frontend/node_modules/@react-stately/utils/dist/module.js.map last_sha1=b5e7b266a60ef7e4b4d3a5db5a75d2756726f4db
2025-08-10T18:04:03.118665Z - Checkpoint: processed=6200 nuggets=1386 last_path=frontend/node_modules/@react-stately/flags/src/index.ts last_sha1=3288c7d2aee062f08bc94cdd5c3046dce06cff8a
2025-08-10T18:04:03.392133Z - Checkpoint: processed=6300 nuggets=1443 last_path=frontend/node_modules/@swc/helpers/esm/_is_native_function.js last_sha1=935211fbde2692e94a88ccd2d102dff5b59ca813
2025-08-10T18:04:03.667356Z - Checkpoint: processed=6400 nuggets=1449 last_path=frontend/node_modules/@swc/helpers/cjs/_to_consumable_array.cjs last_sha1=ce772ba3dba4ed243a4243ee3a0b73056e654273
2025-08-10T18:04:04.262876Z - Checkpoint: processed=6500 nuggets=1565 last_path=frontend/node_modules/@react-aria/utils/dist/chain.main.js.map last_sha1=6e09374e3e8c79aa0877ba968a4eee43ce716da9
2025-08-10T18:04:04.923078Z - Checkpoint: processed=6600 nuggets=1740 last_path=frontend/node_modules/@react-aria/overlays/dist/sk-SK.module.js last_sha1=ef83427c162f86a80e1ea675a43ea1245108f297
2025-08-10T18:04:05.591510Z - Checkpoint: processed=6700 nuggets=1907 last_path=frontend/node_modules/@react-aria/utils/dist/useLabels.mjs last_sha1=80e5d4763f62187fc4b7d93a9342091a44a7bdae
2025-08-10T18:04:06.263243Z - Checkpoint: processed=6800 nuggets=2060 last_path=frontend/node_modules/@react-aria/utils/dist/keyboard.module.js last_sha1=f44558cb0c79dba22295776c6d7d851a2429b00c
2025-08-10T18:04:09.171747Z - Checkpoint: processed=6900 nuggets=2380 last_path=frontend/node_modules/axios/lib/core/README.md last_sha1=3b2abb091fc59ce28a44e729eedb38481204b58e
2025-08-10T18:04:11.234345Z - Checkpoint: processed=7000 nuggets=3272 last_path=frontend/node_modules/@vitejs/plugin-react/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:04:12.080267Z - Checkpoint: processed=7100 nuggets=3293 last_path=frontend/node_modules/babel-plugin-macros/LICENSE last_sha1=e7b670b4e0e9bd96d04d426541e168c0a570104e
2025-08-10T18:04:13.089492Z - Checkpoint: processed=7200 nuggets=3337 last_path=frontend/node_modules/@swc/helpers/src/_ts_values.mjs last_sha1=d6be6c9d871ebe6d3348bc95e0c9f09091ba4e22
2025-08-10T18:04:13.123817Z - Checkpoint: processed=7300 nuggets=3337 last_path=frontend/node_modules/@swc/helpers/src/_class_apply_descriptor_get.mjs last_sha1=e4c11b558dda0f910bfd9cca56181c9eeeab75a5
2025-08-10T18:04:13.225352Z - Checkpoint: processed=7400 nuggets=3341 last_path=frontend/node_modules/@swc/helpers/_/_ts_param/package.json last_sha1=a43714afb2453fb63bfdf3ad400b710ac0a161ee
2025-08-10T18:04:13.949255Z - Checkpoint: processed=7500 nuggets=3572 last_path=frontend/node_modules/@swc/helpers/_/_class_private_method_get/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:04:14.259086Z - Checkpoint: processed=7600 nuggets=3579 last_path=frontend/node_modules/@mui/x-date-pickers/node/internals/components/DateTimeViewWrapper/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:04:15.137722Z - Checkpoint: processed=7700 nuggets=3595 last_path=frontend/node_modules/@mui/x-date-pickers/node/AdapterMomentJalaali/index.js last_sha1=f8593ddc4509ac8b0b9a955b014c3838e66f460a
2025-08-10T18:04:15.803366Z - Checkpoint: processed=7800 nuggets=3601 last_path=frontend/node_modules/@mui/x-date-pickers/node/TimeClock/TimeClock.js last_sha1=9b5c5931bd9a8d44104cf5bbffe1ca0d0d487ff9
2025-08-10T18:04:16.325681Z - Checkpoint: processed=7900 nuggets=3639 last_path=frontend/node_modules/@mui/x-date-pickers/node/locales/roRO.js last_sha1=d6788d0e23105ed0090f23a6ad8a38eda57bb05a
2025-08-10T18:04:16.796365Z - Checkpoint: processed=8000 nuggets=3644 last_path=frontend/node_modules/@mui/x-date-pickers/modern/internals/utils/getDefaultReferenceDate.js last_sha1=b316cfe53fb0fd6a84c64199df48203d44559788
2025-08-10T18:04:17.400316Z - Checkpoint: processed=8100 nuggets=3664 last_path=frontend/node_modules/@mui/x-date-pickers/modern/DesktopTimePicker/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:04:18.011962Z - Checkpoint: processed=8200 nuggets=3677 last_path=frontend/node_modules/@mui/x-date-pickers/node/AdapterDayjs/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:04:18.470693Z - Checkpoint: processed=8300 nuggets=3702 last_path=frontend/node_modules/@mui/x-date-pickers/modern/PickersLayout/PickersLayout.types.js last_sha1=0a4248934294e546fd0a75f35d4bd5c7d9fbbe5e
2025-08-10T18:04:18.997074Z - Checkpoint: processed=8400 nuggets=3847 last_path=frontend/node_modules/@react-aria/interactions/dist/createEventHandler.module.js last_sha1=830b65c72b1f64720a8be1e83da3411924bcc87b
2025-08-10T18:04:19.632124Z - Checkpoint: processed=8500 nuggets=3997 last_path=frontend/node_modules/@react-aria/focus/dist/virtualFocus.module.js.map last_sha1=030da3c4011348afc05a6c27042fd1197abba096
2025-08-10T18:04:19.900760Z - Checkpoint: processed=8600 nuggets=4110 last_path=frontend/node_modules/@react-aria/overlays/dist/hu-HU.module.js.map last_sha1=8eb712ff69d76a4093b187a8440242084c05a84a
2025-08-10T18:04:21.044695Z - Checkpoint: processed=8700 nuggets=4276 last_path=frontend/node_modules/@react-aria/interactions/src/useFocusable.tsx last_sha1=6a88e9a8a126cd7837ca80463e87de7d848a7228
2025-08-10T18:04:22.215299Z - Checkpoint: processed=8800 nuggets=4309 last_path=frontend/node_modules/@mui/x-date-pickers/TimeClock/ClockPointer.js last_sha1=ba0cc2f5d33c424e3706bb040c40cc1d58fc9ec9
2025-08-10T18:04:22.658601Z - Checkpoint: processed=8900 nuggets=4383 last_path=frontend/node_modules/@popperjs/core/lib/modifiers/index.js.flow last_sha1=c370fc93729ba3feb7b92c13c735fd42f21da6e3
2025-08-10T18:04:22.895221Z - Checkpoint: processed=9000 nuggets=4383 last_path=frontend/node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js.flow last_sha1=9f0e721182c0532455712dc52f24a23f490bb43a
2025-08-10T18:04:23.434833Z - Checkpoint: processed=9100 nuggets=4388 last_path=frontend/node_modules/date-fns/esm/fp/eachYearOfInterval/index.js last_sha1=bb42717e67e18aa44b508d07675db472329ec9c2
2025-08-10T18:04:23.544881Z - Checkpoint: processed=9200 nuggets=4388 last_path=frontend/node_modules/date-fns/esm/fp/differenceInQuartersWithOptions/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:04:23.663982Z - Checkpoint: processed=9300 nuggets=4388 last_path=frontend/node_modules/date-fns/esm/fp/formatWithOptions/package.json last_sha1=b808c3482aadc0ec98ad21264c32295d978c0bd7
2025-08-10T18:04:23.773703Z - Checkpoint: processed=9400 nuggets=4388 last_path=frontend/node_modules/date-fns/esm/fp/endOfWeek/package.json last_sha1=b808c3482aadc0ec98ad21264c32295d978c0bd7
2025-08-10T18:04:23.913794Z - Checkpoint: processed=9500 nuggets=4388 last_path=frontend/node_modules/date-fns/esm/endOfISOWeekYear/package.json last_sha1=6d9232fc8ec0a2f41dfb047446bf375759eb9ea3
2025-08-10T18:04:24.133702Z - Checkpoint: processed=9600 nuggets=4392 last_path=frontend/node_modules/date-fns/esm/differenceInMinutes/index.d.ts last_sha1=27c145105e36c6a9ee172f092712e82f12805192
2025-08-10T18:04:24.276583Z - Checkpoint: processed=9700 nuggets=4392 last_path=frontend/node_modules/date-fns/esm/fp/differenceInCalendarWeeksWithOptions/index.js.flow last_sha1=6074fd919bd1072e07075e565a3f05c55449546e
2025-08-10T18:04:24.512664Z - Checkpoint: processed=9800 nuggets=4422 last_path=frontend/node_modules/date-fns/esm/fp/addMonths/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:04:24.616495Z - Checkpoint: processed=9900 nuggets=4422 last_path=frontend/node_modules/date-fns/esm/fp/setSeconds/index.js last_sha1=efb461847623382a22320ed894c2c359fbf1f8e2
2025-08-10T18:04:24.737855Z - Checkpoint: processed=10000 nuggets=4422 last_path=frontend/node_modules/date-fns/esm/fp/quartersToMonths/index.js last_sha1=6cff0872ca53c692602eeec54ac1891575e2c784
2025-08-10T18:04:24.849615Z - Checkpoint: processed=10100 nuggets=4422 last_path=frontend/node_modules/date-fns/esm/fp/subYears/index.d.ts last_sha1=9df8887e9052d2a83b8937db045be5d8df0a7726
2025-08-10T18:04:24.974349Z - Checkpoint: processed=10200 nuggets=4422 last_path=frontend/node_modules/date-fns/esm/fp/startOfMinute/package.json last_sha1=b808c3482aadc0ec98ad21264c32295d978c0bd7
2025-08-10T18:04:25.096642Z - Checkpoint: processed=10300 nuggets=4422 last_path=frontend/node_modules/date-fns/esm/fp/isMonday/index.js.flow last_sha1=8a0c47af4dd516740e83c47a2e280189231c3a7d
2025-08-10T18:04:25.257528Z - Checkpoint: processed=10400 nuggets=4422 last_path=frontend/node_modules/date-fns/esm/fp/hoursToMinutes/index.js.flow last_sha1=787edf242d20130d73946cf155dcbc3979fd596c
2025-08-10T18:04:25.389903Z - Checkpoint: processed=10500 nuggets=4422 last_path=frontend/node_modules/date-fns/esm/fp/monthsToQuarters/index.d.ts last_sha1=d9c366fbf22edc7378c9eba24baf145ab45102b6
2025-08-10T18:04:25.495555Z - Checkpoint: processed=10600 nuggets=4422 last_path=frontend/node_modules/caniuse-lite/data/features/tabindex-attr.js last_sha1=e0abbc53adaaf44a77dbc145a12e7aa4f398eb54
2025-08-10T18:04:25.673168Z - Checkpoint: processed=10700 nuggets=4422 last_path=frontend/node_modules/caniuse-lite/data/features/namevalue-storage.js last_sha1=5fdd9bf2b655b069e8fdfc97f8b1d4d51caf4661
2025-08-10T18:04:25.868144Z - Checkpoint: processed=10800 nuggets=4425 last_path=frontend/node_modules/caniuse-lite/data/regions/IT.js last_sha1=2f1e68cf73074b14b2b45f5d1ef4baa5a222221a
2025-08-10T18:04:26.160619Z - Checkpoint: processed=10900 nuggets=4429 last_path=frontend/node_modules/caniuse-lite/data/regions/AL.js last_sha1=48555b6783b1a4c5bb7461c78d831cbe2a4c2265
2025-08-10T18:04:26.393091Z - Checkpoint: processed=11000 nuggets=4429 last_path=frontend/node_modules/caniuse-lite/data/features/css-clip-path.js last_sha1=ea959bdaf4a7d2545b8d3141648d1832965b053f
2025-08-10T18:04:26.602959Z - Checkpoint: processed=11100 nuggets=4431 last_path=frontend/node_modules/call-bind-apply-helpers/test/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:04:26.808853Z - Checkpoint: processed=11200 nuggets=4435 last_path=frontend/node_modules/caniuse-lite/data/features/hashchange.js last_sha1=204f3866b5cc92a2dbebd10d575badbb56ab7246
2025-08-10T18:04:26.990778Z - Checkpoint: processed=11300 nuggets=4438 last_path=frontend/node_modules/caniuse-lite/data/features/css-touch-action.js last_sha1=d8ad24e29784dd869491426c7371a04df0bc10c8
2025-08-10T18:04:27.257905Z - Checkpoint: processed=11400 nuggets=4449 last_path=frontend/node_modules/date-fns/eachDayOfInterval/index.d.ts last_sha1=834152248e484d4a8a0de9ea3ce0c1cb59b4a549
2025-08-10T18:04:27.413024Z - Checkpoint: processed=11500 nuggets=4449 last_path=frontend/node_modules/date-fns/differenceInWeeks/index.d.ts last_sha1=6f610f8f7b056c1aef3123b5d501369d43c88aa1
2025-08-10T18:04:27.604371Z - Checkpoint: processed=11600 nuggets=4450 last_path=frontend/node_modules/date-fns/esm/differenceInCalendarQuarters/index.d.ts last_sha1=3fae765e11c5ba5cbf77c0df93f1d79d39431d6a
2025-08-10T18:04:27.833798Z - Checkpoint: processed=11700 nuggets=4450 last_path=frontend/node_modules/date-fns/esm/addSeconds/package.json last_sha1=6d9232fc8ec0a2f41dfb047446bf375759eb9ea3
2025-08-10T18:04:28.163562Z - Checkpoint: processed=11800 nuggets=4566 last_path=frontend/node_modules/cosmiconfig/dist/types.d.ts last_sha1=7bdfe200a291921d55359ddccd3a14fb66f0ab93
2025-08-10T18:04:28.468381Z - Checkpoint: processed=11900 nuggets=4590 last_path=frontend/node_modules/caniuse-lite/data/regions/TM.js last_sha1=f12d2e849e8e1f3a2f721013ef6cbf8821e18873
2025-08-10T18:04:28.668545Z - Checkpoint: processed=12000 nuggets=4590 last_path=frontend/node_modules/date-fns/addYears/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:04:33.934956Z - Checkpoint: processed=12100 nuggets=5281 last_path=frontend/node_modules/cross-spawn/package.json last_sha1=9becaa8ecb51ad9b303dd62369423cb9f287163a
2025-08-10T18:04:34.895342Z - Checkpoint: processed=12200 nuggets=5577 last_path=node_modules/@esbuild/win32-x64/package.json last_sha1=a2d572540e5100e86baf8c7ab7413ee62e03ddac
2025-08-10T18:04:35.226743Z - Checkpoint: processed=12300 nuggets=5625 last_path=node_modules/@inquirer/core/dist/esm/lib/theme.js last_sha1=18d64351c55e01ab0153a59d31990ec389de527a
2025-08-10T18:04:35.479251Z - Checkpoint: processed=12400 nuggets=5639 last_path=node_modules/@inquirer/checkbox/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:04:35.934713Z - Checkpoint: processed=12500 nuggets=5721 last_path=node_modules/@emotion/is-prop-valid/src/index.ts last_sha1=56e05d1c3d47b2740545616dca32004e3f4ba4b6
2025-08-10T18:04:36.837015Z - Checkpoint: processed=12600 nuggets=5836 last_path=node_modules/@emotion/babel-plugin/src/utils/transform-expression-with-styles.js last_sha1=aa9dcc103648b76cf38d437111b90ca92ea5eacb
2025-08-10T18:04:37.843073Z - Checkpoint: processed=12700 nuggets=6307 last_path=node_modules/@emotion/serialize/dist/emotion-serialize.development.cjs.mjs last_sha1=5a1287748649409bab7ee2b18c1bfdc50e69b749
2025-08-10T18:04:38.179515Z - Checkpoint: processed=12800 nuggets=6397 last_path=node_modules/@emotion/react/dist/declarations/src/global.d.ts last_sha1=53cb7af655aac4112c6cfccdc138100e5241f20a
2025-08-10T18:04:38.932712Z - Checkpoint: processed=12900 nuggets=6714 last_path=node_modules/@modelcontextprotocol/sdk/dist/esm/examples/server/simpleStatelessStreamableHttp.d.ts last_sha1=97062f1d0051cad32aec41526be064092273b731
2025-08-10T18:04:48.526269Z - Checkpoint: processed=13000 nuggets=18094 last_path=node_modules/@modelcontextprotocol/sdk/dist/cjs/shared/protocol.js.map last_sha1=877bfb0d4ca17724f423ef3f45a72ceedd35addd
2025-08-10T18:04:49.660570Z - Checkpoint: processed=13100 nuggets=19004 last_path=node_modules/@modelcontextprotocol/sdk/node_modules/iconv-lite/.idea/inspectionProfiles/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:04:51.207604Z - Checkpoint: processed=13200 nuggets=19410 last_path=node_modules/@modelcontextprotocol/sdk/node_modules/express/lib/request.js last_sha1=daa78d6150525a3691617d0f26511e94914db185
2025-08-10T18:04:52.172387Z - Checkpoint: processed=13300 nuggets=19687 last_path=node_modules/@jridgewell/set-array/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:04:52.829251Z - Checkpoint: processed=13400 nuggets=19921 last_path=node_modules/@inquirer/rawlist/dist/commonjs/package.json last_sha1=9d0d83ae5d399d96b36d228e614a575fc209d488
2025-08-10T18:04:53.459025Z - Checkpoint: processed=13500 nuggets=20288 last_path=node_modules/@modelcontextprotocol/sdk/dist/cjs/server/auth/handlers/token.js last_sha1=200f617208550c5b85a92d0a5e528db98000b44c
2025-08-10T18:05:04.031052Z - Checkpoint: processed=13600 nuggets=32521 last_path=node_modules/@modelcontextprotocol/sdk/dist/cjs/examples/client/multipleClientsParallel.js last_sha1=34ba688893226af77558782b4ca165d413632f91
2025-08-10T18:05:04.752024Z - Checkpoint: processed=13700 nuggets=32691 last_path=node_modules/@babel/core/lib/tools/build-external-helpers.js.map last_sha1=caedfd40c491433eb11195bccddc15f48b07d063
2025-08-10T18:05:05.454820Z - Checkpoint: processed=13800 nuggets=32799 last_path=node_modules/@babel/core/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:05:06.179955Z - Checkpoint: processed=13900 nuggets=32913 last_path=node_modules/@babel/helpers/lib/helpers/arrayLikeToArray.js last_sha1=f7a82cdc630013d6efaca44ecc0285975bd10cbf
2025-08-10T18:05:07.156646Z - Checkpoint: processed=14000 nuggets=33064 last_path=node_modules/@babel/helper-compilation-targets/lib/filter-items.js last_sha1=8f52f008933284b44b71271a538695e314954b42
2025-08-10T18:05:07.749657Z - Checkpoint: processed=14100 nuggets=33313 last_path=node_modules/.bin/update-browserslist-db.cmd last_sha1=a3c1b17f1a773fe222cfb2e0cd0b04b01d7fb608
2025-08-10T18:05:08.569065Z - Checkpoint: processed=14200 nuggets=33646 last_path=node_modules/@anthropic-ai/sdk/src/_shims/web-types.mjs last_sha1=d996d881d969897de2e4a4a8b85b273385706352
2025-08-10T18:05:09.364592Z - Checkpoint: processed=14300 nuggets=34198 last_path=node_modules/@anthropic-ai/sdk/resources/messages/index.mjs last_sha1=82da86af922645271cc0cb270a0eea9ae4d148eb
2025-08-10T18:05:10.507454Z - Checkpoint: processed=14400 nuggets=34715 last_path=node_modules/@babel/traverse/lib/path/modification.js last_sha1=adc578ed4d76ddeeac30c4c8aa68eb6b3f219693
2025-08-10T18:05:13.064980Z - Checkpoint: processed=14500 nuggets=35689 last_path=node_modules/@babel/runtime/helpers/esm/initializerWarningHelper.js last_sha1=a0e1e5290fe904b6e433f27a74aad22abc13ae45
2025-08-10T18:05:13.283118Z - Checkpoint: processed=14600 nuggets=35740 last_path=node_modules/@babel/types/lib/modifications/prependToMemberExpression.js.map last_sha1=e983019e1ab8f5e7e17e80b46ebad449dfb9dba3
2025-08-10T18:05:14.372523Z - Checkpoint: processed=14700 nuggets=35960 last_path=node_modules/@babel/types/lib/builders/flow/createTypeAnnotationBasedOnTypeof.js.map last_sha1=32b4a174b89367ad312451231f3bb00bfb77ae69
2025-08-10T18:05:15.091746Z - Checkpoint: processed=14800 nuggets=36144 last_path=node_modules/@babel/helpers/lib/helpers/nonIterableSpread.js.map last_sha1=4550e399f259f92e44b703ec7f113d0d458f5b76
2025-08-10T18:05:15.288982Z - Checkpoint: processed=14900 nuggets=36244 last_path=node_modules/@babel/helpers/lib/helpers/classPrivateFieldDestructureSet.js last_sha1=5b7af159f46efb4277872319f79e35a173794ee9
2025-08-10T18:05:15.493818Z - Checkpoint: processed=15000 nuggets=36291 last_path=node_modules/@babel/runtime/helpers/esm/classPrivateFieldGet.js last_sha1=919ae39e5aa207161c6ea832f2a9bd3bed7345fc
2025-08-10T18:05:18.048270Z - Checkpoint: processed=15100 nuggets=36519 last_path=node_modules/@babel/runtime/helpers/classStaticPrivateMethodGet.js last_sha1=c02155c8b8b0cd781b2cac19f76cf7acc6e0fa56
2025-08-10T18:05:18.140461Z - Checkpoint: processed=15200 nuggets=36519 last_path=node_modules/@mui/icons-material/AirplanemodeActiveSharp.js last_sha1=0da19826f91a5cbb0d7f33d724f9aed16ada3d0e
2025-08-10T18:05:18.238837Z - Checkpoint: processed=15300 nuggets=36520 last_path=node_modules/@mui/icons-material/AirlineSeatFlatAngledOutlined.d.ts last_sha1=****************************************
2025-08-10T18:05:18.324140Z - Checkpoint: processed=15400 nuggets=36520 last_path=node_modules/@mui/icons-material/AlignVerticalTopOutlined.d.ts last_sha1=****************************************
2025-08-10T18:05:18.415726Z - Checkpoint: processed=15500 nuggets=36520 last_path=node_modules/@mui/icons-material/AirTwoTone.js last_sha1=2eafa9ee2d24593b12dcdb055ba4147e0387e60f
2025-08-10T18:05:18.504098Z - Checkpoint: processed=15600 nuggets=36520 last_path=node_modules/@mui/icons-material/AddBoxOutlined.js last_sha1=6d675f7a89d7602df1a457a137a1bbb41a50955e
2025-08-10T18:05:18.599608Z - Checkpoint: processed=15700 nuggets=36520 last_path=node_modules/@mui/icons-material/AccountBalanceRounded.js last_sha1=d437698b0c5ffb7772c1f2f86fa588673e97329d
2025-08-10T18:05:18.687988Z - Checkpoint: processed=15800 nuggets=36522 last_path=node_modules/@mui/icons-material/AddCommentOutlined.js last_sha1=b6abc60d0ab09eba05563c57519e98b893880919
2025-08-10T18:05:18.784239Z - Checkpoint: processed=15900 nuggets=36535 last_path=node_modules/@mui/icons-material/AssistWalkerOutlined.d.ts last_sha1=****************************************
2025-08-10T18:05:18.863104Z - Checkpoint: processed=16000 nuggets=36535 last_path=node_modules/@mui/icons-material/AssessmentRounded.js last_sha1=00f588409c07b09a59a2c55cd31dd86600ab9c5e
2025-08-10T18:05:18.953252Z - Checkpoint: processed=16100 nuggets=36535 last_path=node_modules/@mui/icons-material/AutoModeSharp.js last_sha1=73788a6587337fb53e0bc935e733017fe18b6922
2025-08-10T18:05:19.059104Z - Checkpoint: processed=16200 nuggets=36535 last_path=node_modules/@mui/icons-material/AudiotrackSharp.js last_sha1=141d5ae5f98f14deb06999a46c4a83e4efb9cf6c
2025-08-10T18:05:19.148481Z - Checkpoint: processed=16300 nuggets=36535 last_path=node_modules/@mui/icons-material/AppBlockingSharp.js last_sha1=c2f7f3263b4e0e74ff3c197bfb8cd9b05e693384
2025-08-10T18:05:19.245360Z - Checkpoint: processed=16400 nuggets=36535 last_path=node_modules/@mui/icons-material/AlternateEmailRounded.d.ts last_sha1=****************************************
2025-08-10T18:05:19.322164Z - Checkpoint: processed=16500 nuggets=36535 last_path=node_modules/@mui/icons-material/ArrowForwardIosOutlined.d.ts last_sha1=****************************************
2025-08-10T18:05:19.392344Z - Checkpoint: processed=16600 nuggets=36535 last_path=node_modules/@mui/icons-material/ArrowBackIosNewSharp.js last_sha1=3517c8a5dfba8ffd7d87ec9ad3b9c2fd95985db7
2025-08-10T18:05:19.556543Z - Checkpoint: processed=16700 nuggets=36536 last_path=node_modules/@mui/base/modern/Snackbar/Snackbar.js last_sha1=b431e68e5248fde5eef7c4e71bffaab0c298d035
2025-08-10T18:05:19.815024Z - Checkpoint: processed=16800 nuggets=36550 last_path=node_modules/@mui/base/modern/index.js last_sha1=81302b9d633ca6732ba505e72e285bc5541d06ab
2025-08-10T18:05:20.101130Z - Checkpoint: processed=16900 nuggets=36566 last_path=node_modules/@mui/base/node/Button/buttonClasses.js last_sha1=484c26ab12ae6f5bc1f673c10880a28b43229a66
2025-08-10T18:05:20.366426Z - Checkpoint: processed=17000 nuggets=36575 last_path=node_modules/@mui/base/modern/useMenu/menuReducer.js last_sha1=c2714207fbb18be057fea3393b615d8d2c758dfe
2025-08-10T18:05:20.640264Z - Checkpoint: processed=17100 nuggets=36584 last_path=node_modules/@mui/base/legacy/Menu/index.js last_sha1=96f39d07089adaf81f822b2ef1cdca0ceaf92f54
2025-08-10T18:05:22.396691Z - Checkpoint: processed=17200 nuggets=36723 last_path=node_modules/@mui/base/Button/index.d.ts last_sha1=508ff0993207a205b96cda9afe40a05141215f14
2025-08-10T18:05:22.787116Z - Checkpoint: processed=17300 nuggets=36732 last_path=node_modules/@mui/base/legacy/useMenuItem/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:05:23.174402Z - Checkpoint: processed=17400 nuggets=36741 last_path=node_modules/@mui/base/legacy/TabsList/tabsListClasses.js last_sha1=1fc5e7c5fd77536a7f052de5d772f31b9f2fcad3
2025-08-10T18:05:23.658728Z - Checkpoint: processed=17500 nuggets=36811 last_path=node_modules/@mui/base/useList/listActions.types.js last_sha1=cceba200a926aae22ec331085abc958d3934a0e2
2025-08-10T18:05:23.989953Z - Checkpoint: processed=17600 nuggets=36890 last_path=node_modules/@mui/base/utils/omitEventHandlers.js last_sha1=dffac77eaa8199f0b3b35a180b9c4b073377a449
2025-08-10T18:05:24.161985Z - Checkpoint: processed=17700 nuggets=36908 last_path=node_modules/@mui/base/useSelect/index.d.ts last_sha1=dc6e022c7f7bed16ccde165820cfe199e81a1f2b
2025-08-10T18:05:24.397897Z - Checkpoint: processed=17800 nuggets=36936 last_path=node_modules/@mui/base/node/Unstable_Popup/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:05:24.861202Z - Checkpoint: processed=17900 nuggets=36946 last_path=node_modules/@mui/base/node/MenuButton/MenuButton.types.js last_sha1=eb2afc238ec1628cfcb0086c27ef5a4c59ff6939
2025-08-10T18:05:25.230199Z - Checkpoint: processed=18000 nuggets=36967 last_path=node_modules/@mui/base/Option/package.json last_sha1=d531fe9ef0dce8a0d7b951cf2414ae6801c1f12b
2025-08-10T18:05:25.594459Z - Checkpoint: processed=18100 nuggets=37016 last_path=node_modules/@mui/base/node/useMenu/useMenu.types.js last_sha1=eb2afc238ec1628cfcb0086c27ef5a4c59ff6939
2025-08-10T18:05:25.977038Z - Checkpoint: processed=18200 nuggets=37019 last_path=frontend/node_modules/react-i18next/dist/commonjs/context.js last_sha1=7ed7d5ba0133f320e8ee42214ac9643b77ca0ef8
2025-08-10T18:05:26.466343Z - Checkpoint: processed=18300 nuggets=37043 last_path=frontend/node_modules/react-dropzone/examples/theme.css last_sha1=6b79aff13eb8b736d6ecdd6f88ca4414067abe91
2025-08-10T18:05:27.710370Z - Checkpoint: processed=18400 nuggets=37172 last_path=frontend/node_modules/react-query/lib/core/queryObserver.js last_sha1=9929d5215dd36f5a55c9610815b564c66512ac1f
2025-08-10T18:05:29.615281Z - Checkpoint: processed=18500 nuggets=37461 last_path=frontend/node_modules/react-query/es/react/useIsMutating.js last_sha1=955826ff20bf3c9d05f1502025b7114a4685102f
2025-08-10T18:05:29.886385Z - Checkpoint: processed=18600 nuggets=37481 last_path=frontend/node_modules/path-is-absolute/package.json last_sha1=51b80416ea8aff0f6f04b15ee2e114250ba1a14c
2025-08-10T18:05:30.527432Z - Checkpoint: processed=18700 nuggets=37572 last_path=frontend/node_modules/nanoid/non-secure/index.cjs last_sha1=a56b87b5ecf963260a1c647c2ffb33815ce0452e
2025-08-10T18:05:35.359737Z - Checkpoint: processed=18800 nuggets=38069 last_path=frontend/node_modules/react-dom/umd/react-dom-test-utils.production.min.js last_sha1=2516d11259a39256f565205b8f9bf3e00fc36eec
2025-08-10T18:05:40.831565Z - Checkpoint: processed=18900 nuggets=38693 last_path=frontend/node_modules/postcss/lib/comment.d.ts last_sha1=047fa5f0e4ef7f6b7c835ceea13051819768dc4b
2025-08-10T18:05:46.388844Z - Checkpoint: processed=19000 nuggets=39279 last_path=frontend/node_modules/semver/bin/semver.js last_sha1=562a909c318904cf379778a9ba3b1c0e865b0fc7
2025-08-10T18:05:46.758543Z - Checkpoint: processed=19100 nuggets=39392 last_path=frontend/node_modules/rimraf/dist/esm/retry-busy.js.map last_sha1=9928030719f4d7830a72cf69268565c112800c9d
2025-08-10T18:05:47.711114Z - Checkpoint: processed=19200 nuggets=39583 last_path=frontend/node_modules/ts-retry-promise/dist/timeout.js last_sha1=69358de13d79b30f782a4c0b8601fb8b5f75e848
2025-08-10T18:05:50.160430Z - Checkpoint: processed=19300 nuggets=41150 last_path=frontend/node_modules/string-width-cjs/node_modules/emoji-regex/index.js last_sha1=e678799abbf2035d94ab0114ae0783b36a3e5994
2025-08-10T18:05:50.935725Z - Checkpoint: processed=19400 nuggets=41248 last_path=frontend/node_modules/react-query/types/ts3.8/index.d.ts last_sha1=e36feb2638afbfc4f1609a93a6e705dc9bc14904
2025-08-10T18:05:52.853075Z - Checkpoint: processed=19500 nuggets=41474 last_path=frontend/node_modules/resolve/test/shadowed_core/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:05:53.034113Z - Checkpoint: processed=19600 nuggets=41542 last_path=frontend/node_modules/resolve/test/node_path/y/ccc/index.js last_sha1=d85cf7f3160ae2e3e717618e36e6cdc54f5a4c7e
2025-08-10T18:05:53.823375Z - Checkpoint: processed=19700 nuggets=41679 last_path=frontend/node_modules/file-selector/src/index.ts last_sha1=f21ca35f5912def4827350ff781a79e848d0e713
2025-08-10T18:05:54.200203Z - Checkpoint: processed=19800 nuggets=41765 last_path=frontend/node_modules/es-define-property/CHANGELOG.md last_sha1=9b98f7992788e232883f393ff5fded3daaaa2aa8
2025-08-10T18:05:54.616604Z - Checkpoint: processed=19900 nuggets=41811 last_path=frontend/node_modules/framer-motion/dist/es/components/LayoutGroup/index.mjs last_sha1=134d2ec27def85933866f5bd034370aeb8f594f7
2025-08-10T18:05:55.760239Z - Checkpoint: processed=20000 nuggets=41847 last_path=frontend/node_modules/framer-motion/dist/framer-motion.js last_sha1=776133b0a143f7625e307194afa5ba081426b69d
2025-08-10T18:05:57.833459Z - Checkpoint: processed=20100 nuggets=42116 last_path=frontend/node_modules/dom-helpers/cjs/scrollTop.d.ts last_sha1=fbc6060ca0af9ab22a15b863a779aef5e746dca4
2025-08-10T18:05:57.943761Z - Checkpoint: processed=20200 nuggets=42118 last_path=frontend/node_modules/dom-helpers/cjs/offset.d.ts last_sha1=e11510c10af058d8a2586838f49454633f75657b
2025-08-10T18:05:58.054532Z - Checkpoint: processed=20300 nuggets=42119 last_path=frontend/node_modules/electron-to-chromium/package.json last_sha1=79954788fcf1b3ed056c7dd899532c1b25a8ad2e
2025-08-10T18:05:58.481137Z - Checkpoint: processed=20400 nuggets=42121 last_path=frontend/node_modules/dom-helpers/isVisible/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:05:59.976198Z - Checkpoint: processed=20500 nuggets=42434 last_path=frontend/node_modules/isexe/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:06:00.323569Z - Checkpoint: processed=20600 nuggets=42514 last_path=frontend/node_modules/html-parse-stringify/dist/html-parse-stringify.js.map last_sha1=60caed42f73af82bbc73deedb27106f88ed51925
2025-08-10T18:06:00.819671Z - Checkpoint: processed=20700 nuggets=42546 last_path=frontend/node_modules/math-intrinsics/constants/maxValue.js last_sha1=fdd351701c00ce4989c5c677b0a907acd073c110
2025-08-10T18:06:01.953210Z - Checkpoint: processed=20800 nuggets=42655 last_path=frontend/node_modules/json-parse-even-better-errors/CHANGELOG.md last_sha1=88556e596d2ee841afdc4c2682a385a77bca930e
2025-08-10T18:06:02.497001Z - Checkpoint: processed=20900 nuggets=42667 last_path=frontend/node_modules/framer-motion/dist/es/utils/reduced-motion/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:06:02.813925Z - Checkpoint: processed=21000 nuggets=42672 last_path=frontend/node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs last_sha1=0cddf9327b5457fbfcae166fa09e0fe13650b9cd
2025-08-10T18:06:03.617364Z - Checkpoint: processed=21100 nuggets=42770 last_path=frontend/node_modules/framer-motion/node_modules/@emotion/is-prop-valid/dist/is-prop-valid.esm.js last_sha1=73b27d0a182272deaf53bbce218c7042b8af91a9
2025-08-10T18:06:04.018732Z - Checkpoint: processed=21200 nuggets=42844 last_path=frontend/venv/Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:06:06.096881Z - Checkpoint: processed=21300 nuggets=43268 last_path=frontend/venv/Lib/site-packages/setuptools/_deprecation_warning.py last_sha1=9f554ff229a777b55816e0b8b39070efbe8df585
2025-08-10T18:06:07.359834Z - Checkpoint: processed=21400 nuggets=43394 last_path=frontend/venv/Lib/site-packages/setuptools/_vendor/packaging/version.py last_sha1=161edb467745642554aff7ee33a3eb69ff9e7287
2025-08-10T18:06:08.478493Z - Checkpoint: processed=21500 nuggets=43882 last_path=frontend/venv/Lib/site-packages/setuptools/_distutils/command/__pycache__/py37compat.cpython-311.pyc last_sha1=c3f9310372db43570d8aed490884dab9310d4d0f
2025-08-10T18:06:09.130488Z - Checkpoint: processed=21600 nuggets=44129 last_path=frontend/venv/Lib/site-packages/pip/_vendor/rich/__pycache__/_pick.cpython-311.pyc last_sha1=1d5cb72038133e6dee135074b1aeda9c5f9292d5
2025-08-10T18:06:10.292900Z - Checkpoint: processed=21700 nuggets=44352 last_path=frontend/venv/Lib/site-packages/pip/_vendor/rich/syntax.py last_sha1=04a48c39db598db3d0b5ddd11e911193fbb866c6
2025-08-10T18:06:11.132901Z - Checkpoint: processed=21800 nuggets=44423 last_path=frontend/venv/Lib/site-packages/pkg_resources/_vendor/importlib_resources/__pycache__/simple.cpython-311.pyc last_sha1=3fa6cdc478a9cf586879d7c5660cbfaf5186b3dc
2025-08-10T18:06:12.858459Z - Checkpoint: processed=21900 nuggets=45048 last_path=frontend/venv/Lib/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:06:36.586048Z - Checkpoint: processed=22000 nuggets=67447 last_path=manifests/charts/gateways/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:06:37.540031Z - Checkpoint: processed=22100 nuggets=67654 last_path=istio/istio-1.20.3/samples/open-telemetry/loki/otel.yaml last_sha1=227e8d42b7abcbef2b95a3b4556a1837ca10949e
2025-08-10T18:06:38.034126Z - Checkpoint: processed=22200 nuggets=67722 last_path=node_modules/.bin/node-which.cmd last_sha1=2504aa805ed76a090b93a99689bcb86c999b2a74
2025-08-10T18:06:39.541528Z - Checkpoint: processed=22300 nuggets=68110 last_path=manifests/charts/istiod-remote/files/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:06:40.297287Z - Checkpoint: processed=22400 nuggets=68262 last_path=istio/istio-1.20.3/manifests/charts/istio-control/istio-discovery/files/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:06:40.736350Z - Checkpoint: processed=22500 nuggets=68349 last_path=frontend/venv/Scripts/pip.exe last_sha1=8dfc1777c34a79a9eb25989066204b0db3222686
2025-08-10T18:06:41.702363Z - Checkpoint: processed=22600 nuggets=68541 last_path=istio/istio-1.20.3/samples/bookinfo/src/reviews/reviews-wlpcfg/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:06:43.295252Z - Checkpoint: processed=22700 nuggets=68840 last_path=istio/istio-1.20.3/samples/addons/extras/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:06:43.928108Z - Checkpoint: processed=22800 nuggets=69037 last_path=frontend/node_modules/yaml/dist/schema/yaml-1.1/omap.d.ts last_sha1=47ff0c0833ae11b947a049068cab88a9a4dbca5e
2025-08-10T18:06:44.530644Z - Checkpoint: processed=22900 nuggets=69248 last_path=frontend/venv/Lib/site-packages/pip/_internal/cli/__pycache__/status_codes.cpython-311.pyc last_sha1=5cbefba5b6f8f52655edda2fcd9aa302fec43152
2025-08-10T18:06:45.446724Z - Checkpoint: processed=23000 nuggets=69484 last_path=frontend/src/components/EHP/EHPReview.tsx last_sha1=d80b6f70019c8ba51836cad9f8ea8744720f6eff
2025-08-10T18:06:47.112963Z - Checkpoint: processed=23100 nuggets=69984 last_path=frontend/node_modules/unload/dist/browserify.js last_sha1=902712216a4384534dcd0408de42a0f3c18ec2bd
2025-08-10T18:07:19.060194Z - Checkpoint: processed=23200 nuggets=79666 last_path=frontend/node_modules/typescript/lib/lib.es2019.d.ts last_sha1=5d45ed5920f17bae0bc4e62b5f606620e514f398
2025-08-10T18:07:23.430883Z - Checkpoint: processed=23300 nuggets=82121 last_path=frontend/node_modules/wrap-ansi-cjs/node_modules/strip-ansi/package.json last_sha1=892d549c672831716abe655f087946d2644f2852
2025-08-10T18:07:29.257272Z - Checkpoint: processed=23400 nuggets=83100 last_path=frontend/node_modules/use-sync-external-store/shim/with-selector.js last_sha1=6a730dd17993e705c02b158c32f77e0486594d97
2025-08-10T18:07:30.510939Z - Checkpoint: processed=23500 nuggets=83351 last_path=frontend/venv/Lib/site-packages/pip/_vendor/packaging/version.py last_sha1=161edb467745642554aff7ee33a3eb69ff9e7287
2025-08-10T18:07:31.800764Z - Checkpoint: processed=23600 nuggets=83484 last_path=frontend/venv/Lib/site-packages/pip/_vendor/colorama/tests/utils.py last_sha1=783c1793406edec31d678f9b859d1e789085bf2c
2025-08-10T18:07:33.238630Z - Checkpoint: processed=23700 nuggets=83621 last_path=frontend/venv/Lib/site-packages/pip/_vendor/rich/padding.py last_sha1=40e397786a4df256246c2e9e16c135b2a5cf8dd6
2025-08-10T18:07:34.343943Z - Checkpoint: processed=23800 nuggets=83783 last_path=frontend/venv/Lib/site-packages/pip/_vendor/pyproject_hooks/__init__.py last_sha1=df65d428064b7c8e03726669e00c2e42450c227c
2025-08-10T18:07:36.093488Z - Checkpoint: processed=23900 nuggets=84396 last_path=frontend/venv/Lib/site-packages/pip/_internal/utils/unpacking.py last_sha1=9761f6164167af6b9ef5f4bdb68299b6c48db277
2025-08-10T18:07:36.572857Z - Checkpoint: processed=24000 nuggets=84437 last_path=frontend/venv/Lib/site-packages/pip/_internal/operations/build/__pycache__/metadata_legacy.cpython-311.pyc last_sha1=831b592c3a6cd4833a8226cdb1f61992b325d5dc
2025-08-10T18:07:39.028134Z - Checkpoint: processed=24100 nuggets=84653 last_path=frontend/venv/Lib/site-packages/pip/_vendor/chardet/__pycache__/macromanprober.cpython-311.pyc last_sha1=78b6ef7bacc41d08461843a571eae13d58f12dff
2025-08-10T18:07:39.657922Z - Checkpoint: processed=24200 nuggets=84720 last_path=frontend/venv/Lib/site-packages/pip/_vendor/cachecontrol/heuristics.py last_sha1=c43016e83b44a6190cb42a3df0597737daa8cc77
2025-08-10T18:07:40.077227Z - Checkpoint: processed=24300 nuggets=84777 last_path=frontend/node_modules/@mui/icons-material/LyricsSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:40.156684Z - Checkpoint: processed=24400 nuggets=84777 last_path=frontend/node_modules/@mui/icons-material/LoopSharp.js last_sha1=923d354a1a7d14e2666b669920aabe048c0bd7ca
2025-08-10T18:07:40.246437Z - Checkpoint: processed=24500 nuggets=84782 last_path=frontend/node_modules/@mui/icons-material/MarkEmailReadRounded.js last_sha1=0093ecaa2f31625c86d9bcdf2b41f8da9b98c639
2025-08-10T18:07:40.333817Z - Checkpoint: processed=24600 nuggets=84782 last_path=frontend/node_modules/@mui/icons-material/LocalOfferTwoTone.js last_sha1=bb73d28a3f17f53d2146f06d4ba0200746aa8fd3
2025-08-10T18:07:40.412575Z - Checkpoint: processed=24700 nuggets=84782 last_path=frontend/node_modules/@mui/icons-material/LocalFireDepartmentSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:40.496371Z - Checkpoint: processed=24800 nuggets=84782 last_path=frontend/node_modules/@mui/icons-material/LockResetRounded.d.ts last_sha1=****************************************
2025-08-10T18:07:40.599389Z - Checkpoint: processed=24900 nuggets=84782 last_path=frontend/node_modules/@mui/icons-material/LocalShippingTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:40.679263Z - Checkpoint: processed=25000 nuggets=84782 last_path=frontend/node_modules/@mui/icons-material/MonitorWeightOutlined.js last_sha1=f4ede3ef99e747028ae3a8caec44759caf6be7a8
2025-08-10T18:07:40.764301Z - Checkpoint: processed=25100 nuggets=84784 last_path=frontend/node_modules/@mui/icons-material/ModelTrainingOutlined.d.ts last_sha1=****************************************
2025-08-10T18:07:40.847463Z - Checkpoint: processed=25200 nuggets=84787 last_path=frontend/node_modules/@mui/icons-material/MovieFilter.d.ts last_sha1=****************************************
2025-08-10T18:07:40.924423Z - Checkpoint: processed=25300 nuggets=84787 last_path=frontend/node_modules/@mui/icons-material/MosqueSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:41.010945Z - Checkpoint: processed=25400 nuggets=84787 last_path=frontend/node_modules/@mui/icons-material/Memory.d.ts last_sha1=****************************************
2025-08-10T18:07:41.095021Z - Checkpoint: processed=25500 nuggets=84787 last_path=frontend/node_modules/@mui/icons-material/Medication.d.ts last_sha1=****************************************
2025-08-10T18:07:41.174027Z - Checkpoint: processed=25600 nuggets=84787 last_path=frontend/node_modules/@mui/icons-material/ModeEditOutlined.d.ts last_sha1=****************************************
2025-08-10T18:07:41.267419Z - Checkpoint: processed=25700 nuggets=84787 last_path=frontend/node_modules/@mui/icons-material/MiscellaneousServicesSharp.js last_sha1=7230f1fb14622c9f10c3da4ed520f62beb157d1f
2025-08-10T18:07:41.348437Z - Checkpoint: processed=25800 nuggets=84787 last_path=frontend/node_modules/@mui/icons-material/InsertLinkTwoTone.js last_sha1=de2551cf84667039ba706849adc4018230d88417
2025-08-10T18:07:46.490252Z - Checkpoint: processed=25900 nuggets=85067 last_path=frontend/node_modules/@mui/icons-material/ImportContactsRounded.js last_sha1=779756fe2df6455fbefa42f919d702eb8b11193f
2025-08-10T18:07:46.572948Z - Checkpoint: processed=26000 nuggets=85067 last_path=frontend/node_modules/@mui/icons-material/Javascript.js last_sha1=a2659abe5bbaa12e67f2d82d91e89d9f483e5db1
2025-08-10T18:07:46.661746Z - Checkpoint: processed=26100 nuggets=85067 last_path=frontend/node_modules/@mui/icons-material/InterestsSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:46.749073Z - Checkpoint: processed=26200 nuggets=85075 last_path=frontend/node_modules/@mui/icons-material/HorizontalSplitOutlined.js last_sha1=5dbdb680165e11cba2a3c77250ead0c8e7be8a9f
2025-08-10T18:07:46.830110Z - Checkpoint: processed=26300 nuggets=85077 last_path=frontend/node_modules/@mui/icons-material/HlsOffRounded.d.ts last_sha1=****************************************
2025-08-10T18:07:46.914225Z - Checkpoint: processed=26400 nuggets=85078 last_path=frontend/node_modules/@mui/icons-material/HourglassFullSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:46.994765Z - Checkpoint: processed=26500 nuggets=85079 last_path=frontend/node_modules/@mui/icons-material/LibraryBooksTwoTone.js last_sha1=fb4e145e7076db3ad18dde1366746719e40b70cf
2025-08-10T18:07:47.080629Z - Checkpoint: processed=26600 nuggets=85079 last_path=frontend/node_modules/@mui/icons-material/LeakAdd.d.ts last_sha1=****************************************
2025-08-10T18:07:47.158785Z - Checkpoint: processed=26700 nuggets=85079 last_path=frontend/node_modules/@mui/icons-material/LocalAirportRounded.js last_sha1=c30d63ff3d30e267f2335190b302b48f2ac1bf28
2025-08-10T18:07:47.243952Z - Checkpoint: processed=26800 nuggets=85079 last_path=frontend/node_modules/@mui/icons-material/LineStyle.d.ts last_sha1=****************************************
2025-08-10T18:07:47.332785Z - Checkpoint: processed=26900 nuggets=85079 last_path=frontend/node_modules/@mui/icons-material/KeyboardCapslockTwoTone.js last_sha1=e1d0f12608cff9b3c79e44306eb824ac93fc992b
2025-08-10T18:07:47.423084Z - Checkpoint: processed=27000 nuggets=85079 last_path=frontend/node_modules/@mui/icons-material/KebabDiningSharp.js last_sha1=9e93d4750884a8e3ea1079a52f3528aa85ec1278
2025-08-10T18:07:47.507294Z - Checkpoint: processed=27100 nuggets=85079 last_path=frontend/node_modules/@mui/icons-material/LandscapeOutlined.js last_sha1=a3a5744346272bc6f3ba7e984808a086c79ac04c
2025-08-10T18:07:47.594201Z - Checkpoint: processed=27200 nuggets=85079 last_path=frontend/node_modules/@mui/icons-material/LabelImportantOutlined.js last_sha1=e5b276811a22ad1fa0369c0ecc8d780958c018eb
2025-08-10T18:07:47.681708Z - Checkpoint: processed=27300 nuggets=85080 last_path=frontend/node_modules/@mui/icons-material/PlayArrowTwoTone.js last_sha1=824a66e558f968ab2abde89e9b405c46007aacf9
2025-08-10T18:07:47.761244Z - Checkpoint: processed=27400 nuggets=85080 last_path=frontend/node_modules/@mui/icons-material/PieChartSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:47.852001Z - Checkpoint: processed=27500 nuggets=85080 last_path=frontend/node_modules/@mui/icons-material/PoolRounded.d.ts last_sha1=****************************************
2025-08-10T18:07:47.938518Z - Checkpoint: processed=27600 nuggets=85080 last_path=frontend/node_modules/@mui/icons-material/PlaylistAddSharp.js last_sha1=fb2b3fa37888150fc8d0751bacc2b778177de300
2025-08-10T18:07:48.023450Z - Checkpoint: processed=27700 nuggets=85080 last_path=frontend/node_modules/@mui/icons-material/PhoneEnabledRounded.js last_sha1=2426df58fc4c456d1e11b9c9b8f664f1fcf18a42
2025-08-10T18:07:48.165263Z - Checkpoint: processed=27800 nuggets=85080 last_path=frontend/node_modules/@mui/icons-material/PersonSearch.js last_sha1=bcae2c1024cf46838bff10e6b0d568fd6330114a
2025-08-10T18:07:48.252421Z - Checkpoint: processed=27900 nuggets=85085 last_path=frontend/node_modules/@mui/icons-material/PhotoSizeSelectActual.js last_sha1=df310bcc40c3edbc786104f0373e49de7e3e3ea6
2025-08-10T18:07:48.333549Z - Checkpoint: processed=28000 nuggets=85085 last_path=frontend/node_modules/@mui/icons-material/PhoneLockedRounded.d.ts last_sha1=****************************************
2025-08-10T18:07:48.415213Z - Checkpoint: processed=28100 nuggets=85085 last_path=frontend/node_modules/@mui/icons-material/RadarTwoTone.js last_sha1=b0bcf772751f28211952d23a646b75f691f655d7
2025-08-10T18:07:48.497741Z - Checkpoint: processed=28200 nuggets=85085 last_path=frontend/node_modules/@mui/icons-material/RemoveModeratorOutlined.d.ts last_sha1=****************************************
2025-08-10T18:07:48.578763Z - Checkpoint: processed=28300 nuggets=85086 last_path=frontend/node_modules/@mui/icons-material/RedoTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:48.660798Z - Checkpoint: processed=28400 nuggets=85086 last_path=frontend/node_modules/@mui/icons-material/PrintDisabled.js last_sha1=281fc6d76706acf393efb8f4f12fe55243ae8eeb
2025-08-10T18:07:48.745021Z - Checkpoint: processed=28500 nuggets=85086 last_path=frontend/node_modules/@mui/icons-material/PostAddRounded.d.ts last_sha1=****************************************
2025-08-10T18:07:48.828845Z - Checkpoint: processed=28600 nuggets=85086 last_path=frontend/node_modules/@mui/icons-material/QrCode2TwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:48.908159Z - Checkpoint: processed=28700 nuggets=85086 last_path=frontend/node_modules/@mui/icons-material/PropaneTankRounded.js last_sha1=60ed11858c37baa36df9af22079bb88d2d283657
2025-08-10T18:07:48.994472Z - Checkpoint: processed=28800 nuggets=85089 last_path=frontend/node_modules/@mui/icons-material/NoMeetingRoomTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:49.082251Z - Checkpoint: processed=28900 nuggets=85089 last_path=frontend/node_modules/@mui/icons-material/NoCrashRounded.js last_sha1=67f6fa407b9418bcb62f9414e041560799f3efa2
2025-08-10T18:07:49.168510Z - Checkpoint: processed=29000 nuggets=85089 last_path=frontend/node_modules/@mui/icons-material/OfflineBoltSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:49.254917Z - Checkpoint: processed=29100 nuggets=85089 last_path=frontend/node_modules/@mui/icons-material/NotificationsActive.js last_sha1=ae5a35a6fe13aeb56053675f94b64266405839a1
2025-08-10T18:07:49.331179Z - Checkpoint: processed=29200 nuggets=85089 last_path=frontend/node_modules/@mui/icons-material/NetworkWifi2BarSharp.js last_sha1=fbcfca8af37c2e0a48b3fd424a77de5f6cbc4854
2025-08-10T18:07:49.412029Z - Checkpoint: processed=29300 nuggets=85089 last_path=frontend/node_modules/@mui/icons-material/NearbyErrorSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:49.491938Z - Checkpoint: processed=29400 nuggets=85089 last_path=frontend/node_modules/@mui/icons-material/NineteenMp.d.ts last_sha1=****************************************
2025-08-10T18:07:49.572003Z - Checkpoint: processed=29500 nuggets=85089 last_path=frontend/node_modules/@mui/icons-material/NextWeek.d.ts last_sha1=****************************************
2025-08-10T18:07:49.661579Z - Checkpoint: processed=29600 nuggets=85098 last_path=frontend/node_modules/@mui/icons-material/Pentagon.d.ts last_sha1=****************************************
2025-08-10T18:07:49.742820Z - Checkpoint: processed=29700 nuggets=85098 last_path=frontend/node_modules/@mui/icons-material/PauseCircleOutline.js last_sha1=****************************************
2025-08-10T18:07:49.823199Z - Checkpoint: processed=29800 nuggets=85098 last_path=frontend/node_modules/@mui/icons-material/PersonAddDisabledTwoTone.js last_sha1=694d510f51ef34205309b1826e319bbdaba89572
2025-08-10T18:07:49.902671Z - Checkpoint: processed=29900 nuggets=85098 last_path=frontend/node_modules/@mui/icons-material/OpenInNewTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:49.980721Z - Checkpoint: processed=30000 nuggets=85098 last_path=frontend/node_modules/@mui/icons-material/OndemandVideoTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:50.080870Z - Checkpoint: processed=30100 nuggets=85098 last_path=frontend/node_modules/@mui/icons-material/PanoramaVerticalSelectOutlined.js last_sha1=8f21b03d3001f63f1d8d54dc56759063d4c1ac89
2025-08-10T18:07:50.158363Z - Checkpoint: processed=30200 nuggets=85098 last_path=frontend/node_modules/@mui/icons-material/PagesSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:50.250247Z - Checkpoint: processed=30300 nuggets=85098 last_path=frontend/node_modules/@mui/icons-material/DescriptionSharp.js last_sha1=7ee9a164c3a3ce41192b13d1ee48dfe01e4035c9
2025-08-10T18:07:50.325407Z - Checkpoint: processed=30400 nuggets=85098 last_path=frontend/node_modules/@mui/icons-material/DehazeSharp.js last_sha1=1c43f3b4a2eb320792f9296303b2ef183884fc7e
2025-08-10T18:07:50.403252Z - Checkpoint: processed=30500 nuggets=85098 last_path=frontend/node_modules/@mui/icons-material/DifferenceTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:50.486292Z - Checkpoint: processed=30600 nuggets=85098 last_path=frontend/node_modules/@mui/icons-material/DeviceHub.js last_sha1=cc120e118eaee870376d6b453157f4010085c5dc
2025-08-10T18:07:50.702913Z - Checkpoint: processed=30700 nuggets=85121 last_path=frontend/node_modules/@mui/base/useTabsList/useTabsList.js last_sha1=4f1e1116dc08491740f56f93143776aba55560e4
2025-08-10T18:07:50.886060Z - Checkpoint: processed=30800 nuggets=85168 last_path=frontend/node_modules/@mui/base/useMenuButton/useMenuButton.d.ts last_sha1=c5d2cd6d1e6fc8296d165985bf6ccc117d20831d
2025-08-10T18:07:51.081885Z - Checkpoint: processed=30900 nuggets=85186 last_path=frontend/node_modules/@mui/icons-material/DeckRounded.d.ts last_sha1=****************************************
2025-08-10T18:07:51.203879Z - Checkpoint: processed=31000 nuggets=85193 last_path=frontend/node_modules/@mui/icons-material/DataObjectTwoTone.js last_sha1=b71f06cf93cb6d6eb2958bd056578cdf4629ab2d
2025-08-10T18:07:51.287405Z - Checkpoint: processed=31100 nuggets=85193 last_path=frontend/node_modules/@mui/icons-material/DrawRounded.d.ts last_sha1=****************************************
2025-08-10T18:07:51.369598Z - Checkpoint: processed=31200 nuggets=85195 last_path=frontend/node_modules/@mui/icons-material/DoorBackSharp.js last_sha1=7524bfc10b3a333e280ec2d1c483d56a71415388
2025-08-10T18:07:51.455155Z - Checkpoint: processed=31300 nuggets=85198 last_path=frontend/node_modules/@mui/icons-material/EdgesensorLowRounded.js last_sha1=632a6a20f78681795c83ed996e71fc638bee2648
2025-08-10T18:07:51.543804Z - Checkpoint: processed=31400 nuggets=85198 last_path=frontend/node_modules/@mui/icons-material/DuoSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:51.634179Z - Checkpoint: processed=31500 nuggets=85198 last_path=frontend/node_modules/@mui/icons-material/DiscountTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:51.713876Z - Checkpoint: processed=31600 nuggets=85198 last_path=frontend/node_modules/@mui/icons-material/DoneAllSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:51.789722Z - Checkpoint: processed=31700 nuggets=85198 last_path=frontend/node_modules/@mui/icons-material/DockTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:51.885391Z - Checkpoint: processed=31800 nuggets=85198 last_path=frontend/node_modules/@mui/base/node/TabsList/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:07:52.274731Z - Checkpoint: processed=31900 nuggets=85206 last_path=frontend/node_modules/@mui/base/node/MenuButton/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:07:52.592785Z - Checkpoint: processed=32000 nuggets=85211 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/ClassNameGenerator/index.d.ts last_sha1=db035bc3a3a3df69ca08d87f1967be90f8661a4a
2025-08-10T18:07:53.028977Z - Checkpoint: processed=32100 nuggets=85232 last_path=frontend/node_modules/@mui/base/node/useList/useListItem.types.js last_sha1=eb2afc238ec1628cfcb0086c27ef5a4c59ff6939
2025-08-10T18:07:53.354547Z - Checkpoint: processed=32200 nuggets=85236 last_path=frontend/node_modules/@mui/base/modern/composeClasses/index.js last_sha1=dfdc7df29d7ed2b5065918c224e85e8e58614754
2025-08-10T18:07:53.595266Z - Checkpoint: processed=32300 nuggets=85251 last_path=frontend/node_modules/@mui/base/FormControl/package.json last_sha1=a701171cef1b9f574194bbdfff042916ff555735
2025-08-10T18:07:53.866096Z - Checkpoint: processed=32400 nuggets=85276 last_path=frontend/node_modules/@mui/base/modern/useMenuItem/useMenuItemContextStabilizer.js last_sha1=3f6f46d7df72a150b328bb81981e0b1d5d2cc00d
2025-08-10T18:07:54.142539Z - Checkpoint: processed=32500 nuggets=85284 last_path=frontend/node_modules/@mui/base/modern/useDropdown/index.js last_sha1=24455bd9db5b3a5e60a6dd9eca8a41f6bbb75f5b
2025-08-10T18:07:54.409016Z - Checkpoint: processed=32600 nuggets=85289 last_path=frontend/node_modules/@mui/base/NoSsr/index.d.ts last_sha1=392958f8b9e1c5ac5d6e42ad7a51847e1dd9b7a4
2025-08-10T18:07:54.539302Z - Checkpoint: processed=32700 nuggets=85302 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/useEnhancedEffect/useEnhancedEffect.js last_sha1=1ced3c131da83895590ad856d1e90c2c183f9d31
2025-08-10T18:07:54.819757Z - Checkpoint: processed=32800 nuggets=85350 last_path=frontend/node_modules/@mui/base/unstable_useNumberInput/useNumberInput.types.js last_sha1=0a4248934294e546fd0a75f35d4bd5c7d9fbbe5e
2025-08-10T18:07:55.168086Z - Checkpoint: processed=32900 nuggets=85440 last_path=frontend/node_modules/@mui/base/Tab/tabClasses.d.ts last_sha1=214814025ec07a7a100fe755a14f3006adf8076e
2025-08-10T18:07:55.320397Z - Checkpoint: processed=33000 nuggets=85461 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/exactProp/exactProp.js last_sha1=ec1ce85671fb09e946d1b8db642d189e26a21f61
2025-08-10T18:07:55.404051Z - Checkpoint: processed=33100 nuggets=85463 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/esm/getReactNodeRef/getReactNodeRef.js last_sha1=0672e73c987bd716ba3d80c70554f91a2e62153c
2025-08-10T18:07:55.493875Z - Checkpoint: processed=33200 nuggets=85475 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/modern/setRef/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:07:55.612140Z - Checkpoint: processed=33300 nuggets=85483 last_path=frontend/node_modules/@mui/base/node_modules/@mui/utils/modern/chainPropTypes/index.js last_sha1=61c5f3c4d11d416c13b7070fcffba9f9534ce4c3
2025-08-10T18:07:55.692947Z - Checkpoint: processed=33400 nuggets=85483 last_path=frontend/node_modules/@mui/icons-material/FormatIndentDecreaseSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:55.780913Z - Checkpoint: processed=33500 nuggets=85483 last_path=frontend/node_modules/@mui/icons-material/FourMpRounded.d.ts last_sha1=****************************************
2025-08-10T18:07:55.873520Z - Checkpoint: processed=33600 nuggets=85483 last_path=frontend/node_modules/@mui/icons-material/Forward5Outlined.js last_sha1=8c13d5580b98f77fdfd7a80643a02b0588541450
2025-08-10T18:07:55.960229Z - Checkpoint: processed=33700 nuggets=85483 last_path=frontend/node_modules/@mui/icons-material/FluorescentSharp.js last_sha1=9f9678fba48ac8950e287e73bb249d623152f112
2025-08-10T18:07:56.046614Z - Checkpoint: processed=33800 nuggets=85483 last_path=frontend/node_modules/@mui/icons-material/FlashOn.d.ts last_sha1=****************************************
2025-08-10T18:07:56.159517Z - Checkpoint: processed=33900 nuggets=85483 last_path=frontend/node_modules/@mui/icons-material/FoodBankTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:56.243407Z - Checkpoint: processed=34000 nuggets=85483 last_path=frontend/node_modules/@mui/icons-material/FolderOutlined.d.ts last_sha1=****************************************
2025-08-10T18:07:56.337699Z - Checkpoint: processed=34100 nuggets=85483 last_path=frontend/node_modules/@mui/icons-material/HdrOnSelectSharp.js last_sha1=aa27afbae71edca4a5c82e8e833397e26f8f48f2
2025-08-10T18:07:56.418907Z - Checkpoint: processed=34200 nuggets=85483 last_path=frontend/node_modules/@mui/icons-material/Handshake.d.ts last_sha1=****************************************
2025-08-10T18:07:56.509034Z - Checkpoint: processed=34300 nuggets=85483 last_path=frontend/node_modules/@mui/icons-material/HighlightOffOutlined.js last_sha1=e666fdf98656d41491de16dd617751614764c1f6
2025-08-10T18:07:56.591444Z - Checkpoint: processed=34400 nuggets=85483 last_path=frontend/node_modules/@mui/icons-material/HeatPumpTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:56.681697Z - Checkpoint: processed=34500 nuggets=85483 last_path=frontend/node_modules/@mui/icons-material/GradeRounded.d.ts last_sha1=****************************************
2025-08-10T18:07:56.768210Z - Checkpoint: processed=34600 nuggets=85483 last_path=frontend/node_modules/@mui/icons-material/GirlTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:56.854276Z - Checkpoint: processed=34700 nuggets=85484 last_path=frontend/node_modules/@mui/icons-material/Groups3.d.ts last_sha1=****************************************
2025-08-10T18:07:56.949869Z - Checkpoint: processed=34800 nuggets=85484 last_path=frontend/node_modules/@mui/icons-material/GrassRounded.js last_sha1=c8e0ed79cd17d801123655652cc5578a1a39ac46
2025-08-10T18:07:57.032821Z - Checkpoint: processed=34900 nuggets=85484 last_path=frontend/node_modules/@mui/icons-material/EventSeatOutlined.d.ts last_sha1=****************************************
2025-08-10T18:07:57.113673Z - Checkpoint: processed=35000 nuggets=85484 last_path=frontend/node_modules/@mui/icons-material/ErrorOutlineRounded.d.ts last_sha1=****************************************
2025-08-10T18:07:57.256740Z - Checkpoint: processed=35100 nuggets=85484 last_path=frontend/node_modules/@mui/icons-material/ExpandMoreSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:57.346287Z - Checkpoint: processed=35200 nuggets=85484 last_path=frontend/node_modules/@mui/icons-material/EightteenMp.d.ts last_sha1=****************************************
2025-08-10T18:07:57.470328Z - Checkpoint: processed=35300 nuggets=85484 last_path=frontend/node_modules/@mui/icons-material/EditRoad.js last_sha1=4baddd397e0967dda9b8478e4621b09428dbe005
2025-08-10T18:07:57.554851Z - Checkpoint: processed=35400 nuggets=85484 last_path=frontend/node_modules/@mui/icons-material/EmojiFlagsRounded.js last_sha1=697ca3401d25793fb59304b0a3fe905782088728
2025-08-10T18:07:57.653719Z - Checkpoint: processed=35500 nuggets=85484 last_path=frontend/node_modules/@mui/icons-material/ElectricRickshawTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:57.735560Z - Checkpoint: processed=35600 nuggets=85484 last_path=frontend/node_modules/@mui/icons-material/FilterListTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:57.819326Z - Checkpoint: processed=35700 nuggets=85484 last_path=frontend/node_modules/@mui/icons-material/Filter9Plus.js last_sha1=6d76a34e38920ba00de5d7e7d82be99ee24690c0
2025-08-10T18:07:57.897429Z - Checkpoint: processed=35800 nuggets=85487 last_path=frontend/node_modules/@mui/icons-material/FivteenMpTwoTone.js last_sha1=a10b0f826b8e01c8b4166f7b594c89a6b8af304c
2025-08-10T18:07:57.979116Z - Checkpoint: processed=35900 nuggets=85489 last_path=frontend/node_modules/@mui/icons-material/FireplaceSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:58.059446Z - Checkpoint: processed=36000 nuggets=85493 last_path=frontend/node_modules/@mui/icons-material/FiberManualRecord.js last_sha1=42014f98ce6de2f80719ab2b3c9fd6d07163ce81
2025-08-10T18:07:58.145338Z - Checkpoint: processed=36100 nuggets=85498 last_path=frontend/node_modules/@mui/icons-material/FavoriteBorderSharp.d.ts last_sha1=****************************************
2025-08-10T18:07:58.223678Z - Checkpoint: processed=36200 nuggets=85498 last_path=frontend/node_modules/@mui/icons-material/Filter8TwoTone.d.ts last_sha1=****************************************
2025-08-10T18:07:58.307233Z - Checkpoint: processed=36300 nuggets=85498 last_path=frontend/node_modules/@mui/icons-material/FilePresent.d.ts last_sha1=****************************************
2025-08-10T18:07:58.538452Z - Checkpoint: processed=36400 nuggets=85501 last_path=frontend/node_modules/@mui/material/modern/InputLabel/InputLabel.js last_sha1=4ce18268ebec6c8f6a29e0f35670d7c949e5c94f
2025-08-10T18:07:58.854429Z - Checkpoint: processed=36500 nuggets=85511 last_path=frontend/node_modules/@mui/material/modern/DialogContentText/dialogContentTextClasses.js last_sha1=d18b6b50636c3ef9c83d1b1470ed6488b36957cf
2025-08-10T18:07:59.394694Z - Checkpoint: processed=36600 nuggets=85519 last_path=frontend/node_modules/@mui/material/modern/Slide/Slide.js last_sha1=6dfdb0250252e56088da50effea347e65d6f3da7
2025-08-10T18:08:00.003716Z - Checkpoint: processed=36700 nuggets=85520 last_path=frontend/node_modules/@mui/material/modern/Modal/index.js last_sha1=2951488b7f5f2481596396b1e712f708e6694758
2025-08-10T18:08:00.276824Z - Checkpoint: processed=36800 nuggets=85547 last_path=frontend/node_modules/@mui/material/ListItemAvatar/index.d.ts last_sha1=7d55fee41e473278f7dd3261d0d881d40ae1fa79
2025-08-10T18:08:00.723022Z - Checkpoint: processed=36900 nuggets=85571 last_path=frontend/node_modules/@mui/material/modern/Breadcrumbs/BreadcrumbCollapsed.js last_sha1=14b441dbc4311d570188628e0383fa1d5cb2e0a5
2025-08-10T18:08:01.178422Z - Checkpoint: processed=37000 nuggets=85586 last_path=frontend/node_modules/@mui/material/Menu/menuClasses.js last_sha1=6d5864eae7a0305e0fa5d136353d48e49ac96041
2025-08-10T18:08:01.870477Z - Checkpoint: processed=37100 nuggets=85615 last_path=frontend/node_modules/@mui/material/node/internal/switchBaseClasses.js last_sha1=6362526dbf5256abc198109dc4a0f7b452f564d1
2025-08-10T18:08:02.343102Z - Checkpoint: processed=37200 nuggets=85623 last_path=frontend/node_modules/@mui/material/node/Fab/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:08:02.818246Z - Checkpoint: processed=37300 nuggets=85630 last_path=frontend/node_modules/@mui/material/node/Select/index.js last_sha1=b284dac3e6df6e228332f23c7f601dd226712d39
2025-08-10T18:08:03.414313Z - Checkpoint: processed=37400 nuggets=85641 last_path=frontend/node_modules/@mui/material/node/ListItemIcon/listItemIconClasses.js last_sha1=2d3ca623f5e85115a009428e7f21f0440c49f6dc
2025-08-10T18:08:04.207111Z - Checkpoint: processed=37500 nuggets=85656 last_path=frontend/node_modules/@mui/material/modern/TableSortLabel/tableSortLabelClasses.js last_sha1=18877c299d92fa16e6e9d2ec8fd5397b3de26289
2025-08-10T18:08:04.565701Z - Checkpoint: processed=37600 nuggets=85675 last_path=frontend/node_modules/@mui/material/modern/StyledEngineProvider/index.js last_sha1=577bd8a70869edfd93ee156b949515dde944d09f
2025-08-10T18:08:04.979803Z - Checkpoint: processed=37700 nuggets=85687 last_path=frontend/node_modules/@mui/material/node/ButtonBase/TouchRipple.js last_sha1=a23fa7804113118d87087959ecc0434f94ffd025
2025-08-10T18:08:05.475715Z - Checkpoint: processed=37800 nuggets=85694 last_path=frontend/node_modules/@mui/material/node/Badge/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:08:05.823360Z - Checkpoint: processed=37900 nuggets=85722 last_path=frontend/node_modules/@mui/material/CircularProgress/CircularProgress.js last_sha1=e982419bf1c495a62da2fd2c70b26bbb34b2af4e
2025-08-10T18:08:06.280330Z - Checkpoint: processed=38000 nuggets=85791 last_path=frontend/node_modules/@mui/material/ButtonBase/touchRippleClasses.d.ts last_sha1=cfd0a4212a621c63d3c2d3a7ce87b3307d604e3a
2025-08-10T18:08:06.567142Z - Checkpoint: processed=38100 nuggets=85839 last_path=frontend/node_modules/@mui/material/FormGroup/index.d.ts last_sha1=d1726b0f0e938d087199b0a6d688d3c060b7e513
2025-08-10T18:08:06.824646Z - Checkpoint: processed=38200 nuggets=85860 last_path=frontend/node_modules/@mui/material/Container/containerClasses.js last_sha1=6e52eeb7946d496d8a7876531d46ed91f8c0e542
2025-08-10T18:08:06.921306Z - Checkpoint: processed=38300 nuggets=85864 last_path=frontend/node_modules/@mui/icons-material/Woman2Sharp.d.ts last_sha1=****************************************
2025-08-10T18:08:07.005245Z - Checkpoint: processed=38400 nuggets=85864 last_path=frontend/node_modules/@mui/icons-material/WifiTetheringError.d.ts last_sha1=****************************************
2025-08-10T18:08:08.511239Z - Checkpoint: processed=38500 nuggets=86000 last_path=frontend/node_modules/@mui/material/AccordionSummary/accordionSummaryClasses.js last_sha1=7ea6cd72a5d96cb71e2519067c86403e60a93d77
2025-08-10T18:08:08.595846Z - Checkpoint: processed=38600 nuggets=86001 last_path=frontend/node_modules/@mui/material/FormHelperText/index.js last_sha1=8abb1bc0602a332f55003463a77218820f16ac71
2025-08-10T18:08:09.240106Z - Checkpoint: processed=38700 nuggets=86002 last_path=frontend/node_modules/@mui/material/legacy/Hidden/hiddenCssClasses.js last_sha1=a21bd81ef8f74428993b494ba359f060e9509891
2025-08-10T18:08:09.607153Z - Checkpoint: processed=38800 nuggets=86015 last_path=frontend/node_modules/@mui/material/legacy/StyledEngineProvider/index.js last_sha1=577bd8a70869edfd93ee156b949515dde944d09f
2025-08-10T18:08:09.970495Z - Checkpoint: processed=38900 nuggets=86020 last_path=frontend/node_modules/@mui/material/legacy/Select/index.js last_sha1=0e564fdd6cf45f5b9190865f283cdade06765c61
2025-08-10T18:08:10.548634Z - Checkpoint: processed=39000 nuggets=86030 last_path=frontend/node_modules/@mui/material/internal/switchBaseClasses.d.ts last_sha1=5d324441214f01bea0c40eda959be4c7d125531a
2025-08-10T18:08:10.919256Z - Checkpoint: processed=39100 nuggets=86060 last_path=frontend/node_modules/@mui/material/FormLabel/package.json last_sha1=6344c3d5eec57302bada2b854978e75b14b8bbc5
2025-08-10T18:08:11.143137Z - Checkpoint: processed=39200 nuggets=86089 last_path=frontend/node_modules/@mui/material/legacy/CircularProgress/index.js last_sha1=8c423a47df43299a394e622aeecd89220c2b2ced
2025-08-10T18:08:11.572128Z - Checkpoint: processed=39300 nuggets=86101 last_path=frontend/node_modules/@mui/material/legacy/Backdrop/Backdrop.js last_sha1=7b389d7ce92c741a277b088c6ca7f88cc0af0ab7
2025-08-10T18:08:11.855877Z - Checkpoint: processed=39400 nuggets=86112 last_path=frontend/node_modules/@mui/utils/modern/useLazyRef/useLazyRef.js last_sha1=382335bd7d3c34c8a990c87c997b11ddd0a25fe2
2025-08-10T18:08:11.935199Z - Checkpoint: processed=39500 nuggets=86118 last_path=frontend/node_modules/@mui/utils/modern/resolveProps/resolveProps.js last_sha1=0cf66d5218bf300739b5df936cb38ff07b037892
2025-08-10T18:08:12.290695Z - Checkpoint: processed=39600 nuggets=86138 last_path=frontend/node_modules/@mui/x-date-pickers/DateCalendar/PickersFadeTransitionGroup.js last_sha1=1ca5b0ba29ebffd640d021340d3ba3de4dbc3632
2025-08-10T18:08:12.916990Z - Checkpoint: processed=39700 nuggets=86217 last_path=frontend/node_modules/@mui/x-date-pickers/CHANGELOG.md last_sha1=e4fdfe0633b6c940b4dee80a6873e109836b2ff8
2025-08-10T18:08:13.060701Z - Checkpoint: processed=39800 nuggets=86219 last_path=frontend/node_modules/@mui/utils/integerPropType/package.json last_sha1=fd69f34ffa5660fcb3e34ff0ad3d8e66d4542a97
2025-08-10T18:08:13.157631Z - Checkpoint: processed=39900 nuggets=86231 last_path=frontend/node_modules/@mui/utils/esm/setRef/index.js last_sha1=12e56d31fb8defa2fa7873a87728908421ec2e2a
2025-08-10T18:08:13.243783Z - Checkpoint: processed=40000 nuggets=86233 last_path=frontend/node_modules/@mui/utils/legacy/useSlotProps/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:08:13.332152Z - Checkpoint: processed=40100 nuggets=86240 last_path=frontend/node_modules/@mui/utils/legacy/formatMuiErrorMessage/index.js last_sha1=4cd974515f7d8a582941e48a94d3802f6d73fe2c
2025-08-10T18:08:13.769218Z - Checkpoint: processed=40200 nuggets=86246 last_path=frontend/node_modules/@mui/x-date-pickers/legacy/MultiSectionDigitalClock/index.js last_sha1=0db93cd2556bdc8b4448c08edb11c6f0612818f0
2025-08-10T18:08:14.153150Z - Checkpoint: processed=40300 nuggets=86264 last_path=frontend/node_modules/@mui/x-date-pickers/legacy/internals/hooks/usePicker/usePickerViews.js last_sha1=d946ea7ad93b7ac69a4fb3801574837a75420590
2025-08-10T18:08:14.645839Z - Checkpoint: processed=40400 nuggets=86322 last_path=frontend/node_modules/@mui/x-date-pickers/legacy/TimeField/TimeField.js last_sha1=7d074a9c93670bd547eb3bae2f56e61e5749d640
2025-08-10T18:08:15.124351Z - Checkpoint: processed=40500 nuggets=86348 last_path=frontend/node_modules/@mui/x-date-pickers/internals/components/pickersToolbarClasses.js last_sha1=e0a9e14190b9d0514163813791d9153b767e0c10
2025-08-10T18:08:15.437552Z - Checkpoint: processed=40600 nuggets=86421 last_path=frontend/node_modules/@mui/x-date-pickers/dateTimeViewRenderers/index.d.ts last_sha1=97b7b2511674cb1d4cbcba7c48a1261469330324
2025-08-10T18:08:15.872148Z - Checkpoint: processed=40700 nuggets=86491 last_path=frontend/node_modules/@mui/x-date-pickers/legacy/DateField/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:08:16.580893Z - Checkpoint: processed=40800 nuggets=86561 last_path=frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldState.js last_sha1=2e770f5bac4775bc4d342ddd00797cb8899553e9
2025-08-10T18:08:16.941943Z - Checkpoint: processed=40900 nuggets=86668 last_path=frontend/node_modules/@mui/material/styles/identifier.d.ts last_sha1=a658fcb41d1760b3d000446b3891697c41ebb519
2025-08-10T18:08:17.272198Z - Checkpoint: processed=41000 nuggets=86732 last_path=frontend/node_modules/@mui/material/Stack/package.json last_sha1=df06a9efefbabd77d1b4885a85d82308aa20cef3
2025-08-10T18:08:17.690106Z - Checkpoint: processed=41100 nuggets=86836 last_path=frontend/node_modules/@mui/material/Toolbar/toolbarClasses.d.ts last_sha1=78d7fbe6cc9bed982abc5f27e1b78fe2b2290fc5
2025-08-10T18:08:21.747316Z - Checkpoint: processed=41200 nuggets=87114 last_path=frontend/node_modules/@mui/material/TableFooter/TableFooter.d.ts last_sha1=c9773dbd8b7b73de5026a35f97e9d7195f6de095
2025-08-10T18:08:22.217453Z - Checkpoint: processed=41300 nuggets=87170 last_path=frontend/node_modules/@mui/material/node/Zoom/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:08:22.636723Z - Checkpoint: processed=41400 nuggets=87175 last_path=frontend/node_modules/@mui/material/node/Tabs/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:08:23.133864Z - Checkpoint: processed=41500 nuggets=87208 last_path=frontend/node_modules/@mui/material/SpeedDialIcon/speedDialIconClasses.js last_sha1=9c23244c72cdb29176366f9e7b898f60f02ef261
2025-08-10T18:08:23.573311Z - Checkpoint: processed=41600 nuggets=87259 last_path=frontend/node_modules/@mui/material/RadioGroup/RadioGroupContext.d.ts last_sha1=01189f8e2b86a6f0fc0b26708c15a630bd45e135
2025-08-10T18:08:23.885601Z - Checkpoint: processed=41700 nuggets=87303 last_path=frontend/node_modules/@mui/system/modern/InitColorSchemeScript/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:08:24.247677Z - Checkpoint: processed=41800 nuggets=87350 last_path=frontend/node_modules/@mui/system/legacy/DefaultPropsProvider/DefaultPropsProvider.js last_sha1=4fec84c74eaa6392e4b3b039f9cc85a7944abacd
2025-08-10T18:08:24.387807Z - Checkpoint: processed=41900 nuggets=87360 last_path=frontend/node_modules/@mui/utils/esm/chainPropTypes/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:08:25.924053Z - Checkpoint: processed=42000 nuggets=87488 last_path=frontend/node_modules/@mui/types/desktop.ini last_sha1=bde2dc12dad2f72bd0f4f7014e57628cea6b05b3
2025-08-10T18:08:28.686056Z - Checkpoint: processed=42100 nuggets=87701 last_path=frontend/node_modules/@mui/private-theming/modern/ThemeProvider/ThemeProvider.js last_sha1=e74a57d54ba01cf56490c9e270d7ecff49e7688d
2025-08-10T18:08:29.918305Z - Checkpoint: processed=42200 nuggets=87809 last_path=frontend/node_modules/@mui/system/esm/cssVars/createGetCssVar.js last_sha1=bac67bcce505ca972d83d850a9c18d579932e6e4
2025-08-10T18:08:30.225032Z - Checkpoint: processed=42300 nuggets=87833 last_path=frontend/node_modules/@mui/system/Box/index.js last_sha1=35032f58121c2bedd6636902a018014d609369ef
2025-08-10T18:08:30.565118Z - Checkpoint: processed=42400 nuggets=87890 last_path=frontend/node_modules/@mui/icons-material/SixteenMpTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:08:30.651046Z - Checkpoint: processed=42500 nuggets=87890 last_path=frontend/node_modules/@mui/icons-material/SignLanguageRounded.js last_sha1=37828a10b46d7cca15663634a500518a57825028
2025-08-10T18:08:30.737685Z - Checkpoint: processed=42600 nuggets=87890 last_path=frontend/node_modules/@mui/icons-material/SmokingRoomsTwoTone.js last_sha1=f353ddf09fbb60c3a574518089a8598d6ad0226e
2025-08-10T18:08:30.820687Z - Checkpoint: processed=42700 nuggets=87890 last_path=frontend/node_modules/@mui/icons-material/SkipPreviousSharp.d.ts last_sha1=****************************************
2025-08-10T18:08:30.909050Z - Checkpoint: processed=42800 nuggets=87890 last_path=frontend/node_modules/@mui/icons-material/SignalCellular2BarOutlined.d.ts last_sha1=****************************************
2025-08-10T18:08:30.990913Z - Checkpoint: processed=42900 nuggets=87890 last_path=frontend/node_modules/@mui/icons-material/ShortcutOutlined.d.ts last_sha1=****************************************
2025-08-10T18:08:31.094607Z - Checkpoint: processed=43000 nuggets=87891 last_path=frontend/node_modules/@mui/icons-material/SignalWifiConnectedNoInternet4.d.ts last_sha1=****************************************
2025-08-10T18:08:31.176507Z - Checkpoint: processed=43100 nuggets=87891 last_path=frontend/node_modules/@mui/icons-material/SignalWifi0Bar.d.ts last_sha1=****************************************
2025-08-10T18:08:31.261921Z - Checkpoint: processed=43200 nuggets=87891 last_path=frontend/node_modules/@mui/icons-material/StarHalfTwoTone.js last_sha1=8725c589c8a6e6427ff0dad224ad8c6f4629f888
2025-08-10T18:08:31.340618Z - Checkpoint: processed=43300 nuggets=87891 last_path=frontend/node_modules/@mui/icons-material/SquareSharp.d.ts last_sha1=****************************************
2025-08-10T18:08:31.422574Z - Checkpoint: processed=43400 nuggets=87891 last_path=frontend/node_modules/@mui/icons-material/StraightenOutlined.d.ts last_sha1=****************************************
2025-08-10T18:08:31.503881Z - Checkpoint: processed=43500 nuggets=87891 last_path=frontend/node_modules/@mui/icons-material/StayCurrentPortrait.d.ts last_sha1=****************************************
2025-08-10T18:08:31.580942Z - Checkpoint: processed=43600 nuggets=87891 last_path=frontend/node_modules/@mui/icons-material/SpatialAudioRounded.d.ts last_sha1=****************************************
2025-08-10T18:08:31.658245Z - Checkpoint: processed=43700 nuggets=87891 last_path=frontend/node_modules/@mui/icons-material/SouthAmerica.js last_sha1=b8e00f21f0b3fa617930d53c5a12056c456bf6d0
2025-08-10T18:08:31.747069Z - Checkpoint: processed=43800 nuggets=87891 last_path=frontend/node_modules/@mui/icons-material/SportsHandballTwoTone.js last_sha1=938595d4f9a1d57d7ab69003ebb422540608cbac
2025-08-10T18:08:31.827022Z - Checkpoint: processed=43900 nuggets=87892 last_path=frontend/node_modules/@mui/icons-material/RuleFolderSharp.d.ts last_sha1=****************************************
2025-08-10T18:08:31.918100Z - Checkpoint: processed=44000 nuggets=87901 last_path=frontend/node_modules/@mui/icons-material/RotateRightTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:08:31.994249Z - Checkpoint: processed=44100 nuggets=87901 last_path=frontend/node_modules/@mui/icons-material/Schema.d.ts last_sha1=****************************************
2025-08-10T18:08:32.078644Z - Checkpoint: processed=44200 nuggets=87906 last_path=frontend/node_modules/@mui/icons-material/SaveAlt.js last_sha1=49c0fceae380e09f8876fc5eb528f97392da78d2
2025-08-10T18:08:32.157862Z - Checkpoint: processed=44300 nuggets=87906 last_path=frontend/node_modules/@mui/icons-material/ReportOff.js last_sha1=8e9453e55c4b9613f25e7222080e7983533c87b4
2025-08-10T18:08:32.237013Z - Checkpoint: processed=44400 nuggets=87906 last_path=frontend/node_modules/@mui/icons-material/RepeatOneOnTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:08:32.323018Z - Checkpoint: processed=44500 nuggets=87908 last_path=frontend/node_modules/@mui/icons-material/RocketOutlined.d.ts last_sha1=****************************************
2025-08-10T18:08:32.400950Z - Checkpoint: processed=44600 nuggets=87908 last_path=frontend/node_modules/@mui/icons-material/RestoreFromTrashRounded.d.ts last_sha1=****************************************
2025-08-10T18:08:32.487889Z - Checkpoint: processed=44700 nuggets=87908 last_path=frontend/node_modules/@mui/icons-material/SettingsInputComponentSharp.js last_sha1=cbe173f5cd0f208e23bcc607c86c5bce4ae8c7c3
2025-08-10T18:08:32.566412Z - Checkpoint: processed=44800 nuggets=87908 last_path=frontend/node_modules/@mui/icons-material/SettingsCellSharp.js last_sha1=2db55b0d3b663faa1182d0536cbc90a88043ff8a
2025-08-10T18:08:32.646544Z - Checkpoint: processed=44900 nuggets=87908 last_path=frontend/node_modules/@mui/icons-material/ShoppingCartCheckout.js last_sha1=a203c20b8fd19d2f8ca6a52ab56f279bc382f54d
2025-08-10T18:08:32.730150Z - Checkpoint: processed=45000 nuggets=87908 last_path=frontend/node_modules/@mui/icons-material/ShapeLineTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:08:32.810988Z - Checkpoint: processed=45100 nuggets=87908 last_path=frontend/node_modules/@mui/icons-material/SecurityUpdateGoodTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:08:32.885704Z - Checkpoint: processed=45200 nuggets=87908 last_path=frontend/node_modules/@mui/icons-material/ScreenSearchDesktopSharp.d.ts last_sha1=****************************************
2025-08-10T18:08:32.980955Z - Checkpoint: processed=45300 nuggets=87908 last_path=frontend/node_modules/@mui/icons-material/SentimentDissatisfiedRounded.d.ts last_sha1=****************************************
2025-08-10T18:08:33.065473Z - Checkpoint: processed=45400 nuggets=87908 last_path=frontend/node_modules/@mui/icons-material/SendAndArchiveSharp.js last_sha1=005ae7772f41ee4454bb1d68e6811bca6c43bc00
2025-08-10T18:08:33.145504Z - Checkpoint: processed=45500 nuggets=87908 last_path=frontend/node_modules/@mui/icons-material/VerticalAlignBottomTwoTone.js last_sha1=8743f4339c12e790b7345ff0cf4b756488595b2c
2025-08-10T18:08:33.259362Z - Checkpoint: processed=45600 nuggets=87908 last_path=frontend/node_modules/@mui/icons-material/UsbOff.d.ts last_sha1=****************************************
2025-08-10T18:08:33.339862Z - Checkpoint: processed=45700 nuggets=87908 last_path=frontend/node_modules/@mui/icons-material/VideoCallOutlined.d.ts last_sha1=****************************************
2025-08-10T18:08:33.417968Z - Checkpoint: processed=45800 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/TurnSharpLeftSharp.d.ts last_sha1=****************************************
2025-08-10T18:08:33.494453Z - Checkpoint: processed=45900 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/Tty.d.ts last_sha1=****************************************
2025-08-10T18:08:33.570132Z - Checkpoint: processed=46000 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/UnfoldLessDoubleRounded.d.ts last_sha1=****************************************
2025-08-10T18:08:33.652171Z - Checkpoint: processed=46100 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/TwentyOneMp.js last_sha1=8791252d8e5b9081272e7b750e2abbafd8e92b50
2025-08-10T18:08:33.740234Z - Checkpoint: processed=46200 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/WavesOutlined.d.ts last_sha1=****************************************
2025-08-10T18:08:33.880465Z - Checkpoint: processed=46300 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/WashSharp.d.ts last_sha1=****************************************
2025-08-10T18:08:33.958005Z - Checkpoint: processed=46400 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/WidgetsOutlined.d.ts last_sha1=****************************************
2025-08-10T18:08:34.037908Z - Checkpoint: processed=46500 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/WhatshotOutlined.d.ts last_sha1=****************************************
2025-08-10T18:08:34.113643Z - Checkpoint: processed=46600 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/ViewWeekSharp.d.ts last_sha1=****************************************
2025-08-10T18:08:34.188171Z - Checkpoint: processed=46700 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/ViewInAr.js last_sha1=64e77e3e471b48c499a06924689b6db6ee1790c2
2025-08-10T18:08:34.267240Z - Checkpoint: processed=46800 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/Warning.js last_sha1=0d5167dffc55d23fc32cf38918c334b952126586
2025-08-10T18:08:34.348902Z - Checkpoint: processed=46900 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/VolumeOffRounded.d.ts last_sha1=****************************************
2025-08-10T18:08:34.425128Z - Checkpoint: processed=47000 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/SystemUpdateAlt.d.ts last_sha1=****************************************
2025-08-10T18:08:34.560072Z - Checkpoint: processed=47100 nuggets=87913 last_path=frontend/node_modules/@mui/icons-material/SwitchRightTwoTone.js last_sha1=1b9dce0b15f260869b46d3d9cdea2e645973b839
2025-08-10T18:08:34.640636Z - Checkpoint: processed=47200 nuggets=87914 last_path=frontend/node_modules/@mui/icons-material/Telegram.js last_sha1=cef3ba7ee53dfb21b593e89bc9ee5f704e1c2e3b
2025-08-10T18:08:34.714032Z - Checkpoint: processed=47300 nuggets=87914 last_path=frontend/node_modules/@mui/icons-material/TableViewSharp.d.ts last_sha1=****************************************
2025-08-10T18:08:34.797922Z - Checkpoint: processed=47400 nuggets=87914 last_path=frontend/node_modules/@mui/icons-material/StrikethroughSOutlined.d.ts last_sha1=****************************************
2025-08-10T18:08:34.880561Z - Checkpoint: processed=47500 nuggets=87914 last_path=frontend/node_modules/@mui/icons-material/SwipeLeftTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:08:34.960070Z - Checkpoint: processed=47600 nuggets=87914 last_path=frontend/node_modules/@mui/icons-material/SwapHorizontalCircleOutlined.d.ts last_sha1=****************************************
2025-08-10T18:08:35.037203Z - Checkpoint: processed=47700 nuggets=87914 last_path=frontend/node_modules/@mui/icons-material/Today.js last_sha1=7d408f88777d5efebdaca55c344673a9092b560b
2025-08-10T18:08:35.125507Z - Checkpoint: processed=47800 nuggets=87914 last_path=frontend/node_modules/@mui/icons-material/Timer3Outlined.d.ts last_sha1=****************************************
2025-08-10T18:08:35.215236Z - Checkpoint: processed=47900 nuggets=87914 last_path=frontend/node_modules/@mui/icons-material/TranscribeTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:08:35.311096Z - Checkpoint: processed=48000 nuggets=87914 last_path=frontend/node_modules/@mui/icons-material/TonalityTwoTone.d.ts last_sha1=****************************************
2025-08-10T18:08:35.398614Z - Checkpoint: processed=48100 nuggets=87914 last_path=frontend/node_modules/@mui/icons-material/TextRotationNoneRounded.d.ts last_sha1=****************************************
2025-08-10T18:08:35.475441Z - Checkpoint: processed=48200 nuggets=87914 last_path=frontend/node_modules/@mui/icons-material/Terrain.js last_sha1=a0e0e9f6967c82a75a7660118226fbb16bf3eedb
2025-08-10T18:08:35.552622Z - Checkpoint: processed=48300 nuggets=87914 last_path=frontend/node_modules/@mui/icons-material/ThunderstormOutlined.d.ts last_sha1=****************************************
2025-08-10T18:08:35.632073Z - Checkpoint: processed=48400 nuggets=87914 last_path=frontend/node_modules/@mui/icons-material/ThreeKTwoTone.js last_sha1=76ce6e1a6f1dfea541e1ca593e14ed790c0f1117
2025-08-10T18:08:35.662180Z - File processing completed: processed=48438 nuggets=87914
2025-08-10T18:08:36.466677Z - COMPLETED coverage=48438 manifest=48438
# Phase 1D Content Scan Summary

**Completion Status:** COMPLETED
**Files Processed:** 48438 / 48438
**Nuggets Extracted:** 87914
**Zero Hits Identified:** 385
**Detector Expansion:** 46 unique patterns

## Output Files Generated:
- deep_nuggets.jsonl (87914 entries)
- deep_nuggets_report.md (human-readable)
- full_scan_coverage.json (48438 entries)
- zero_hit_report.md (385 entries)
- detector_expansion.md (46 patterns)

## Validation Results:
**HARD GATES PASSED:** Coverage (48438) = Manifest (48438)

**Phase 1D content scanning completed successfully.**

