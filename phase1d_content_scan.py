# phase1d_content_scan.py
# Full-file streaming scanner with adaptive audit and resume-safe operation.
# Root expected: C:\Users\<USER>\Documents\SalvageControlSystem

import os, sys, json, csv, time, re, traceback, subprocess
from pathlib import Path

# ---------- Paths / Constants ----------
ROOT = Path(__file__).resolve().parent
INPUT_MANIFEST = ROOT / "reports_local" / "phase1d_manifest.cleaned.csv"
STATE_PATH      = ROOT / "state.json"
COVERAGE_PATH   = ROOT / "full_scan_coverage.json"    # JSONL
PROGRESS_LOG    = ROOT / "full_scan_progress.log"
NUGGETS_PATH    = ROOT / "nuggets.jsonl"              # JSONL
NUGGETS_REPORT  = ROOT / "deep_nuggets_report.md"
ZERO_HIT_REPORT = ROOT / "zero_hit_report.md"
RUNTIME_METRICS = ROOT / "reports_local" / "phase1d_runtime_metrics.md"
STREAM_AUDIT_LOG= ROOT / "reports_local" / "stream_audit.log"

IO_CHUNK_BYTES  = 1_048_576  # 1 MB
BASE_TIMEOUT    = 120
PER_MB_TIMEOUT  = 1.5
MAX_TIMEOUT     = 3600

INCLUDE_EXTS = {".txt",".md",".csv",".json",".yml",".yaml",".py",".js",".ts",".tsx",".html"}
EXCLUDE_DIRS = {"node_modules","build","dist",".git",".cache"}
EXCLUDE_GLOBS = {".node",".pack",".core",".lib",".zip",".rar",".7z",".tar",".tar.gz"}

# Adaptive audit thresholds (env-overridable)
AUDIT_MAX_SECONDS = int(os.getenv("AUDIT_MAX_SECONDS", "600"))  # default 10 min
AUDIT_MIN_LINES   = int(os.getenv("AUDIT_MIN_LINES", "20"))     # >=20 chunk logs
AUDIT_MIN_FILES   = int(os.getenv("AUDIT_MIN_FILES", "2"))      # across >=2 files
STREAM_AUDIT      = os.getenv("STREAM_AUDIT", "0") in ("1","true","True")
STREAM_AUDIT_LIMIT= int(os.getenv("STREAM_AUDIT_LIMIT", "5"))   # first N chunks per file

# ---------- Optional dependency ----------
try:
    import ijson  # incremental JSON parser
except Exception:
    ijson = None

# ---------- Streaming Audit (inline; no separate file needed) ----------
def _audit_write(line: str):
    try:
        STREAM_AUDIT_LOG.parent.mkdir(parents=True, exist_ok=True)
        with open(STREAM_AUDIT_LOG, "a", encoding="utf-8") as f:
            f.write(line + "\n")
    except Exception:
        pass

class StreamAudit:
    def __init__(self, file_path, file_size, chunk_bytes, limit=STREAM_AUDIT_LIMIT, logger=print):
        self.path = file_path
        self.size = file_size
        self.chunk_bytes = chunk_bytes
        self.limit = limit
        self._count = 0
        self._logger = logger
        self._t0 = time.time()
        self.enabled = STREAM_AUDIT

    def log(self, offset, chunk_len, hits_in_chunk=0, note=""):
        if not self.enabled or self._count >= self.limit:
            return
        self._count += 1
        line = (f"[STREAM-AUDIT] file='{self.path}' size={self.size} "
                f"chunk_bytes={self.chunk_bytes} offset={offset} len={chunk_len} "
                f"hits_in_chunk={hits_in_chunk} note='{note}' t+={time.time()-self._t0:.2f}s")
        self._logger(line)
        _audit_write(line)

# ---------- Detectors (broad + precise; replace/augment with your production rules) ----------
BROAD = [
    "PAPPG","2 CFR 200","Uniform Guidance","Stafford Act","DRRA","1235b",
    "Public Assistance","Individual Assistance","Category A","Category B",
    "Category C","Category D","Category E","Category F","Category G",
    "PA Category","Section 406","Section 428","Hazard Mitigation",
    "50% rule","improved project","alternate project","force account",
    "mutual aid","procurement","noncompetitive","cost reasonableness",
    "debris removal","emergency protective measures","permanent work",
    "small project","large project","damage inventory","Grants Portal"
]
BROAD_LC = [s.lower() for s in BROAD]
RX = [
    re.compile(r"\b2\s*CFR\s*200(\.\d+)?\b", re.I),
    re.compile(r"\b(PA|Public Assistance)\s+Category\s+[A-G]\b", re.I),
    re.compile(r"\bSection\s+40[68]\b", re.I),
    re.compile(r"\bDRRA\s*(Sec(t|tion)?\s*)?1235b\b", re.I),
    re.compile(r"\bStafford\s+Act\b", re.I),
]

def run_detectors_on_text(text: str):
    tl = text.lower()
    broad_hits = sum(1 for k in BROAD_LC if k in tl)
    precise_hits = sum(1 for r in RX if r.search(text))
    score = broad_hits + 5 * precise_hits
    hits = []
    if score >= 5:
        hits.append({"score": score, "broad": broad_hits, "precise": precise_hits})
    return hits

# ---------- Utilities ----------
def append_json_line(path: Path, obj: dict):
    with open(path, "a", encoding="utf-8") as f:
        f.write(json.dumps(obj) + "\n")

def log_progress(msg: str):
    with open(PROGRESS_LOG, "a", encoding="utf-8") as f:
        f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {msg}\n")

def load_state():
    if STATE_PATH.exists():
        try:
            return json.loads(STATE_PATH.read_text(encoding="utf-8"))
        except Exception:
            pass
    return {"index": 0}

def save_state(state):
    STATE_PATH.write_text(json.dumps(state), encoding="utf-8")

def is_excluded(path: Path) -> bool:
    # directory exclusion by name
    for parent in path.parents:
        if parent.name in EXCLUDE_DIRS:
            return True
    # extension/filename globs
    for g in EXCLUDE_GLOBS:
        if path.name.endswith(g):
            return True
    return False

def adaptive_timeout(size_bytes: int) -> float:
    size_mb = max(1.0, size_bytes / (1024 * 1024))
    return min(BASE_TIMEOUT + PER_MB_TIMEOUT * size_mb, MAX_TIMEOUT)

# ---------- Streaming readers ----------
def scan_text_like_file(path: Path):
    size = path.stat().st_size
    audit = StreamAudit(str(path), size, IO_CHUNK_BYTES)
    bytes_read = 0
    total_hits = 0
    t0 = time.time()

    with open(path, "rb") as f:
        offset = 0
        while True:
            buf = f.read(IO_CHUNK_BYTES)
            if not buf:
                break
            chunk = buf.decode("utf-8", errors="ignore")
            hits = run_detectors_on_text(chunk)
            h = len(hits) if hasattr(hits, "__len__") else int(hits)
            total_hits += h
            audit.log(offset, len(buf), hits_in_chunk=h)
            offset += len(buf)
            bytes_read += len(buf)

    return {
        "status": "OK",
        "bytes_read": bytes_read,
        "size_bytes": size,
        "duration_sec": time.time() - t0,
        "hits_total": total_hits,
    }

class AuditedReader:
    def __init__(self, f, path: Path, size: int, chunk_bytes: int):
        self.f = f
        self.path = path
        self.size = size
        self.offset = 0
        self.audit = StreamAudit(str(path), size, chunk_bytes)
    def read(self, n=-1):
        data = self.f.read(n)
        if data:
            self.audit.log(self.offset, len(data), note="ijson-read")
            self.offset += len(data)
        return data
    def __getattr__(self, name):
        return getattr(self.f, name)

def scan_json_file(path: Path):
    size = path.stat().st_size
    t0 = time.time()
    total_hits = 0
    bytes_read = 0
    with open(path, "rb") as raw:
        if ijson:
            audited = AuditedReader(raw, path, size, IO_CHUNK_BYTES)
            for prefix, event, value in ijson.parse(audited):
                if event in ("string","map_key"):
                    total_hits += len(run_detectors_on_text(str(value)))
            bytes_read = audited.offset
        else:
            audited = AuditedReader(raw, path, size, IO_CHUNK_BYTES)
            while True:
                buf = audited.read(IO_CHUNK_BYTES)
                if not buf:
                    break
                total_hits += len(run_detectors_on_text(buf.decode("utf-8", errors="ignore")))
            bytes_read = audited.offset
    return {
        "status": "OK",
        "bytes_read": bytes_read,
        "size_bytes": size,
        "duration_sec": time.time() - t0,
        "hits_total": total_hits,
    }

# ---------- Main scan pass (single run; used by controller) ----------
def main_scan():
    if not INPUT_MANIFEST.exists():
        print(f"ERROR: Missing manifest at {INPUT_MANIFEST}")
        sys.exit(2)
    # load manifest (expect a CSV with a 'path' or 'relative_path' column and optionally size/bytes)
    rows = []
    with open(INPUT_MANIFEST, newline="", encoding="utf-8") as f:
        r = csv.DictReader(f)
        for row in r:
            rows.append(row)
    # sort largest-first if size column exists
    def _size_of(row):
        v = row.get("bytes") or row.get("size_bytes") or "0"
        try:
            return int(v)
        except Exception:
            return 0
    rows.sort(key=_size_of, reverse=True)

    state = load_state()
    start = state.get("index", 0)
    total = len(rows)
    processed_since_heartbeat = 0

    for i in range(start, total):
        rel = rows[i].get("path") or rows[i].get("relative_path")
        if not rel:
            # skip malformed row
            state["index"] = i + 1
            save_state(state)
            continue
        abspath = (ROOT / rel).resolve()
        if not abspath.exists() or is_excluded(abspath):
            coverage = {"file": rel, "status": "SKIPPED", "reason": "missing_or_excluded"}
            append_json_line(COVERAGE_PATH, coverage)
            state["index"] = i + 1
            save_state(state)
            continue

        size = abspath.stat().st_size
        file_timeout = adaptive_timeout(size)
        t0 = time.time()
        try:
            if abspath.suffix.lower() == ".json":
                result = scan_json_file(abspath)
            else:
                result = scan_text_like_file(abspath)
            status = result.get("status","OK")
        except Exception as e:
            status = "ERROR"
            result = {
                "status": status, "bytes_read": 0, "size_bytes": size,
                "duration_sec": time.time() - t0,
                "error": f"{e.__class__.__name__}: {e}",
                "trace": traceback.format_exc(limit=2),
            }

        # Late timeout flag (coarse-grained)
        if (time.time() - t0) > file_timeout and status == "OK":
            status = "TIMEOUT"

        coverage = {
            "file": rel,
            "status": status,
            "size_bytes": size,
            "bytes_read": result.get("bytes_read", 0),
            "duration_sec": result.get("duration_sec", 0.0),
            "hits_total": result.get("hits_total", 0),
        }
        append_json_line(COVERAGE_PATH, coverage)

        # Simple nuggets plumbing
        if result.get("hits_total", 0) > 0:
            append_json_line(NUGGETS_PATH, {
                "file": rel,
                "hits_total": result["hits_total"],
                "ts": time.time(),
            })

        processed_since_heartbeat += 1
        if processed_since_heartbeat >= 10:
            log_progress(f"processed={i+1}/{total} last='{rel}'")
            processed_since_heartbeat = 0

        state["index"] = i + 1
        save_state(state)

    # End-of-run summary note
    RUNTIME_METRICS.parent.mkdir(parents=True, exist_ok=True)
    with open(RUNTIME_METRICS, "a", encoding="utf-8") as f:
        f.write(f"Completed up to index {state['index']} of {total} at {time.strftime('%Y-%m-%d %H:%M:%S')}\n")

# ---------- Audit helpers ----------
def audit_has_enough_evidence():
    if not STREAM_AUDIT_LOG.exists():
        return False
    files = set()
    lines = 0
    with open(STREAM_AUDIT_LOG, "r", encoding="utf-8") as f:
        for line in f:
            if "[STREAM-AUDIT]" in line:
                lines += 1
                try:
                    s = line.split("file='", 1)[1]
                    file_path = s.split("'", 1)[0]
                    files.add(file_path)
                except Exception:
                    pass
    return (lines >= AUDIT_MIN_LINES) and (len(files) >= AUDIT_MIN_FILES)

def coverage_count():
    if not COVERAGE_PATH.exists():
        return 0
    return sum(1 for _ in open(COVERAGE_PATH, "r", encoding="utf-8"))

# ---------- Entry points ----------
def run_scan_mode_only():
    # Used by controller to run a pure scan pass (honors STREAM_AUDIT)
    main_scan()

def controller():
    # Preflight
    if not INPUT_MANIFEST.exists():
        print(f"ERROR: Manifest not found: {INPUT_MANIFEST}")
        sys.exit(2)
    if not STATE_PATH.exists():
        STATE_PATH.write_text(json.dumps({"index": 204}), encoding="utf-8")

    # Step 1: Adaptive audit pass
    print("\n=== Starting STREAM_AUDIT run (adaptive) ===")
    # Clear prior audit log
    try:
        STREAM_AUDIT_LOG.unlink(missing_ok=True)
    except Exception:
        pass

    start_cov = coverage_count()
    env = os.environ.copy()
    env["STREAM_AUDIT"] = "1"

    # Launch a short-lived scan subprocess in audit mode
    audit_proc = subprocess.Popen([sys.executable, __file__, "--mode", "scan"], env=env)
    t0 = time.time()
    enough = False
    cov_done = False
    timed_out = False

    while True:
        time.sleep(5)
        enough = audit_has_enough_evidence()
        cov_done = coverage_count() > start_cov  # at least one file finished
        timed_out = (time.time() - t0) >= AUDIT_MAX_SECONDS
        if enough or cov_done or timed_out:
            try:
                audit_proc.terminate()
            except Exception:
                pass
            break

    try:
        audit_proc.wait(timeout=30)
    except Exception:
        pass
    print(f"=== Audit pass finished. enough={enough} file_finished={cov_done} timed_out={timed_out} ===")

    # Step 2: Full run (no audit)
    env.pop("STREAM_AUDIT", None)
    print("\n=== Resuming full scan without STREAM_AUDIT ===")
    subprocess.call([sys.executable, __file__, "--mode", "scan"], env=env)

if __name__ == "__main__":
    # CLI: --mode scan → single pass (used by controller)
    if len(sys.argv) >= 3 and sys.argv[1] == "--mode" and sys.argv[2] == "scan":
        run_scan_mode_only()
    else:
        controller()
