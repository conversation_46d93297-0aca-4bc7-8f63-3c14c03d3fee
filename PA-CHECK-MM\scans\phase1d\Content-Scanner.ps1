param(
    [string]$SourceDir = "C:\Users\<USER>\Documents\repo analysis 202508\PA-CHECK-MM",
    [string]$OutputDir = "C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d",
    [string]$ManifestPath = "C:\Users\<USER>\Documents\SalvageControlSystem\PA-CHECK-MM\scans\phase1d\full_scan_manifest.csv"
)

# Ensure UTF8 encoding
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Define output files
$DeepNuggetsFile = Join-Path $OutputDir "deep_nuggets.jsonl"
$NuggetsReportFile = Join-Path $OutputDir "deep_nuggets_report.md"
$CoverageFile = Join-Path $OutputDir "full_scan_coverage.json"
$ProgressFile = Join-Path $OutputDir "full_scan_progress.log"
$ZeroHitReportFile = Join-Path $OutputDir "zero_hit_report.md"
$StateFile = Join-Path $OutputDir "state.json"

# Initialize progress logging
function Write-Progress-Log {
    param([string]$Message)
    $timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    $logEntry = "$timestamp - $Message"
    Add-Content -Path $ProgressFile -Value $logEntry -Encoding UTF8
    Write-Host $logEntry
}

Write-Progress-Log "Starting Phase 1D content scan"

# Detector patterns
$FemaPatterns = @("PAPPG", "Public Assistance", "Individual Assistance", "FEMA", "Category", "DRRA", "1235b", "1206", "HMGP", "HMA", "44 CFR", "2 CFR 200", "procurement", "time and materials", "mutual aid", "force account", "small project", "large project", "damage inventory", "site worksheet", "hazard mitigation", "EHP", "Davis-Bacon", "BABA")
$DataPatterns = @("schema", "model", "DDL", "Prisma", "CREATE TABLE", "OpenAPI", "Swagger", "interface", "zod", "pydantic", "rules", "checklist", "mapping", "decision table", "state machine", "evaluation", "parser", "validator")
$AuthPatterns = @("JWT", "JWE", "OAuth", "HMAC", "signature", "roles", "permissions", "rbac", "abac", "rate limit", "retry", "circuit breaker", "secrets", "kms", "vault", "bcrypt", "argon2", "crypto")

# Function to get extended path
function Get-ExtendedPath {
    param([string]$Path)
    if ($Path.Length -gt 260 -and -not $Path.StartsWith("\\?\")) {
        return "\\?\$Path"
    }
    return $Path
}

# Function to redact secrets
function Redact-Secrets {
    param([string]$Text)
    $Text = $Text -replace 'password\s*[:=]\s*\S+', 'password=***REDACTED***'
    $Text = $Text -replace 'secret\s*[:=]\s*\S+', 'secret=***REDACTED***'
    $Text = $Text -replace 'key\s*[:=]\s*\S+', 'key=***REDACTED***'
    $Text = $Text -replace 'token\s*[:=]\s*\S+', 'token=***REDACTED***'
    return $Text
}

# Function to detect nuggets in content
function Find-Nuggets {
    param(
        [string[]]$Lines,
        [string]$FilePath,
        [string]$FileHash
    )
    
    $nuggets = @()
    $detectedPatterns = @()
    
    for ($i = 0; $i -lt $Lines.Count; $i++) {
        $line = $Lines[$i]
        $matchedDetectors = @()
        
        # Check FEMA patterns
        foreach ($pattern in $FemaPatterns) {
            if ($line -match [regex]::Escape($pattern)) {
                $matchedDetectors += "fema:$pattern"
                $detectedPatterns += $pattern
            }
        }
        
        # Check Data patterns
        foreach ($pattern in $DataPatterns) {
            if ($line -match [regex]::Escape($pattern)) {
                $matchedDetectors += "data:$pattern"
                $detectedPatterns += $pattern
            }
        }
        
        # Check Auth patterns
        foreach ($pattern in $AuthPatterns) {
            if ($line -match [regex]::Escape($pattern)) {
                $matchedDetectors += "auth:$pattern"
                $detectedPatterns += $pattern
            }
        }
        
        # If we found matches, extract a nugget
        if ($matchedDetectors.Count -gt 0) {
            $startLine = [Math]::Max(0, $i - 1)
            $endLine = [Math]::Min($Lines.Count - 1, $i + 10)
            
            $snippet = ($Lines[$startLine..$endLine] | ForEach-Object { $_.Trim() }) -join "`n"
            $snippet = $snippet.Substring(0, [Math]::Min(500, $snippet.Length))
            $snippet = Redact-Secrets $snippet
            
            # Determine category
            $category = "Other"
            if ($matchedDetectors -match "fema:") { $category = "FEMA/Compliance" }
            elseif ($matchedDetectors -match "data:") { $category = "Data Models" }
            elseif ($matchedDetectors -match "auth:") { $category = "Authentication/Security" }
            
            # Generate tags (minimum 3)
            $tags = @()
            if ($FilePath -match "frontend|ui|react|vue") { $tags += "frontend" }
            elseif ($FilePath -match "backend|api|server") { $tags += "backend" }
            else { $tags += "core" }
            
            if ($matchedDetectors -match "fema:") { $tags += "fema-pa" }
            if ($matchedDetectors -match "Category") { $tags += "category-mapping" }
            
            $fileExt = [System.IO.Path]::GetExtension($FilePath).ToLower().TrimStart('.')
            if ($fileExt) { $tags += $fileExt }
            
            # Ensure minimum 3 tags
            while ($tags.Count -lt 3) {
                $tags += "general"
            }
            
            $nugget = @{
                path = $FilePath
                sha1 = $FileHash
                category = $category
                tags = $tags
                lines = @($startLine + 1, $endLine + 1)
                snippet = $snippet
                reason = "Contains domain-relevant patterns: $($matchedDetectors -join ', ')"
                detectors = $matchedDetectors
                risk_level = "medium"
                action = "review-policy"
                has_more_in_file = $false
                discovery_mode = "manifest-scan"
                ts_utc = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                sig = [System.Security.Cryptography.SHA1]::Create().ComputeHash([System.Text.Encoding]::UTF8.GetBytes("$FilePath$startLine$endLine$snippet")) | ForEach-Object { $_.ToString("x2") } | Join-String
            }
            
            $nuggets += $nugget
        }
    }
    
    return $nuggets, $detectedPatterns
}

# Function to check zero-hit criteria
function Test-ZeroHitCriteria {
    param(
        [string]$FilePath,
        [string[]]$Lines
    )
    
    # Check if path suggests domain relevance
    $domainRelevantPath = $FilePath -match "wizard|rule|auth|schema|model|config|mapping|policy|pappg|drra|elig|checklist"
    
    # Check if it's a substantial doc with policy terms
    $wordCount = ($Lines -join " ").Split(" ", [StringSplitOptions]::RemoveEmptyEntries).Count
    $policyTermCount = 0
    foreach ($line in $Lines) {
        if ($line -match "FEMA|PAPPG|Category|DRRA|policy|compliance|regulation") {
            $policyTermCount++
        }
    }
    
    return $domainRelevantPath -or ($wordCount -ge 500 -and $policyTermCount -ge 3)
}

# Load manifest
Write-Progress-Log "Loading manifest from $ManifestPath"
$manifestContent = Get-Content $ManifestPath -Encoding UTF8
$manifestRows = @()

for ($i = 1; $i -lt $manifestContent.Count; $i++) {  # Skip header
    $line = $manifestContent[$i]
    if ($line -match '^"([^"]+)",(\d+),(true|false),"([^"]*)"') {
        $manifestRows += @{
            path = $matches[1]
            bytes = [int]$matches[2]
            is_binary = $matches[3] -eq "true"
            sha1 = $matches[4]
            index = $i - 1
        }
    }
}

$totalFiles = $manifestRows.Count
Write-Progress-Log "Loaded manifest: $totalFiles files to process"

# Check for resume state
$startIndex = 0
$processedCount = 0
$totalNuggets = 0

if (Test-Path $StateFile) {
    try {
        $state = Get-Content $StateFile -Raw | ConvertFrom-Json
        $startIndex = $state.manifest_index
        $processedCount = $state.processed_count
        $totalNuggets = $state.total_nuggets
        Write-Progress-Log "Resuming from index $startIndex (processed: $processedCount, nuggets: $totalNuggets)"
    }
    catch {
        Write-Progress-Log "Could not parse state file, starting fresh"
    }
}

# Initialize output files if starting fresh
if ($startIndex -eq 0) {
    if (-not (Test-Path $DeepNuggetsFile)) {
        "" | Out-File -FilePath $DeepNuggetsFile -Encoding UTF8
    }
    if (-not (Test-Path $NuggetsReportFile)) {
        "# Deep Nuggets Report`n`nGenerated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss UTC')`n" | Out-File -FilePath $NuggetsReportFile -Encoding UTF8
    }
    if (-not (Test-Path $ZeroHitReportFile)) {
        "# Zero Hit Report`n`nFiles with 0 nuggets but domain relevance:`n" | Out-File -FilePath $ZeroHitReportFile -Encoding UTF8
    }
}

# Process files
$coverage = @()
$zeroHits = @()
$newDetectors = @()

Write-Progress-Log "Starting file processing from index $startIndex"

for ($i = $startIndex; $i -lt $manifestRows.Count; $i++) {
    $fileInfo = $manifestRows[$i]
    $absolutePath = Join-Path $SourceDir $fileInfo.path
    $extendedPath = Get-ExtendedPath $absolutePath
    
    $coverageEntry = @{
        path = $fileInfo.path
        sha1 = $fileInfo.sha1
        bytes = $fileInfo.bytes
        is_binary = $fileInfo.is_binary
        scanned_lines = 0
        nuggets = 0
        has_more_in_file = $false
        first_seen_utc = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        last_scan_utc = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        error = $null
    }
    
    try {
        if ($fileInfo.is_binary) {
            # Binary file - just record coverage
            $coverageEntry.scanned_lines = 0
            $coverageEntry.nuggets = 0
        }
        else {
            # Text file - read and analyze
            $retryCount = 0
            $maxRetries = 15
            $fileProcessed = $false
            
            while (-not $fileProcessed -and $retryCount -lt $maxRetries) {
                try {
                    $lines = Get-Content $extendedPath -Encoding UTF8 -ErrorAction Stop
                    $coverageEntry.scanned_lines = $lines.Count
                    
                    $nuggets, $detectedPatterns = Find-Nuggets $lines $fileInfo.path $fileInfo.sha1
                    $coverageEntry.nuggets = $nuggets.Count
                    $totalNuggets += $nuggets.Count
                    
                    # Write nuggets to JSONL
                    foreach ($nugget in $nuggets) {
                        $nuggetJson = $nugget | ConvertTo-Json -Compress
                        Add-Content -Path $DeepNuggetsFile -Value $nuggetJson -Encoding UTF8
                        
                        # Add to report
                        $reportEntry = "## $($nugget.path) (Lines $($nugget.lines[0])-$($nugget.lines[1]))`n"
                        $reportEntry += "**Category:** $($nugget.category)`n"
                        $reportEntry += "**Tags:** $($nugget.tags -join ', ')`n"
                        $reportEntry += "**Reason:** $($nugget.reason)`n"
                        $reportEntry += "````n$($nugget.snippet)`n```n`n"
                        Add-Content -Path $NuggetsReportFile -Value $reportEntry -Encoding UTF8
                    }
                    
                    # Check for zero hits
                    if ($nuggets.Count -eq 0 -and (Test-ZeroHitCriteria $fileInfo.path $lines)) {
                        $zeroHitEntry = "- **$($fileInfo.path)** ($($lines.Count) lines) - Detectors tried: all patterns - Hypothesis: Domain-relevant file with no current pattern matches"
                        Add-Content -Path $ZeroHitReportFile -Value $zeroHitEntry -Encoding UTF8
                        $zeroHits += $fileInfo.path
                    }
                    
                    # Track new detector patterns
                    $newDetectors += $detectedPatterns
                    
                    $fileProcessed = $true
                }
                catch {
                    $retryCount++
                    if ($retryCount -lt $maxRetries) {
                        Start-Sleep -Milliseconds (100 * $retryCount)
                    } else {
                        $coverageEntry.error = "locked"
                        $fileProcessed = $true
                    }
                }
            }
        }
    }
    catch {
        $coverageEntry.error = $_.Exception.Message
    }
    
    $coverage += $coverageEntry
    $processedCount++
    
    # Checkpoint every 100 files
    if ($processedCount % 100 -eq 0) {
        $state = @{
            manifest_index = $i + 1
            last_path = $fileInfo.path
            processed_count = $processedCount
            total_nuggets = $totalNuggets
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        }
        $state | ConvertTo-Json | Out-File -FilePath $StateFile -Encoding UTF8
        Write-Progress-Log "Checkpoint: processed=$processedCount nuggets=$totalNuggets last_path=$($fileInfo.path)"
    }
}

Write-Progress-Log "File processing completed: processed=$processedCount nuggets=$totalNuggets"

# Write final coverage file
$coverage | ConvertTo-Json | Out-File -FilePath $CoverageFile -Encoding UTF8

# Final validation
$manifestCount = $totalFiles
$coverageCount = $coverage.Count

if ($coverageCount -ne $manifestCount) {
    Write-Progress-Log "ABORT_MISMATCH manifest=$manifestCount coverage=$coverageCount"
    exit 1
} else {
    Write-Progress-Log "COMPLETED coverage=$coverageCount manifest=$manifestCount"
    
    # Generate summary
    $summary = @"
# Phase 1D Content Scan Summary

**Completion Status:** COMPLETED
**Files Processed:** $processedCount / $manifestCount
**Nuggets Extracted:** $totalNuggets
**Zero Hits Identified:** $($zeroHits.Count)

**Output Files Generated:**
- deep_nuggets.jsonl ($totalNuggets entries)
- deep_nuggets_report.md (human-readable)
- full_scan_coverage.json ($coverageCount entries)
- zero_hit_report.md ($($zeroHits.Count) entries)

**Validation:** Perfect parity achieved (Coverage = Manifest = $coverageCount)
"@
    
    Write-Host $summary
    Add-Content -Path $ProgressFile -Value $summary -Encoding UTF8
    
    # Clean up state file
    if (Test-Path $StateFile) {
        Remove-Item $StateFile -Force
    }
    
    exit 0
}
