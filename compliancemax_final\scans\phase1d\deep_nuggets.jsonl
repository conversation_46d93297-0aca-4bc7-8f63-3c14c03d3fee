{"path": "DEPLOYMENT_GUIDE.md", "sha1": "4ce3dca5e8a8f0352bff697f962ef274cffe78f1", "category": "FEMA/Compliance", "tags": ["fema-compliance", "general", "code-logic"], "lines": [61, 73], "snippet": "4. **Policy Matcher Service**: Specialized service for policy matching functionality\n5. **Integration Services**: Connectors for external systems (FEMA, document repositories, calendars)\n\n## Frontend Deployment\n\n### Building the Frontend\n\n1. Navigate to the frontend directory:\n   ```bash\n   cd /path/to/compliancemax/frontend\n   ```\n", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.546238+00:00", "sig": "7f71ee3ae89d3801f5d31cfa96dcc07d86d9eeac"}
{"path": "DEPLOYMENT_GUIDE.md", "sha1": "4ce3dca5e8a8f0352bff697f962ef274cffe78f1", "category": "FEMA/Compliance", "tags": ["fema-compliance", "general", "code-logic"], "lines": [342, 354], "snippet": "\n### FEMA Database Integration\n\n1. Register for FEMA API access at https://www.fema.gov/api\n2. Add your API key to the .env file:\n   ```\n   FEMA_API_KEY=***REDACTED***\n   FEMA_API_URL=https://www.fema.gov/api/open/v1\n   ```\n\n### Document Management Integration\n", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.550274+00:00", "sig": "fa2504b1e5d0d467f95560e00239fd547f8ecd8c"}
{"path": "DEPLOYMENT_GUIDE.md", "sha1": "4ce3dca5e8a8f0352bff697f962ef274cffe78f1", "category": "FEMA/Compliance", "tags": ["fema-compliance", "general", "code-logic"], "lines": [344, 356], "snippet": "\n1. Register for FEMA API access at https://www.fema.gov/api\n2. Add your API key to the .env file:\n   ```\n   FEMA_API_KEY=***REDACTED***\n   FEMA_API_URL=https://www.fema.gov/api/open/v1\n   ```\n\n### Document Management Integration\n\nConfigure the following variables in the .env file for each document repository:\n", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.550572+00:00", "sig": "c73b580ecea6e2063a8c54c53834be7b30fbb351"}
{"path": "DEPLOYMENT_GUIDE.md", "sha1": "4ce3dca5e8a8f0352bff697f962ef274cffe78f1", "category": "FEMA/Compliance", "tags": ["fema-compliance", "general", "code-logic"], "lines": [348, 360], "snippet": "   FEMA_API_KEY=***REDACTED***\n   FEMA_API_URL=https://www.fema.gov/api/open/v1\n   ```\n\n### Document Management Integration\n\nConfigure the following variables in the .env file for each document repository:\n\n```\n# SharePoint\nSHAREPOINT_CLIENT_ID=your-client-id\nSHAREPOINT_CLIENT_SECRET=***REDACTED***", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.550910+00:00", "sig": "fb1947b34d8cff8b4d1e8432e5fcc79497abc32a"}
{"path": "DEPLOYMENT_GUIDE.md", "sha1": "4ce3dca5e8a8f0352bff697f962ef274cffe78f1", "category": "Config/Data Assets", "tags": ["data-model", "general", "code-logic"], "lines": [43, 55], "snippet": "  - Outbound access for external integrations\n  - Firewall rules for appropriate ports\n\n### Development Environment\n\n- Node.js 16.x or higher\n- Python 3.10 or higher\n- PostgreSQL 14.x or higher\n- Git\n- Docker and Docker Compose (optional but recommended)\n\n## Architecture Overview", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.554185+00:00", "sig": "7e2372f01e030ed3c8bef805b71e1de5fc66cea1"}
{"path": "DEPLOYMENT_GUIDE.md", "sha1": "4ce3dca5e8a8f0352bff697f962ef274cffe78f1", "category": "Authentication/Security", "tags": ["access-control", "auth-security", "general"], "lines": [397, 409], "snippet": "   ```\n   JWT_SECRET=***REDACTED***\n   JWT_ALGORITHM=HS256\n   ACCESS_TOKEN_EXPIRE_MINUTES=30\n   REFRESH_TOKEN_EXPIRE_DAYS=7\n   ```\n\n2. For production, consider integrating with OAuth providers:\n   ```\n   OAUTH_GOOGLE_CLIENT_ID=your-client-id\n   OAUTH_GOOGLE_CLIENT_SECRET=***REDACTED***\n   OAUTH_MICROSOFT_CLIENT_ID=your-client-id", "reason": "Handles authentication or security", "detectors": ["regex:\\bJWT\\b"], "risk_level": "high", "action": "review-security", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.564748+00:00", "sig": "28503b5e054c93522f213329893b1b35d14a7baa"}
{"path": "DEPLOYMENT_GUIDE.md", "sha1": "4ce3dca5e8a8f0352bff697f962ef274cffe78f1", "category": "Authentication/Security", "tags": ["access-control", "auth-security", "general"], "lines": [403, 415], "snippet": "\n2. For production, consider integrating with OAuth providers:\n   ```\n   OAUTH_GOOGLE_CLIENT_ID=your-client-id\n   OAUTH_GOOGLE_CLIENT_SECRET=***REDACTED***\n   OAUTH_MICROSOFT_CLIENT_ID=your-client-id\n   OAUTH_MICROSOFT_CLIENT_SECRET=***REDACTED***\n   ```\n\n### Data Protection\n\n1. Ensure sensitive data is encrypted at rest:", "reason": "Handles authentication or security", "detectors": ["regex:\\bOAuth\\b"], "risk_level": "high", "action": "review-security", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.565097+00:00", "sig": "21463c38f6bb0ae661fc68fa95132310aed5f324"}
{"path": "Documents.tsx", "sha1": "8ac9a5b3973e7d1b23dad3f9298c949ef61ed9e7", "category": "FEMA/Compliance", "tags": ["fema-compliance", "typescript", "general"], "lines": [31, 43], "snippet": "    { id: 3, name: 'Employee Handbook', type: 'Policy', uploadDate: '2025-01-10', status: 'Active', size: '2.8 MB' },\n    { id: 4, name: 'FEMA Compliance Checklist', type: 'Form', uploadDate: '2025-03-22', status: 'Active', size: '0.5 MB' },\n    { id: 5, name: 'Quarterly Compliance Report', type: 'Report', uploadDate: '2025-04-01', status: 'Draft', size: '1.7 MB' },\n  ]);\n  \n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('All');\n  const [openUploadDialog, setOpenUploadDialog] = useState(false);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [documentType, setDocumentType] = useState('Policy');\n  \n  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.593070+00:00", "sig": "a7af39e5da14316aeabc0a8239320773cfb169d6"}
{"path": "Documents.tsx", "sha1": "8ac9a5b3973e7d1b23dad3f9298c949ef61ed9e7", "category": "Wizard/Workflow Logic", "tags": ["typescript", "data-model", "data-structure"], "lines": [17, 29], "snippet": "\ninterface Document {\n  id: number;\n  name: string;\n  type: string;\n  uploadDate: string;\n  status: string;\n  size: string;\n}\n\nconst Documents: React.FC = () => {\n  const [documents, setDocuments] = useState<Document[]>([", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.598276+00:00", "sig": "aff51f4b5b21599eff42d1cb096b11419d952cc6"}
{"path": "Documents.tsx", "sha1": "8ac9a5b3973e7d1b23dad3f9298c949ef61ed9e7", "category": "FEMA/Compliance", "tags": ["typescript", "data-model", "general"], "lines": [31, 43], "snippet": "    { id: 3, name: 'Employee Handbook', type: 'Policy', uploadDate: '2025-01-10', status: 'Active', size: '2.8 MB' },\n    { id: 4, name: 'FEMA Compliance Checklist', type: 'Form', uploadDate: '2025-03-22', status: 'Active', size: '0.5 MB' },\n    { id: 5, name: 'Quarterly Compliance Report', type: 'Report', uploadDate: '2025-04-01', status: 'Draft', size: '1.7 MB' },\n  ]);\n  \n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('All');\n  const [openUploadDialog, setOpenUploadDialog] = useState(false);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [documentType, setDocumentType] = useState('Policy');\n  \n  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {", "reason": "Validation or checking logic", "detectors": ["regex:\\bchecklist\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.599162+00:00", "sig": "a7af39e5da14316aeabc0a8239320773cfb169d6"}
{"path": "EnhancedDashboard.tsx", "sha1": "29ee0c9575ef05a807be0caa780e7401b3db4b80", "category": "Data Models", "tags": ["typescript", "data-model", "data-structure"], "lines": [12, 24], "snippet": "\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nconst TabPanel = (props: TabPanelProps) => {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.625644+00:00", "sig": "a868a97b6816efb34af09f002bbe7b626444a4d3"}
{"path": "EnhancedPolicyMatcher.tsx", "sha1": "8c5a25ee4c81d12fc8128ab586fb9319feec2c19", "category": "Data Models", "tags": ["typescript", "data-model", "data-structure"], "lines": [19, 31], "snippet": "\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nconst TabPanel = (props: TabPanelProps) => {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.663270+00:00", "sig": "a32b782b4913d01ab71127b5c8310b3940bef0bd"}
{"path": "EnhancedPolicyMatcher.tsx", "sha1": "8c5a25ee4c81d12fc8128ab586fb9319feec2c19", "category": "FEMA/Compliance", "tags": ["typescript", "data-model", "data-structure"], "lines": [45, 57], "snippet": "\ninterface MatchResult {\n  id: number;\n  requirement: string;\n  policy: string;\n  section: string;\n  confidence: number;\n}\n\ninterface Job {\n  id: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.663866+00:00", "sig": "229d5142df147e917e49684aa5c168a0a32bab42"}
{"path": "EnhancedPolicyMatcher.tsx", "sha1": "8c5a25ee4c81d12fc8128ab586fb9319feec2c19", "category": "FEMA/Compliance", "tags": ["typescript", "data-model", "data-structure"], "lines": [53, 65], "snippet": "\ninterface Job {\n  id: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  created_at: string;\n  file_name: string;\n  results?: MatchResult[];\n}\n\nconst PolicyMatcher: React.FC = () => {\n  // State for file upload\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.664305+00:00", "sig": "4336c4d17e546586f9fd7d3dd9b7a4efa85881b7"}
{"path": "ExportFunctionality.tsx", "sha1": "6c5f8967662f92adab8c013856c06db618edfa52", "category": "FEMA/Compliance", "tags": ["fema-compliance", "typescript", "data-structure"], "lines": [41, 53], "snippet": "      { id: 3, name: 'Detailed Analysis', description: 'Comprehensive report with all details' },\n      { id: 4, name: 'FEMA Compliance', description: 'Formatted specifically for FEMA requirements' },\n      { id: 5, name: 'Custom Template 1', description: 'User-defined custom template' }\n    ]);\n  }\n};\n\ninterface Report {\n  id: number;\n  name: string;\n  type: string;\n  date: string;", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.688341+00:00", "sig": "876fa342ce2c626701940341c04ee6d450f627a5"}
{"path": "ExportFunctionality.tsx", "sha1": "6c5f8967662f92adab8c013856c06db618edfa52", "category": "FEMA/Compliance", "tags": ["fema-compliance", "typescript", "general"], "lines": [60, 72], "snippet": "    { id: 2, name: 'Policy Coverage Analysis', type: 'Policy', date: '2025-03-15', status: 'Complete' },\n    { id: 3, name: 'FEMA Requirements Gap Analysis', type: 'Gap Analysis', date: '2025-03-22', status: 'Complete' },\n    { id: 4, name: 'Document Completeness Report', type: 'Document', date: '2025-04-05', status: 'Complete' },\n    { id: 5, name: 'Annual Security Assessment', type: 'Security', date: '2025-02-28', status: 'Complete' },\n  ]);\n  \n  // State for export options\n  const [selectedReports, setSelectedReports] = useState<number[]>([]);\n  const [exportFormat, setExportFormat] = useState('pdf');\n  const [exportLoading, setExportLoading] = useState(false);\n  \n  // State for template dialog", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "high", "action": "review-security", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.689080+00:00", "sig": "132b416a87829ae4544caab47da8af24dbcb01c5"}
{"path": "ExportFunctionality.tsx", "sha1": "6c5f8967662f92adab8c013856c06db618edfa52", "category": "Wizard/Workflow Logic", "tags": ["typescript", "data-model", "data-structure"], "lines": [47, 59], "snippet": "\ninterface Report {\n  id: number;\n  name: string;\n  type: string;\n  date: string;\n  status: string;\n}\n\nconst ExportFunctionality: React.FC = () => {\n  // State for reports\n  const [reports, setReports] = useState<Report[]>([", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.698650+00:00", "sig": "2ed685d1181de21e51a8726b6868e7c9524019a5"}
{"path": "ExternalSystemsIntegration.tsx", "sha1": "104dbbe51affa8144ac1bdabe786f168ae4ded41", "category": "FEMA/Compliance", "tags": ["fema-compliance", "fema-pa", "typescript"], "lines": [51, 63], "snippet": "\n// Mock FEMA data\nconst femaDisasterData = [\n  { id: 'DR-4611', name: 'Hurricane Alpha', state: 'Florida', declarationDate: '2025-03-15', type: 'Hurricane', status: 'Active' },\n  { id: 'DR-4612', name: 'Severe Storms and Flooding', state: 'Texas', declarationDate: '2025-02-28', type: 'Flood', status: 'Active' },\n  { id: 'DR-4613', name: 'Wildfire', state: 'California', declarationDate: '2025-01-20', type: 'Fire', status: 'Closed' },\n  { id: 'DR-4614', name: 'Tornado', state: 'Oklahoma', declarationDate: '2025-04-05', type: 'Tornado', status: 'Active' },\n  { id: 'DR-4615', name: 'Winter Storm', state: 'Michigan', declarationDate: '2025-02-10', type: 'Winter Storm', status: 'Closed' },\n];\n\nconst femaAssistancePrograms = [\n  { id: 'PA', name: 'Public Assistance', description: 'Provides assistance to state, local, tribal, and territorial governments for emergency work and repair/replacement of disaster-damaged facilities.' },", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.718058+00:00", "sig": "80563138a06c150b4f2bedfac9eec5a41ebab737"}
{"path": "ExternalSystemsIntegration.tsx", "sha1": "104dbbe51affa8144ac1bdabe786f168ae4ded41", "category": "FEMA/Compliance", "tags": ["fema-compliance", "fema-pa", "typescript"], "lines": [61, 73], "snippet": "const femaAssistancePrograms = [\n  { id: 'PA', name: 'Public Assistance', description: 'Provides assistance to state, local, tribal, and territorial governments for emergency work and repair/replacement of disaster-damaged facilities.' },\n  { id: 'IA', name: 'Individual Assistance', description: 'Provides direct assistance to individuals and households for housing and other needs.' },\n  { id: 'HMGP', name: 'Hazard Mitigation Grant Program', description: 'Provides funding for hazard mitigation measures that reduce the risk of loss of life and property from future disasters.' },\n  { id: 'FMA', name: 'Flood Mitigation Assistance', description: 'Provides funding to reduce or eliminate the risk of repetitive flood damage to buildings insured under the NFIP.' },\n  { id: 'BRIC', name: 'Building Resilient Infrastructure and Communities', description: 'Supports states, local communities, tribes and territories as they undertake hazard mitigation projects.' },\n];\n\n// Mock document management system data\nconst documentRepositories = [\n  { id: 1, name: 'SharePoint', connected: true, lastSync: '2025-04-12 09:30 AM', documentCount: 156 },\n  { id: 2, name: 'Google Drive', connected: true, lastSync: '2025-04-13 10:15 AM', documentCount: 89 },", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bPublic Assistance\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.718967+00:00", "sig": "fc24d0bb1a258016d323170113cf12969577a6ad"}
{"path": "ExternalSystemsIntegration.tsx", "sha1": "104dbbe51affa8144ac1bdabe786f168ae4ded41", "category": "Wizard/Workflow Logic", "tags": ["fema-compliance", "fema-pa", "typescript"], "lines": [62, 74], "snippet": "  { id: 'PA', name: 'Public Assistance', description: 'Provides assistance to state, local, tribal, and territorial governments for emergency work and repair/replacement of disaster-damaged facilities.' },\n  { id: 'IA', name: 'Individual Assistance', description: 'Provides direct assistance to individuals and households for housing and other needs.' },\n  { id: 'HMGP', name: 'Hazard Mitigation Grant Program', description: 'Provides funding for hazard mitigation measures that reduce the risk of loss of life and property from future disasters.' },\n  { id: 'FMA', name: 'Flood Mitigation Assistance', description: 'Provides funding to reduce or eliminate the risk of repetitive flood damage to buildings insured under the NFIP.' },\n  { id: 'BRIC', name: 'Building Resilient Infrastructure and Communities', description: 'Supports states, local communities, tribes and territories as they undertake hazard mitigation projects.' },\n];\n\n// Mock document management system data\nconst documentRepositories = [\n  { id: 1, name: 'SharePoint', connected: true, lastSync: '2025-04-12 09:30 AM', documentCount: 156 },\n  { id: 2, name: 'Google Drive', connected: true, lastSync: '2025-04-13 10:15 AM', documentCount: 89 },\n  { id: 3, name: 'Dropbox', connected: false, lastSync: 'Never', documentCount: 0 },", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bIndividual Assistance\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.719578+00:00", "sig": "7c4a1daedebd141835254dbc99b55a1fb602765d"}
{"path": "ExternalSystemsIntegration.tsx", "sha1": "104dbbe51affa8144ac1bdabe786f168ae4ded41", "category": "Wizard/Workflow Logic", "tags": ["fema-compliance", "typescript", "general"], "lines": [63, 75], "snippet": "  { id: 'IA', name: 'Individual Assistance', description: 'Provides direct assistance to individuals and households for housing and other needs.' },\n  { id: 'HMGP', name: 'Hazard Mitigation Grant Program', description: 'Provides funding for hazard mitigation measures that reduce the risk of loss of life and property from future disasters.' },\n  { id: 'FMA', name: 'Flood Mitigation Assistance', description: 'Provides funding to reduce or eliminate the risk of repetitive flood damage to buildings insured under the NFIP.' },\n  { id: 'BRIC', name: 'Building Resilient Infrastructure and Communities', description: 'Supports states, local communities, tribes and territories as they undertake hazard mitigation projects.' },\n];\n\n// Mock document management system data\nconst documentRepositories = [\n  { id: 1, name: 'SharePoint', connected: true, lastSync: '2025-04-12 09:30 AM', documentCount: 156 },\n  { id: 2, name: 'Google Drive', connected: true, lastSync: '2025-04-13 10:15 AM', documentCount: 89 },\n  { id: 3, name: 'Dropbox', connected: false, lastSync: 'Never', documentCount: 0 },\n  { id: 4, name: 'OneDrive', connected: true, lastSync: '2025-04-10 02:45 PM', documentCount: 42 },", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bHMGP\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.720168+00:00", "sig": "9fe48ba0c883b61e251564644f4d7f2f2ae97f0d"}
{"path": "ExternalSystemsIntegration.tsx", "sha1": "104dbbe51affa8144ac1bdabe786f168ae4ded41", "category": "Wizard/Workflow Logic", "tags": ["fema-compliance", "typescript", "general"], "lines": [63, 75], "snippet": "  { id: 'IA', name: 'Individual Assistance', description: 'Provides direct assistance to individuals and households for housing and other needs.' },\n  { id: 'HMGP', name: 'Hazard Mitigation Grant Program', description: 'Provides funding for hazard mitigation measures that reduce the risk of loss of life and property from future disasters.' },\n  { id: 'FMA', name: 'Flood Mitigation Assistance', description: 'Provides funding to reduce or eliminate the risk of repetitive flood damage to buildings insured under the NFIP.' },\n  { id: 'BRIC', name: 'Building Resilient Infrastructure and Communities', description: 'Supports states, local communities, tribes and territories as they undertake hazard mitigation projects.' },\n];\n\n// Mock document management system data\nconst documentRepositories = [\n  { id: 1, name: 'SharePoint', connected: true, lastSync: '2025-04-12 09:30 AM', documentCount: 156 },\n  { id: 2, name: 'Google Drive', connected: true, lastSync: '2025-04-13 10:15 AM', documentCount: 89 },\n  { id: 3, name: 'Dropbox', connected: false, lastSync: 'Never', documentCount: 0 },\n  { id: 4, name: 'OneDrive', connected: true, lastSync: '2025-04-10 02:45 PM', documentCount: 42 },", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bhazard mitigation\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.720707+00:00", "sig": "9fe48ba0c883b61e251564644f4d7f2f2ae97f0d"}
{"path": "ExternalSystemsIntegration.tsx", "sha1": "104dbbe51affa8144ac1bdabe786f168ae4ded41", "category": "Wizard/Workflow Logic", "tags": ["fema-compliance", "typescript", "general"], "lines": [65, 77], "snippet": "  { id: 'FMA', name: 'Flood Mitigation Assistance', description: 'Provides funding to reduce or eliminate the risk of repetitive flood damage to buildings insured under the NFIP.' },\n  { id: 'BRIC', name: 'Building Resilient Infrastructure and Communities', description: 'Supports states, local communities, tribes and territories as they undertake hazard mitigation projects.' },\n];\n\n// Mock document management system data\nconst documentRepositories = [\n  { id: 1, name: 'SharePoint', connected: true, lastSync: '2025-04-12 09:30 AM', documentCount: 156 },\n  { id: 2, name: 'Google Drive', connected: true, lastSync: '2025-04-13 10:15 AM', documentCount: 89 },\n  { id: 3, name: 'Dropbox', connected: false, lastSync: 'Never', documentCount: 0 },\n  { id: 4, name: 'OneDrive', connected: true, lastSync: '2025-04-10 02:45 PM', documentCount: 42 },\n  { id: 5, name: 'Box', connected: false, lastSync: 'Never', documentCount: 0 },\n];", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bhazard mitigation\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.721348+00:00", "sig": "c0433a6c4703ab560c91bcda8b877b4c5f9b3b10"}
{"path": "ExternalSystemsIntegration.tsx", "sha1": "104dbbe51affa8144ac1bdabe786f168ae4ded41", "category": "FEMA/Compliance", "tags": ["fema-compliance", "fema-pa", "typescript"], "lines": [79, 91], "snippet": "  { id: 1, name: 'Disaster Recovery Plan.docx', repository: 'SharePoint', lastModified: '2025-04-12', size: '2.4 MB' },\n  { id: 2, name: 'FEMA Compliance Checklist.xlsx', repository: 'Google Drive', lastModified: '2025-04-11', size: '1.8 MB' },\n  { id: 3, name: 'Emergency Response Procedures.pdf', repository: 'SharePoint', lastModified: '2025-04-10', size: '3.5 MB' },\n  { id: 4, name: 'Hazard Mitigation Plan.docx', repository: 'OneDrive', lastModified: '2025-04-09', size: '5.2 MB' },\n  { id: 5, name: 'Public Assistance Application.pdf', repository: 'Google Drive', lastModified: '2025-04-08', size: '1.2 MB' },\n];\n\n// Mock calendar events\nconst calendarEvents = [\n  { id: 1, title: 'FEMA Application Deadline', start: '2025-04-20', end: '2025-04-20', type: 'deadline', calendar: 'Compliance' },\n  { id: 2, title: 'Quarterly Compliance Review', start: '2025-04-25', end: '2025-04-26', type: 'meeting', calendar: 'Compliance' },\n  { id: 3, title: 'Document Submission', start: '2025-05-05', end: '2025-05-05', type: 'deadline', calendar: 'FEMA' },", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.722225+00:00", "sig": "9adc1d1b491e836174d4ff1d62dd109f24a37e71"}
{"path": "ExternalSystemsIntegration.tsx", "sha1": "104dbbe51affa8144ac1bdabe786f168ae4ded41", "category": "FEMA/Compliance", "tags": ["fema-compliance", "fema-pa", "typescript"], "lines": [81, 93], "snippet": "  { id: 3, name: 'Emergency Response Procedures.pdf', repository: 'SharePoint', lastModified: '2025-04-10', size: '3.5 MB' },\n  { id: 4, name: 'Hazard Mitigation Plan.docx', repository: 'OneDrive', lastModified: '2025-04-09', size: '5.2 MB' },\n  { id: 5, name: 'Public Assistance Application.pdf', repository: 'Google Drive', lastModified: '2025-04-08', size: '1.2 MB' },\n];\n\n// Mock calendar events\nconst calendarEvents = [\n  { id: 1, title: 'FEMA Application Deadline', start: '2025-04-20', end: '2025-04-20', type: 'deadline', calendar: 'Compliance' },\n  { id: 2, title: 'Quarterly Compliance Review', start: '2025-04-25', end: '2025-04-26', type: 'meeting', calendar: 'Compliance' },\n  { id: 3, title: 'Document Submission', start: '2025-05-05', end: '2025-05-05', type: 'deadline', calendar: 'FEMA' },\n  { id: 4, title: 'Policy Update Training', start: '2025-05-10', end: '2025-05-10', type: 'training', calendar: 'Team' },\n  { id: 5, title: 'Disaster Recovery Exercise', start: '2025-05-15', end: '2025-05-16', type: 'exercise', calendar: 'Emergency' },", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bhazard mitigation\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.722874+00:00", "sig": "08675c335dc24136dabf879d47118ae36a3fb180"}
{"path": "ExternalSystemsIntegration.tsx", "sha1": "104dbbe51affa8144ac1bdabe786f168ae4ded41", "category": "FEMA/Compliance", "tags": ["fema-compliance", "fema-pa", "typescript"], "lines": [82, 94], "snippet": "  { id: 4, name: 'Hazard Mitigation Plan.docx', repository: 'OneDrive', lastModified: '2025-04-09', size: '5.2 MB' },\n  { id: 5, name: 'Public Assistance Application.pdf', repository: 'Google Drive', lastModified: '2025-04-08', size: '1.2 MB' },\n];\n\n// Mock calendar events\nconst calendarEvents = [\n  { id: 1, title: 'FEMA Application Deadline', start: '2025-04-20', end: '2025-04-20', type: 'deadline', calendar: 'Compliance' },\n  { id: 2, title: 'Quarterly Compliance Review', start: '2025-04-25', end: '2025-04-26', type: 'meeting', calendar: 'Compliance' },\n  { id: 3, title: 'Document Submission', start: '2025-05-05', end: '2025-05-05', type: 'deadline', calendar: 'FEMA' },\n  { id: 4, title: 'Policy Update Training', start: '2025-05-10', end: '2025-05-10', type: 'training', calendar: 'Team' },\n  { id: 5, title: 'Disaster Recovery Exercise', start: '2025-05-15', end: '2025-05-16', type: 'exercise', calendar: 'Emergency' },\n];", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bPublic Assistance\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.723339+00:00", "sig": "510786eea51808128acc62397d3eec298ae49cab"}
{"path": "ExternalSystemsIntegration.tsx", "sha1": "104dbbe51affa8144ac1bdabe786f168ae4ded41", "category": "FEMA/Compliance", "tags": ["fema-compliance", "typescript", "general"], "lines": [87, 99], "snippet": "const calendarEvents = [\n  { id: 1, title: 'FEMA Application Deadline', start: '2025-04-20', end: '2025-04-20', type: 'deadline', calendar: 'Compliance' },\n  { id: 2, title: 'Quarterly Compliance Review', start: '2025-04-25', end: '2025-04-26', type: 'meeting', calendar: 'Compliance' },\n  { id: 3, title: 'Document Submission', start: '2025-05-05', end: '2025-05-05', type: 'deadline', calendar: 'FEMA' },\n  { id: 4, title: 'Policy Update Training', start: '2025-05-10', end: '2025-05-10', type: 'training', calendar: 'Team' },\n  { id: 5, title: 'Disaster Recovery Exercise', start: '2025-05-15', end: '2025-05-16', type: 'exercise', calendar: 'Emergency' },\n];\n\nconst calendarSources = [\n  { id: 1, name: 'Google Calendar', connected: true, lastSync: '2025-04-13 11:30 AM' },\n  { id: 2, name: 'Microsoft Outlook', connected: true, lastSync: '2025-04-13 10:45 AM' },\n  { id: 3, name: 'Apple Calendar', connected: false, lastSync: 'Never' },", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.723901+00:00", "sig": "c10629e96270ae15770b7278b4bc4d4f775f7470"}
{"path": "NotificationSystem.tsx", "sha1": "f7581da5c1e870cf3b8d8a7f6b5bb787a3f567b6", "category": "Data Models", "tags": ["typescript", "data-model", "data-structure"], "lines": [24, 36], "snippet": "\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nconst TabPanel = (props: TabPanelProps) => {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.755736+00:00", "sig": "df2dbcc28d736773d1a5a45cf8a0a25a5221b0f8"}
{"path": "NotificationSystem.tsx", "sha1": "f7581da5c1e870cf3b8d8a7f6b5bb787a3f567b6", "category": "Data Models", "tags": ["typescript", "data-model", "data-structure"], "lines": [50, 62], "snippet": "\ninterface Notification {\n  id: number;\n  type: 'email' | 'sms' | 'app';\n  title: string;\n  message: string;\n  date: string;\n  read: boolean;\n  priority: 'high' | 'medium' | 'low';\n}\n\ninterface NotificationRule {", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.756270+00:00", "sig": "cee1154a213eff4a38378ad4296233f2460fe0e8"}
{"path": "NotificationSystem.tsx", "sha1": "f7581da5c1e870cf3b8d8a7f6b5bb787a3f567b6", "category": "Data Models", "tags": ["typescript", "data-model", "data-structure"], "lines": [60, 72], "snippet": "\ninterface NotificationRule {\n  id: number;\n  name: string;\n  event: string;\n  channels: string[];\n  enabled: boolean;\n}\n\ninterface ScheduledReport {\n  id: number;\n  name: string;", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.756642+00:00", "sig": "254df84506d1a861d11a52e7fd106ef14af4194a"}
{"path": "NotificationSystem.tsx", "sha1": "f7581da5c1e870cf3b8d8a7f6b5bb787a3f567b6", "category": "Data Models", "tags": ["typescript", "data-model", "data-structure"], "lines": [68, 80], "snippet": "\ninterface ScheduledReport {\n  id: number;\n  name: string;\n  reportType: string;\n  frequency: string;\n  nextDelivery: string;\n  recipients: string[];\n  enabled: boolean;\n}\n\nconst NotificationSystem: React.FC = () => {", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.757039+00:00", "sig": "e67e907d027a535f3f22318c8e54690934629c9b"}
{"path": "NotificationSystem.tsx", "sha1": "f7581da5c1e870cf3b8d8a7f6b5bb787a3f567b6", "category": "FEMA/Compliance", "tags": ["typescript", "data-model", "general"], "lines": [90, 102], "snippet": "  \n  // Notification rules state\n  const [notificationRules, setNotificationRules] = useState<NotificationRule[]>([\n    { id: 1, name: 'Policy Match Completion', event: 'policy_match_complete', channels: ['email', 'app'], enabled: true },\n    { id: 2, name: 'Compliance Alerts', event: 'compliance_alert', channels: ['email', 'sms', 'app'], enabled: true },\n    { id: 3, name: 'Report Generation', event: 'report_generated', channels: ['email'], enabled: true },\n    { id: 4, name: 'New Policy Added', event: 'policy_added', channels: ['app'], enabled: false },\n    { id: 5, name: 'System Updates', event: 'system_update', channels: ['email', 'app'], enabled: true },\n  ]);\n  \n  // Scheduled reports state\n  const [scheduledReports, setScheduledReports] = useState<ScheduledReport[]>([", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.758080+00:00", "sig": "444f3dccefb2f95c838c76a0ea6d08b45cae5f81"}
{"path": "NotificationSystem.tsx", "sha1": "f7581da5c1e870cf3b8d8a7f6b5bb787a3f567b6", "category": "Other", "tags": ["typescript", "data-model", "general"], "lines": [148, 160], "snippet": "    setNotificationRules(prev => \n      prev.map(rule => \n        rule.id === id ? { ...rule, enabled: !rule.enabled } : rule\n      )\n    );\n  };\n  \n  const handleToggleReport = (id: number) => {\n    setScheduledReports(prev => \n      prev.map(report => \n        report.id === id ? { ...report, enabled: !report.enabled } : report\n      )", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.759580+00:00", "sig": "93bf18336a6a5350542cb0ee836b9583baa1b3b6"}
{"path": "NotificationSystem.tsx", "sha1": "f7581da5c1e870cf3b8d8a7f6b5bb787a3f567b6", "category": "Other", "tags": ["typescript", "data-model", "general"], "lines": [149, 161], "snippet": "      prev.map(rule => \n        rule.id === id ? { ...rule, enabled: !rule.enabled } : rule\n      )\n    );\n  };\n  \n  const handleToggleReport = (id: number) => {\n    setScheduledReports(prev => \n      prev.map(report => \n        report.id === id ? { ...report, enabled: !report.enabled } : report\n      )\n    );", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.759931+00:00", "sig": "1f740603a858705e7d8db23582c53920d483c947"}
{"path": "NotificationSystem.tsx", "sha1": "f7581da5c1e870cf3b8d8a7f6b5bb787a3f567b6", "category": "Other", "tags": ["typescript", "data-model", "general"], "lines": [162, 174], "snippet": "  \n  const handleOpenRuleDialog = (rule?: NotificationRule) => {\n    if (rule) {\n      setEditingRule(rule);\n      setRuleName(rule.name);\n      setRuleEvent(rule.event);\n      setRuleChannels(rule.channels);\n      setRuleEnabled(rule.enabled);\n    } else {\n      setEditingRule(null);\n      setRuleName('');\n      setRuleEvent('');", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.760393+00:00", "sig": "3ffb54dad107f529a0dc5f8efd954fca0331f4c0"}
{"path": "NotificationSystem.tsx", "sha1": "f7581da5c1e870cf3b8d8a7f6b5bb787a3f567b6", "category": "Other", "tags": ["typescript", "data-model", "general"], "lines": [163, 175], "snippet": "  const handleOpenRuleDialog = (rule?: NotificationRule) => {\n    if (rule) {\n      setEditingRule(rule);\n      setRuleName(rule.name);\n      setRuleEvent(rule.event);\n      setRuleChannels(rule.channels);\n      setRuleEnabled(rule.enabled);\n    } else {\n      setEditingRule(null);\n      setRuleName('');\n      setRuleEvent('');\n      setRuleChannels([]);", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.760739+00:00", "sig": "1821000c1bd313276aaf65e1062ab8537b28f86a"}
{"path": "NotificationSystem.tsx", "sha1": "f7581da5c1e870cf3b8d8a7f6b5bb787a3f567b6", "category": "Other", "tags": ["typescript", "data-model", "general"], "lines": [164, 176], "snippet": "    if (rule) {\n      setEditingRule(rule);\n      setRuleName(rule.name);\n      setRuleEvent(rule.event);\n      setRuleChannels(rule.channels);\n      setRuleEnabled(rule.enabled);\n    } else {\n      setEditingRule(null);\n      setRuleName('');\n      setRuleEvent('');\n      setRuleChannels([]);\n      setRuleEnabled(true);", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.761074+00:00", "sig": "e4ea40a5c92d2a3135315eb7716968d677502860"}
{"path": "Profile.tsx", "sha1": "47fd83ae47e3fb63269579dbb030d0dc1cd21e3f", "category": "Data Models", "tags": ["typescript", "data-model", "data-structure"], "lines": [21, 33], "snippet": "\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nconst TabPanel = (props: TabPanelProps) => {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.778866+00:00", "sig": "83a205f0f7d05e5ed393764bdfacdea2990b34df"}
{"path": "README.md", "sha1": "ebc0392835a5d9866c03bc2dcff3fcf01ad333d5", "category": "FEMA/Compliance", "tags": ["fema-compliance", "general", "code-logic"], "lines": [15, 27], "snippet": "- **Enhanced Dashboard**: Detailed trend analysis and compliance monitoring\n- **External Systems Integration**: Connect with FEMA database, document repositories, and calendars\n\n## Repository Structure\n\n```\ncompliancemax/\n\u251c\u2500\u2500 frontend/                  # React frontend application\n\u2502   \u251c\u2500\u2500 src/\n\u2502   \u2502   \u251c\u2500\u2500 components/        # Reusable UI components\n\u2502   \u2502   \u251c\u2500\u2500 pages/             # Page components\n\u2502   \u2502   \u251c\u2500\u2500 services/          # API service integrations", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.796439+00:00", "sig": "ed617cded307bd5c2c5583038f180e3948bebbbf"}
{"path": "Reports.tsx", "sha1": "a9270713738ef2ee9836e01e4f722eb9c0ea886b", "category": "FEMA/Compliance", "tags": ["fema-compliance", "typescript", "general"], "lines": [57, 69], "snippet": "    { id: 2, name: 'Policy Coverage Analysis', type: 'Policy', generatedDate: '2025-03-15', status: 'Complete', score: 92 },\n    { id: 3, name: 'FEMA Requirements Gap Analysis', type: 'Gap Analysis', generatedDate: '2025-03-22', status: 'Complete', score: 76 },\n    { id: 4, name: 'Document Completeness Report', type: 'Document', generatedDate: '2025-04-05', status: 'In Progress', score: 45 },\n    { id: 5, name: 'Annual Security Assessment', type: 'Security', generatedDate: '2025-02-28', status: 'Complete', score: 81 },\n  ]);\n  \n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('All');\n  const [tabValue, setTabValue] = useState(0);\n  const [openGenerateDialog, setOpenGenerateDialog] = useState(false);\n  const [reportType, setReportType] = useState('Compliance');\n  ", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "high", "action": "review-security", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.823362+00:00", "sig": "2c2483f8fd46cad7d601defe07f918aa0c915d17"}
{"path": "Reports.tsx", "sha1": "a9270713738ef2ee9836e01e4f722eb9c0ea886b", "category": "FEMA/Compliance", "tags": ["fema-compliance", "typescript", "general"], "lines": [329, 341], "snippet": "              <Typography variant=\"body2\" color=\"text.secondary\">\n                \u2022 FEMA Requirements Gap Analysis\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                \u2022 Document Completeness Report\n              </Typography>\n            </Box>\n          </CardContent>\n        </Card>\n      </TabPanel>\n      \n      {/* Generate Report Dialog */}", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.829891+00:00", "sig": "852066b106c07ddf81df311f06d0cdec206fdeb9"}
{"path": "Reports.tsx", "sha1": "a9270713738ef2ee9836e01e4f722eb9c0ea886b", "category": "Data Models", "tags": ["typescript", "data-model", "data-structure"], "lines": [18, 30], "snippet": "\ninterface Report {\n  id: number;\n  name: string;\n  type: string;\n  generatedDate: string;\n  status: string;\n  score: number;\n}\n\ninterface TabPanelProps {\n  children?: React.ReactNode;", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.831504+00:00", "sig": "91b2f97a638537641d7378868becae194112ade5"}
{"path": "Reports.tsx", "sha1": "a9270713738ef2ee9836e01e4f722eb9c0ea886b", "category": "Data Models", "tags": ["typescript", "data-model", "data-structure"], "lines": [27, 39], "snippet": "\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\nconst TabPanel = (props: TabPanelProps) => {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.831943+00:00", "sig": "463192f9ad3f1f07dddcb622ff0afe1bfbdce948"}
{"path": "USER_GUIDE.md", "sha1": "332792ee6d1754ef0b504deab8664948884d6532", "category": "FEMA/Compliance", "tags": ["fema-compliance", "general", "code-logic"], "lines": [227, 239], "snippet": "\n### FEMA Database Integration\n\n1. Navigate to \"External Systems Integration\" from the main menu\n2. Select the \"FEMA Database\" tab\n3. Use the search box to find specific disasters\n4. Click on a disaster to view details\n5. Click \"Import Selected Disaster\" to bring data into ComplianceMax\n\n### Document Management Integration\n\n1. Select the \"Document Management\" tab", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.857353+00:00", "sig": "fad916dfabd2463587e011e2baa133a78b410cc4"}
{"path": "USER_GUIDE.md", "sha1": "332792ee6d1754ef0b504deab8664948884d6532", "category": "FEMA/Compliance", "tags": ["fema-compliance", "general", "code-logic"], "lines": [230, 242], "snippet": "1. Navigate to \"External Systems Integration\" from the main menu\n2. Select the \"FEMA Database\" tab\n3. Use the search box to find specific disasters\n4. Click on a disaster to view details\n5. Click \"Import Selected Disaster\" to bring data into ComplianceMax\n\n### Document Management Integration\n\n1. Select the \"Document Management\" tab\n2. View connected repositories\n3. Click \"Add Repository\" to connect a new document source\n4. Configure connection details:", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.857909+00:00", "sig": "02798c3a9085432258588b7a3ca97af4ade24484"}
{"path": "USER_GUIDE.md", "sha1": "332792ee6d1754ef0b504deab8664948884d6532", "category": "Config/Data Assets", "tags": ["data-model", "general", "code-logic"], "lines": [202, 214], "snippet": "\n### Configuring Notification Rules\n\n1. Select the \"Notification Rules\" tab\n2. Click \"Add Rule\" to create a new notification rule\n3. Enter a rule name\n4. Select the event that triggers the notification\n5. Choose notification channels (Email, SMS, In-App)\n6. Toggle the rule on/off as needed\n7. Click \"Create\" to save the rule\n\n### Scheduled Reports", "reason": "Configuration or mapping logic", "detectors": ["regex:\\brules?\\b"], "risk_level": "medium", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.863545+00:00", "sig": "b157fa3009ccc760193cf4e12cef99cec9b0fb08"}
{"path": "USER_GUIDE.md", "sha1": "332792ee6d1754ef0b504deab8664948884d6532", "category": "Other", "tags": ["data-model", "general", "code-logic"], "lines": [204, 216], "snippet": "\n1. Select the \"Notification Rules\" tab\n2. Click \"Add Rule\" to create a new notification rule\n3. Enter a rule name\n4. Select the event that triggers the notification\n5. Choose notification channels (Email, SMS, In-App)\n6. Toggle the rule on/off as needed\n7. Click \"Create\" to save the rule\n\n### Scheduled Reports\n\n1. Select the \"Scheduled Reports\" tab", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.863925+00:00", "sig": "93d2fa8458af632c2c5da88c733a293b4f5645eb"}
{"path": "USER_GUIDE.md", "sha1": "332792ee6d1754ef0b504deab8664948884d6532", "category": "Other", "tags": ["data-model", "general", "code-logic"], "lines": [205, 217], "snippet": "1. Select the \"Notification Rules\" tab\n2. Click \"Add Rule\" to create a new notification rule\n3. Enter a rule name\n4. Select the event that triggers the notification\n5. Choose notification channels (Email, SMS, In-App)\n6. Toggle the rule on/off as needed\n7. Click \"Create\" to save the rule\n\n### Scheduled Reports\n\n1. Select the \"Scheduled Reports\" tab\n2. Click \"Schedule New Report\" to set up a recurring report", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.864302+00:00", "sig": "ad5fbcb2e34ecb26e8d3889790ac0bbd39bae19f"}
{"path": "USER_GUIDE.md", "sha1": "332792ee6d1754ef0b504deab8664948884d6532", "category": "Config/Data Assets", "tags": ["data-model", "general", "code-logic"], "lines": [206, 218], "snippet": "2. Click \"Add Rule\" to create a new notification rule\n3. Enter a rule name\n4. Select the event that triggers the notification\n5. Choose notification channels (Email, SMS, In-App)\n6. Toggle the rule on/off as needed\n7. Click \"Create\" to save the rule\n\n### Scheduled Reports\n\n1. Select the \"Scheduled Reports\" tab\n2. Click \"Schedule New Report\" to set up a recurring report\n3. Configure report details:", "reason": "Configuration or mapping logic", "detectors": ["regex:\\brules?\\b"], "risk_level": "medium", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.864631+00:00", "sig": "cdce65c3f9b0019c4ab1e92b1a4ec28edc8dfdfd"}
{"path": "USER_GUIDE.md", "sha1": "332792ee6d1754ef0b504deab8664948884d6532", "category": "Data Models", "tags": ["data-model", "general", "code-logic"], "lines": [209, 221], "snippet": "5. Choose notification channels (Email, SMS, In-App)\n6. Toggle the rule on/off as needed\n7. Click \"Create\" to save the rule\n\n### Scheduled Reports\n\n1. Select the \"Scheduled Reports\" tab\n2. Click \"Schedule New Report\" to set up a recurring report\n3. Configure report details:\n   - Report name and type\n   - Frequency (Daily, Weekly, Monthly, Quarterly)\n   - Recipients (email addresses)", "reason": "Configuration or mapping logic", "detectors": ["regex:\\brules?\\b"], "risk_level": "medium", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.865077+00:00", "sig": "f7e74b6ae8e590622b909828e80134d6bb4354c9"}
{"path": "USER_GUIDE.md", "sha1": "332792ee6d1754ef0b504deab8664948884d6532", "category": "Data Models", "tags": ["data-model", "general", "code-logic"], "lines": [210, 222], "snippet": "6. Toggle the rule on/off as needed\n7. Click \"Create\" to save the rule\n\n### Scheduled Reports\n\n1. Select the \"Scheduled Reports\" tab\n2. Click \"Schedule New Report\" to set up a recurring report\n3. Configure report details:\n   - Report name and type\n   - Frequency (Daily, Weekly, Monthly, Quarterly)\n   - Recipients (email addresses)\n4. Toggle the schedule on/off as needed", "reason": "Configuration or mapping logic", "detectors": ["regex:\\brules?\\b"], "risk_level": "medium", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.865418+00:00", "sig": "ed1966552524289eedcfeb0f746efa3adf6c44ba"}
{"path": "USER_GUIDE.md", "sha1": "332792ee6d1754ef0b504deab8664948884d6532", "category": "Authentication/Security", "tags": ["access-control", "auth-security", "general"], "lines": [262, 274], "snippet": "\nThe User Management section allows administrators to manage user accounts and permissions.\n\n### Adding Users\n\n1. Navigate to \"Settings\" > \"User Management\"\n2. Click \"Add User\"\n3. Enter user details:\n   - Name\n   - Email address\n   - Role (Admin, Manager, User)\n4. Click \"Create User\"", "reason": "Handles authentication or security", "detectors": ["regex:\\bpermissions\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.870479+00:00", "sig": "d411b09d822c060a994d4c30fa895ef4f8045cee"}
{"path": "USER_GUIDE.md", "sha1": "332792ee6d1754ef0b504deab8664948884d6532", "category": "FEMA/Compliance", "tags": ["access-control", "auth-security", "general"], "lines": [275, 287], "snippet": "\n### Managing Roles and Permissions\n\n1. Navigate to \"Settings\" > \"Roles & Permissions\"\n2. Select a role to modify\n3. Adjust permissions for each feature\n4. Click \"Save Changes\" to update\n\n## Support and Troubleshooting\n\nIf you encounter any issues while using ComplianceMax:\n", "reason": "Handles authentication or security", "detectors": ["regex:\\broles\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": false, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.870906+00:00", "sig": "c748cbdacbeb7329c0e20c85c445a8384d2b12f3"}
{"path": "api_integration.py", "sha1": "c4e9ddb4cd617697f3aa5da5e1b779b59579ecb4", "category": "FEMA/Compliance", "tags": ["access-control", "auth-security", "python"], "lines": [68, 80], "snippet": "    \"\"\"\n    # Verify permissions\n    if not verify_permissions(current_user, \"policy_matcher:use\"):\n        raise HTTPException(\n            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"Not enough permissions to use policy matcher\"\n        )\n    \n    # Create temporary directory for processing\n    temp_dir = tempfile.mkdtemp(prefix=\"policy_matcher_\")\n    \n    # Save uploaded requirements file", "reason": "Handles authentication or security", "detectors": ["regex:\\bpermissions\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.893503+00:00", "sig": "0408725ab145c846b25e5f584032710009cf780d"}
{"path": "api_integration.py", "sha1": "c4e9ddb4cd617697f3aa5da5e1b779b59579ecb4", "category": "FEMA/Compliance", "tags": ["access-control", "auth-security", "python"], "lines": [72, 84], "snippet": "            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"Not enough permissions to use policy matcher\"\n        )\n    \n    # Create temporary directory for processing\n    temp_dir = tempfile.mkdtemp(prefix=\"policy_matcher_\")\n    \n    # Save uploaded requirements file\n    requirements_path = os.path.join(temp_dir, requirements_file.filename)\n    with open(requirements_path, \"wb\") as f:\n        content = await requirements_file.read()\n        f.write(content)", "reason": "Handles authentication or security", "detectors": ["regex:\\bpermissions\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.893896+00:00", "sig": "e6f0549126b77e1af4d92ccf46e48e3ce7576aa1"}
{"path": "api_integration.py", "sha1": "c4e9ddb4cd617697f3aa5da5e1b779b59579ecb4", "category": "FEMA/Compliance", "tags": ["access-control", "auth-security", "python"], "lines": [149, 161], "snippet": "    \"\"\"\n    # Verify permissions\n    if not verify_permissions(current_user, \"policy_matcher:use\"):\n        raise HTTPException(\n            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"Not enough permissions to use policy matcher\"\n        )\n    \n    # Check if job exists\n    job = db.query(PolicyMatch).filter(\n        PolicyMatch.job_id == job_id,\n        PolicyMatch.user_id == current_user.id", "reason": "Handles authentication or security", "detectors": ["regex:\\bpermissions\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.895303+00:00", "sig": "0a5401f56d7f00794384778d10656b7eea2d3470"}
{"path": "api_integration.py", "sha1": "c4e9ddb4cd617697f3aa5da5e1b779b59579ecb4", "category": "FEMA/Compliance", "tags": ["access-control", "auth-security", "python"], "lines": [153, 165], "snippet": "            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"Not enough permissions to use policy matcher\"\n        )\n    \n    # Check if job exists\n    job = db.query(PolicyMatch).filter(\n        PolicyMatch.job_id == job_id,\n        PolicyMatch.user_id == current_user.id\n    ).first()\n    \n    if not job:\n        raise HTTPException(", "reason": "Handles authentication or security", "detectors": ["regex:\\bpermissions\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.895674+00:00", "sig": "adfefc94cb449cb19f61471e94d7a45fc8e0b071"}
{"path": "api_integration.py", "sha1": "c4e9ddb4cd617697f3aa5da5e1b779b59579ecb4", "category": "FEMA/Compliance", "tags": ["access-control", "auth-security", "python"], "lines": [194, 206], "snippet": "    \"\"\"\n    # Verify permissions\n    if not verify_permissions(current_user, \"policy_matcher:use\"):\n        raise HTTPException(\n            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"Not enough permissions to use policy matcher\"\n        )\n    \n    # Check if job exists and is completed\n    job = db.query(PolicyMatch).filter(\n        PolicyMatch.job_id == job_id,\n        PolicyMatch.user_id == current_user.id,", "reason": "Handles authentication or security", "detectors": ["regex:\\bpermissions\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.896641+00:00", "sig": "a227751ebed54adc8db55eee829d5220073293c9"}
{"path": "api_integration.py", "sha1": "c4e9ddb4cd617697f3aa5da5e1b779b59579ecb4", "category": "FEMA/Compliance", "tags": ["access-control", "auth-security", "python"], "lines": [198, 210], "snippet": "            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"Not enough permissions to use policy matcher\"\n        )\n    \n    # Check if job exists and is completed\n    job = db.query(PolicyMatch).filter(\n        PolicyMatch.job_id == job_id,\n        PolicyMatch.user_id == current_user.id,\n        PolicyMatch.status == \"completed\"\n    ).first()\n    \n    if not job:", "reason": "Handles authentication or security", "detectors": ["regex:\\bpermissions\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.897021+00:00", "sig": "689c0a57354a26592d8204a1482680728c1b2c3d"}
{"path": "api_integration.py", "sha1": "c4e9ddb4cd617697f3aa5da5e1b779b59579ecb4", "category": "FEMA/Compliance", "tags": ["access-control", "auth-security", "python"], "lines": [235, 247], "snippet": "    \"\"\"\n    # Verify permissions\n    if not verify_permissions(current_user, \"policy_matcher:use\"):\n        raise HTTPException(\n            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"Not enough permissions to use policy matcher\"\n        )\n    \n    # Check if job exists and is completed\n    job = db.query(PolicyMatch).filter(\n        PolicyMatch.job_id == job_id,\n        PolicyMatch.user_id == current_user.id,", "reason": "Handles authentication or security", "detectors": ["regex:\\bpermissions\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.897773+00:00", "sig": "e1b331f0cb099a2f4c80166d7e74fa612cfa3610"}
{"path": "api_integration.py", "sha1": "c4e9ddb4cd617697f3aa5da5e1b779b59579ecb4", "category": "FEMA/Compliance", "tags": ["access-control", "auth-security", "python"], "lines": [239, 251], "snippet": "            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"Not enough permissions to use policy matcher\"\n        )\n    \n    # Check if job exists and is completed\n    job = db.query(PolicyMatch).filter(\n        PolicyMatch.job_id == job_id,\n        PolicyMatch.user_id == current_user.id,\n        PolicyMatch.status == \"completed\"\n    ).first()\n    \n    if not job:", "reason": "Handles authentication or security", "detectors": ["regex:\\bpermissions\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.898152+00:00", "sig": "c05b2340cda5fb6cc8f627cd35ad99a353fae11b"}
{"path": "api_integration.py", "sha1": "c4e9ddb4cd617697f3aa5da5e1b779b59579ecb4", "category": "FEMA/Compliance", "tags": ["access-control", "auth-security", "python"], "lines": [307, 319], "snippet": "    \"\"\"\n    # Verify permissions\n    if not verify_permissions(current_user, \"policy_matcher:delete\"):\n        raise HTTPException(\n            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"Not enough permissions to delete policy matcher jobs\"\n        )\n    \n    # Check if job exists\n    job = db.query(PolicyMatch).filter(\n        PolicyMatch.job_id == job_id,\n        PolicyMatch.user_id == current_user.id", "reason": "Handles authentication or security", "detectors": ["regex:\\bpermissions\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.899349+00:00", "sig": "ea817ce4800cf5713df421f5950fabce14b86d18"}
{"path": "api_integration.py", "sha1": "c4e9ddb4cd617697f3aa5da5e1b779b59579ecb4", "category": "FEMA/Compliance", "tags": ["access-control", "auth-security", "python"], "lines": [311, 323], "snippet": "            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"Not enough permissions to delete policy matcher jobs\"\n        )\n    \n    # Check if job exists\n    job = db.query(PolicyMatch).filter(\n        PolicyMatch.job_id == job_id,\n        PolicyMatch.user_id == current_user.id\n    ).first()\n    \n    if not job:\n        raise HTTPException(", "reason": "Handles authentication or security", "detectors": ["regex:\\bpermissions\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.899720+00:00", "sig": "b0c3bd61af361b78e90a9ce233822c7275d900aa"}
{"path": "component_tests.js", "sha1": "fcbd0993edadcf09c3bf5c9dcf3c838c1fa7704c", "category": "FEMA/Compliance", "tags": ["fema-compliance", "javascript", "general"], "lines": [193, 205], "snippet": "    expect(screen.getByText('External Systems Integration')).toBeInTheDocument();\n    expect(screen.getByText('FEMA Database')).toBeInTheDocument();\n  });\n\n  test('searches FEMA database', () => {\n    render(<ExternalSystemsIntegration />);\n    \n    // Enter search query\n    const searchInput = screen.getByPlaceholderText('Search disasters by ID, name, or state');\n    fireEvent.change(searchInput, { target: { value: 'Hurricane' } });\n    \n    // Click search button", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.935717+00:00", "sig": "60e56cabf840054b7f1b0492ed301c9176d840c8"}
{"path": "component_tests.js", "sha1": "fcbd0993edadcf09c3bf5c9dcf3c838c1fa7704c", "category": "FEMA/Compliance", "tags": ["fema-compliance", "javascript", "general"], "lines": [196, 208], "snippet": "\n  test('searches FEMA database', () => {\n    render(<ExternalSystemsIntegration />);\n    \n    // Enter search query\n    const searchInput = screen.getByPlaceholderText('Search disasters by ID, name, or state');\n    fireEvent.change(searchInput, { target: { value: 'Hurricane' } });\n    \n    // Click search button\n    const searchButton = screen.getByText('Search').closest('button');\n    fireEvent.click(searchButton);\n    ", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.936039+00:00", "sig": "c1abdf385bfecba7652ff74b9c10069d02c13f11"}
{"path": "database_integration.py", "sha1": "2642e67d50f07f6cf42ae9ceed1d5e3dabaf4c8e", "category": "FEMA/Compliance", "tags": ["data-model", "python", "general"], "lines": [56, 68], "snippet": "        Returns:\n            Dictionary mapping policy names to policy text\n        \"\"\"\n        query = self.db.query(Document).filter(Document.document_type == \"policy\")\n        \n        # Apply filters\n        if policy_ids:\n            query = query.filter(Document.id.in_(policy_ids))\n        \n        if project_id:\n            query = query.filter(Document.project_id == project_id)\n        ", "reason": "Configuration or mapping logic", "detectors": ["regex:\\bmapping\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.953810+00:00", "sig": "b90708257bc181c166388486df4e40e69cf97c76"}
{"path": "database_integration.py", "sha1": "2642e67d50f07f6cf42ae9ceed1d5e3dabaf4c8e", "category": "Data Models", "tags": ["eligibility-rules", "data-model", "python"], "lines": [98, 110], "snippet": "        Args:\n            requirements: Dictionary mapping requirement names to requirement text\n            requirement_categories: Dictionary mapping category types to lists of requirement names\n            job_id: Job ID for the matching task\n            user_id: ID of the user who initiated the matching\n            project_id: Optional project ID to associate with the requirements\n            \n        Returns:\n            Dictionary mapping requirement names to requirement IDs\n        \"\"\"\n        requirement_ids = {}\n        ", "reason": "Contains eligibility or business rules", "detectors": ["regex:\\bmapping\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.954740+00:00", "sig": "f427f2d023e0696531acba6f3b84bce34bda5538"}
{"path": "database_integration.py", "sha1": "2642e67d50f07f6cf42ae9ceed1d5e3dabaf4c8e", "category": "Data Models", "tags": ["eligibility-rules", "data-model", "python"], "lines": [99, 111], "snippet": "            requirements: Dictionary mapping requirement names to requirement text\n            requirement_categories: Dictionary mapping category types to lists of requirement names\n            job_id: Job ID for the matching task\n            user_id: ID of the user who initiated the matching\n            project_id: Optional project ID to associate with the requirements\n            \n        Returns:\n            Dictionary mapping requirement names to requirement IDs\n        \"\"\"\n        requirement_ids = {}\n        \n        for req_name, req_text in requirements.items():", "reason": "Contains eligibility or business rules", "detectors": ["regex:\\bmapping\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.955130+00:00", "sig": "e5f50ed6ae8bb5aadcdbb219d4b2b8749cf49cf1"}
{"path": "database_integration.py", "sha1": "2642e67d50f07f6cf42ae9ceed1d5e3dabaf4c8e", "category": "Data Models", "tags": ["eligibility-rules", "data-model", "python"], "lines": [105, 117], "snippet": "        Returns:\n            Dictionary mapping requirement names to requirement IDs\n        \"\"\"\n        requirement_ids = {}\n        \n        for req_name, req_text in requirements.items():\n            # Determine category\n            category = \"Other\"\n            for cat_type, cat_reqs in requirement_categories.items():\n                if req_name in cat_reqs:\n                    category = cat_type\n                    break", "reason": "Contains eligibility or business rules", "detectors": ["regex:\\bmapping\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.955565+00:00", "sig": "9a9a6278b850a69c7e098dd0231238110deb494a"}
{"path": "database_integration.py", "sha1": "2642e67d50f07f6cf42ae9ceed1d5e3dabaf4c8e", "category": "FEMA/Compliance", "tags": ["data-model", "python", "general"], "lines": [149, 161], "snippet": "        Args:\n            matches: Dictionary mapping requirement names to lists of (policy_name, score) tuples\n            requirement_ids: Dictionary mapping requirement names to requirement IDs\n            job_id: Job ID for the matching task\n            user_id: ID of the user who initiated the matching\n            project_id: Optional project ID to associate with the matches\n        \"\"\"\n        match_count = 0\n        \n        for req_name, policy_matches in matches.items():\n            if req_name not in requirement_ids:\n                logger.warning(f\"Requirement '{req_name}' not found in requirement_ids\")", "reason": "Configuration or mapping logic", "detectors": ["regex:\\bmapping\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.956523+00:00", "sig": "e89dd28cac4dc13283ac7d0478e6f85e84298117"}
{"path": "database_integration.py", "sha1": "2642e67d50f07f6cf42ae9ceed1d5e3dabaf4c8e", "category": "FEMA/Compliance", "tags": ["data-model", "python", "general"], "lines": [150, 162], "snippet": "            matches: Dictionary mapping requirement names to lists of (policy_name, score) tuples\n            requirement_ids: Dictionary mapping requirement names to requirement IDs\n            job_id: Job ID for the matching task\n            user_id: ID of the user who initiated the matching\n            project_id: Optional project ID to associate with the matches\n        \"\"\"\n        match_count = 0\n        \n        for req_name, policy_matches in matches.items():\n            if req_name not in requirement_ids:\n                logger.warning(f\"Requirement '{req_name}' not found in requirement_ids\")\n                continue", "reason": "Configuration or mapping logic", "detectors": ["regex:\\bmapping\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.956881+00:00", "sig": "abca5cc6c48b49fb9a7eaf5cc748bba62b9411cd"}
{"path": "end_to_end_tests.py", "sha1": "c9d65fed689f4b93dd20b6748ed487b540b1699d", "category": "FEMA/Compliance", "tags": ["fema-compliance", "python", "general"], "lines": [287, 299], "snippet": "    \n    # Test FEMA Database tab\n    WebDriverWait(driver, 10).until(\n        EC.presence_of_element_located((By.XPATH, \"//h6[contains(text(), 'FEMA Disaster Database')]\"))\n    )\n    \n    # Search for a disaster\n    search_input = driver.find_element(By.XPATH, \"//input[@placeholder='Search disasters by ID, name, or state']\")\n    search_input.send_keys(\"Hurricane\")\n    driver.find_element(By.XPATH, \"//button[contains(text(), 'Search')]\").click()\n    \n    # Verify search results", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.976149+00:00", "sig": "d1da240a670227f7e6ed94f22d0c0479209ff274"}
{"path": "end_to_end_tests.py", "sha1": "c9d65fed689f4b93dd20b6748ed487b540b1699d", "category": "FEMA/Compliance", "tags": ["fema-compliance", "python", "general"], "lines": [289, 301], "snippet": "    WebDriverWait(driver, 10).until(\n        EC.presence_of_element_located((By.XPATH, \"//h6[contains(text(), 'FEMA Disaster Database')]\"))\n    )\n    \n    # Search for a disaster\n    search_input = driver.find_element(By.XPATH, \"//input[@placeholder='Search disasters by ID, name, or state']\")\n    search_input.send_keys(\"Hurricane\")\n    driver.find_element(By.XPATH, \"//button[contains(text(), 'Search')]\").click()\n    \n    # Verify search results\n    WebDriverWait(driver, 10).until(\n        EC.presence_of_element_located((By.XPATH, \"//td[contains(text(), 'Hurricane')]\"))", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.976504+00:00", "sig": "66ebe0465d18c5fa90abb01087cae55d9eb655ac"}
{"path": "end_to_end_tests.py", "sha1": "c9d65fed689f4b93dd20b6748ed487b540b1699d", "category": "Other", "tags": ["data-model", "python", "general"], "lines": [198, 210], "snippet": "    \n    # Go to Notification Rules tab\n    driver.find_element(By.XPATH, \"//span[contains(text(), 'Notification Rules')]\").click()\n    \n    # Add a new rule\n    WebDriverWait(driver, 10).until(\n        EC.presence_of_element_located((By.XPATH, \"//button[contains(text(), 'Add Rule')]\"))\n    )\n    driver.find_element(By.XPATH, \"//button[contains(text(), 'Add Rule')]\").click()\n    \n    # Fill in rule details\n    WebDriverWait(driver, 10).until(", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.983491+00:00", "sig": "637723010f2c1ad55a125d8fc59b0a4713e6e33c"}
{"path": "end_to_end_tests.py", "sha1": "c9d65fed689f4b93dd20b6748ed487b540b1699d", "category": "Other", "tags": ["data-model", "python", "general"], "lines": [199, 211], "snippet": "    # Go to Notification Rules tab\n    driver.find_element(By.XPATH, \"//span[contains(text(), 'Notification Rules')]\").click()\n    \n    # Add a new rule\n    WebDriverWait(driver, 10).until(\n        EC.presence_of_element_located((By.XPATH, \"//button[contains(text(), 'Add Rule')]\"))\n    )\n    driver.find_element(By.XPATH, \"//button[contains(text(), 'Add Rule')]\").click()\n    \n    # Fill in rule details\n    WebDriverWait(driver, 10).until(\n        EC.presence_of_element_located((By.XPATH, \"//input[@label='Rule Name']\"))", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.983885+00:00", "sig": "8e4c67098541aaa7f0d6b75a08ca5afd5260dfd8"}
{"path": "end_to_end_tests.py", "sha1": "c9d65fed689f4b93dd20b6748ed487b540b1699d", "category": "Other", "tags": ["data-model", "python", "general"], "lines": [201, 213], "snippet": "    \n    # Add a new rule\n    WebDriverWait(driver, 10).until(\n        EC.presence_of_element_located((By.XPATH, \"//button[contains(text(), 'Add Rule')]\"))\n    )\n    driver.find_element(By.XPATH, \"//button[contains(text(), 'Add Rule')]\").click()\n    \n    # Fill in rule details\n    WebDriverWait(driver, 10).until(\n        EC.presence_of_element_located((By.XPATH, \"//input[@label='Rule Name']\"))\n    )\n    driver.find_element(By.XPATH, \"//input[@label='Rule Name']\").send_keys(\"Test Rule\")", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.984258+00:00", "sig": "97734da805b9aaf55be07a7e281903d90c959951"}
{"path": "end_to_end_tests.py", "sha1": "c9d65fed689f4b93dd20b6748ed487b540b1699d", "category": "Data Models", "tags": ["data-model", "python", "general"], "lines": [203, 215], "snippet": "    WebDriverWait(driver, 10).until(\n        EC.presence_of_element_located((By.XPATH, \"//button[contains(text(), 'Add Rule')]\"))\n    )\n    driver.find_element(By.XPATH, \"//button[contains(text(), 'Add Rule')]\").click()\n    \n    # Fill in rule details\n    WebDriverWait(driver, 10).until(\n        EC.presence_of_element_located((By.XPATH, \"//input[@label='Rule Name']\"))\n    )\n    driver.find_element(By.XPATH, \"//input[@label='Rule Name']\").send_keys(\"Test Rule\")\n    \n    # Select event type", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.984615+00:00", "sig": "8be979d2d01396c1ac388c23ba0cab300c84cbc9"}
{"path": "end_to_end_tests.py", "sha1": "c9d65fed689f4b93dd20b6748ed487b540b1699d", "category": "FEMA/Compliance", "tags": ["data-model", "python", "general"], "lines": [205, 217], "snippet": "    )\n    driver.find_element(By.XPATH, \"//button[contains(text(), 'Add Rule')]\").click()\n    \n    # Fill in rule details\n    WebDriverWait(driver, 10).until(\n        EC.presence_of_element_located((By.XPATH, \"//input[@label='Rule Name']\"))\n    )\n    driver.find_element(By.XPATH, \"//input[@label='Rule Name']\").send_keys(\"Test Rule\")\n    \n    # Select event type\n    driver.find_element(By.ID, \"event-select\").click()\n    driver.find_element(By.XPATH, \"//li[contains(text(), 'Policy Match Completion')]\").click()", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.984962+00:00", "sig": "0a63d50c210cbad927dbc332fe868b938a7d8125"}
{"path": "end_to_end_tests.py", "sha1": "c9d65fed689f4b93dd20b6748ed487b540b1699d", "category": "FEMA/Compliance", "tags": ["data-model", "python", "general"], "lines": [207, 219], "snippet": "    \n    # Fill in rule details\n    WebDriverWait(driver, 10).until(\n        EC.presence_of_element_located((By.XPATH, \"//input[@label='Rule Name']\"))\n    )\n    driver.find_element(By.XPATH, \"//input[@label='Rule Name']\").send_keys(\"Test Rule\")\n    \n    # Select event type\n    driver.find_element(By.ID, \"event-select\").click()\n    driver.find_element(By.XPATH, \"//li[contains(text(), 'Policy Match Completion')]\").click()\n    \n    # Select channels", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.985288+00:00", "sig": "fce5a5b286d6df748ba5962f487b82c29d69637c"}
{"path": "end_to_end_tests.py", "sha1": "c9d65fed689f4b93dd20b6748ed487b540b1699d", "category": "FEMA/Compliance", "tags": ["data-model", "python", "general"], "lines": [209, 221], "snippet": "    WebDriverWait(driver, 10).until(\n        EC.presence_of_element_located((By.XPATH, \"//input[@label='Rule Name']\"))\n    )\n    driver.find_element(By.XPATH, \"//input[@label='Rule Name']\").send_keys(\"Test Rule\")\n    \n    # Select event type\n    driver.find_element(By.ID, \"event-select\").click()\n    driver.find_element(By.XPATH, \"//li[contains(text(), 'Policy Match Completion')]\").click()\n    \n    # Select channels\n    driver.find_element(By.XPATH, \"//span[contains(text(), 'Email')]\").click()\n    ", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.985634+00:00", "sig": "3f1115492752527d85f1e7c81605686bc095f6c2"}
{"path": "end_to_end_tests.py", "sha1": "c9d65fed689f4b93dd20b6748ed487b540b1699d", "category": "FEMA/Compliance", "tags": ["data-model", "python", "general"], "lines": [211, 223], "snippet": "    )\n    driver.find_element(By.XPATH, \"//input[@label='Rule Name']\").send_keys(\"Test Rule\")\n    \n    # Select event type\n    driver.find_element(By.ID, \"event-select\").click()\n    driver.find_element(By.XPATH, \"//li[contains(text(), 'Policy Match Completion')]\").click()\n    \n    # Select channels\n    driver.find_element(By.XPATH, \"//span[contains(text(), 'Email')]\").click()\n    \n    # Save rule\n    driver.find_element(By.XPATH, \"//button[contains(text(), 'Create')]\").click()", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "high", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.985987+00:00", "sig": "845cfb6d3f5e730c26a1dfaa9e28eec2942b0a6b"}
{"path": "frontend_integration.tsx", "sha1": "694b46c0ec39446f390d8309667c222d25d8c78e", "category": "FEMA/Compliance", "tags": ["typescript", "data-model", "data-structure"], "lines": [29, 41], "snippet": "// Types\ninterface PolicyMatcherProps {\n  projectId?: number;\n}\n\ninterface MatchingJob {\n  job_id: string;\n  status: 'processing' | 'completed' | 'failed';\n  created_at: string;\n  completed_at?: string;\n  report_url?: string;\n  error_message?: string;", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.996808+00:00", "sig": "358656a8398cc915a26ce8a78ce8a5739e941cc5"}
{"path": "frontend_integration.tsx", "sha1": "694b46c0ec39446f390d8309667c222d25d8c78e", "category": "FEMA/Compliance", "tags": ["typescript", "data-model", "data-structure"], "lines": [33, 45], "snippet": "\ninterface MatchingJob {\n  job_id: string;\n  status: 'processing' | 'completed' | 'failed';\n  created_at: string;\n  completed_at?: string;\n  report_url?: string;\n  error_message?: string;\n}\n\nconst PolicyMatcher: React.FC<PolicyMatcherProps> = ({ projectId }) => {\n  // State", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:43.997133+00:00", "sig": "8f83238bb9473dffcc4b2668b90641859ad20af7"}
{"path": "policy_matcher_api.py", "sha1": "c303b90461211785cdfa8054c7264bc5bace0a83", "category": "FEMA/Compliance", "tags": ["data-model", "data-structure", "python"], "lines": [3, 15], "snippet": "from typing import List, Optional\nfrom pydantic import BaseModel\nimport uuid\nimport os\nimport tempfile\nimport json\nfrom datetime import datetime\nimport shutil\n\n# Import the refactored policy matcher\nfrom refactored_policy_matcher.main import run_policy_matcher\nfrom refactored_policy_matcher.config import Config", "reason": "Defines data schemas or models", "detectors": ["regex:\\bpydantic\\b"], "risk_level": "medium", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:44.017062+00:00", "sig": "9c0b3b2f74fee57eefcd89e86f9fffea41ca6096"}
{"path": "policy_matcher_api.py", "sha1": "c303b90461211785cdfa8054c7264bc5bace0a83", "category": "FEMA/Compliance", "tags": ["data-model", "python", "general"], "lines": [303, 309], "snippet": "        {\"id\": 3, \"name\": \"Data Protection Policy\", \"description\": \"Guidelines for data handling\", \"last_updated\": \"2025-03-10\"},\n        {\"id\": 4, \"name\": \"Acceptable Use Policy\", \"description\": \"Rules for IT resource usage\", \"last_updated\": \"2025-01-05\"},\n        {\"id\": 5, \"name\": \"Incident Response Policy\", \"description\": \"Procedures for security incidents\", \"last_updated\": \"2025-02-28\"}\n    ]\n    \n    return {\"policies\": policies}\n", "reason": "Contains reusable logic or patterns", "detectors": ["regex:\\brules?\\b"], "risk_level": "high", "action": "review-security", "has_more_in_file": false, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:44.021855+00:00", "sig": "7fc70fbb526b807857efa5c57768c42182315bab"}
{"path": "test_plan.md", "sha1": "a1fa3f78dc7da5128a2308278da5e08e07f01ace", "category": "FEMA/Compliance", "tags": ["fema-compliance", "general", "code-logic"], "lines": [29, 41], "snippet": "- Enhanced Dashboard with Trend Analysis\n- External Systems Integration (FEMA, Document Management, Calendar)\n\n## 4. Test Cases\n\n### 4.1 Unit Tests\n\n#### Policy Matcher Component\n- Test file upload functionality\n- Test confidence threshold selection\n- Test algorithm selection\n- Test results display", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:44.029813+00:00", "sig": "8fea93f3cf473b997461bdfda2a350bdf09321e7"}
{"path": "test_plan.md", "sha1": "a1fa3f78dc7da5128a2308278da5e08e07f01ace", "category": "FEMA/Compliance", "tags": ["fema-compliance", "general", "code-logic"], "lines": [61, 73], "snippet": "#### External Systems Integration\n- Test FEMA database connection and search\n- Test document repository connections\n- Test calendar integration and event creation\n\n### 4.2 Integration Tests\n\n#### Policy Matcher with Backend\n- Test API communication for uploading documents\n- Test job status polling\n- Test results retrieval\n- Test report download", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:44.030507+00:00", "sig": "68aaa7b5e866f7e6590cbdb9b4ed5e2f832c6994"}
{"path": "test_plan.md", "sha1": "a1fa3f78dc7da5128a2308278da5e08e07f01ace", "category": "FEMA/Compliance", "tags": ["fema-compliance", "general", "code-logic"], "lines": [86, 98], "snippet": "- Test calendar events with compliance deadlines\n- Test FEMA data with reports generation\n\n### 4.3 End-to-End Tests\n\n#### Complete Policy Matching Workflow\n- Upload document\n- Process matching\n- View results\n- Generate report\n- Export report\n- Receive notification", "reason": "Contains FEMA compliance or policy logic", "detectors": ["regex:\\bFEMA\\b"], "risk_level": "medium", "action": "verify-policy", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:44.031167+00:00", "sig": "85aed2519e113fe819ae64d02328137e388ac89c"}
{"path": "test_plan.md", "sha1": "a1fa3f78dc7da5128a2308278da5e08e07f01ace", "category": "FEMA/Compliance", "tags": ["data-model", "general", "code-logic"], "lines": [56, 68], "snippet": "- Test notification display\n- Test notification rules creation\n- Test scheduled reports configuration\n- Test notification status updates\n\n#### External Systems Integration\n- Test FEMA database connection and search\n- Test document repository connections\n- Test calendar integration and event creation\n\n### 4.2 Integration Tests\n", "reason": "Configuration or mapping logic", "detectors": ["regex:\\brules?\\b"], "risk_level": "medium", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:44.033267+00:00", "sig": "efa94ad88c5a5f310eae185881658ae151bdaf40"}
{"path": "test_plan.md", "sha1": "a1fa3f78dc7da5128a2308278da5e08e07f01ace", "category": "Data Models", "tags": ["data-model", "data-structure", "general"], "lines": [155, 167], "snippet": "- Mobile responsiveness testing\n- Touch interface testing\n\n## 10. Test Documentation\n\n- Test results documentation\n- Issue tracking and resolution\n- Test coverage reporting\n- Performance metrics reporting\n\n## 11. Continuous Integration\n", "reason": "Defines data schemas or models", "detectors": ["regex:\\binterface\\b"], "risk_level": "low", "action": "port", "has_more_in_file": true, "discovery_mode": "full-scan", "ts_utc": "2025-08-10T03:43:44.034606+00:00", "sig": "48534f6b979c629074040704302e682793866034"}
