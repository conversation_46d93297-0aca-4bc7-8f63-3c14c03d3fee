{".gitignore": {"path": ".giti<PERSON>re", "sha1": "7904c5b21589f7a01ccc0bd4f626a8ea63424da7", "bytes": 772, "is_binary": false, "scanned_lines": 0, "nuggets": 0, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.543358+00:00", "last_scan_utc": "2025-08-10T03:43:43.543365+00:00"}, "DEPLOYMENT_GUIDE.md": {"path": "DEPLOYMENT_GUIDE.md", "sha1": "4ce3dca5e8a8f0352bff697f962ef274cffe78f1", "bytes": 12716, "is_binary": false, "scanned_lines": 521, "nuggets": 7, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.543440+00:00", "last_scan_utc": "2025-08-10T03:43:43.543444+00:00"}, "DataVisualization.tsx": {"path": "DataVisualization.tsx", "sha1": "88df91b1b91ee282307a8de937d509b408d81a82", "bytes": 13243, "is_binary": false, "scanned_lines": 341, "nuggets": 0, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.570610+00:00", "last_scan_utc": "2025-08-10T03:43:43.570617+00:00"}, "Documents.tsx": {"path": "Documents.tsx", "sha1": "8ac9a5b3973e7d1b23dad3f9298c949ef61ed9e7", "bytes": 9068, "is_binary": false, "scanned_lines": 263, "nuggets": 3, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.591811+00:00", "last_scan_utc": "2025-08-10T03:43:43.591822+00:00"}, "EnhancedDashboard.tsx": {"path": "EnhancedDashboard.tsx", "sha1": "29ee0c9575ef05a807be0caa780e7401b3db4b80", "bytes": 24355, "is_binary": false, "scanned_lines": 629, "nuggets": 1, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.608614+00:00", "last_scan_utc": "2025-08-10T03:43:43.608631+00:00"}, "EnhancedPolicyMatcher.tsx": {"path": "EnhancedPolicyMatcher.tsx", "sha1": "8c5a25ee4c81d12fc8128ab586fb9319feec2c19", "bytes": 22713, "is_binary": false, "scanned_lines": 703, "nuggets": 3, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.649192+00:00", "last_scan_utc": "2025-08-10T03:43:43.649201+00:00"}, "ExportFunctionality.tsx": {"path": "ExportFunctionality.tsx", "sha1": "6c5f8967662f92adab8c013856c06db618edfa52", "bytes": 17059, "is_binary": false, "scanned_lines": 482, "nuggets": 3, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.686821+00:00", "last_scan_utc": "2025-08-10T03:43:43.686828+00:00"}, "ExternalSystemsIntegration.tsx": {"path": "ExternalSystemsIntegration.tsx", "sha1": "104dbbe51affa8144ac1bdabe786f168ae4ded41", "bytes": 36463, "is_binary": false, "scanned_lines": 937, "nuggets": 10, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.716226+00:00", "last_scan_utc": "2025-08-10T03:43:43.716246+00:00"}, "Login.tsx": {"path": "Login.tsx", "sha1": "45b3cbb69df092239897e264bab7c2b12e331b96", "bytes": 5307, "is_binary": false, "scanned_lines": 188, "nuggets": 0, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.729496+00:00", "last_scan_utc": "2025-08-10T03:43:43.729517+00:00"}, "NotificationSystem.tsx": {"path": "NotificationSystem.tsx", "sha1": "f7581da5c1e870cf3b8d8a7f6b5bb787a3f567b6", "bytes": 24608, "is_binary": false, "scanned_lines": 721, "nuggets": 10, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.739341+00:00", "last_scan_utc": "2025-08-10T03:43:43.739350+00:00"}, "Profile.tsx": {"path": "Profile.tsx", "sha1": "47fd83ae47e3fb63269579dbb030d0dc1cd21e3f", "bytes": 16709, "is_binary": false, "scanned_lines": 498, "nuggets": 1, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.766351+00:00", "last_scan_utc": "2025-08-10T03:43:43.766358+00:00"}, "README.md": {"path": "README.md", "sha1": "ebc0392835a5d9866c03bc2dcff3fcf01ad333d5", "bytes": 4755, "is_binary": false, "scanned_lines": 144, "nuggets": 1, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.795357+00:00", "last_scan_utc": "2025-08-10T03:43:43.795378+00:00"}, "Register.tsx": {"path": "Register.tsx", "sha1": "855c6561a506a5d583f7b51d34ffd38a163e9bf2", "bytes": 11433, "is_binary": false, "scanned_lines": 378, "nuggets": 0, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.803905+00:00", "last_scan_utc": "2025-08-10T03:43:43.803922+00:00"}, "Reports.tsx": {"path": "Reports.tsx", "sha1": "a9270713738ef2ee9836e01e4f722eb9c0ea886b", "bytes": 13955, "is_binary": false, "scanned_lines": 380, "nuggets": 4, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.821828+00:00", "last_scan_utc": "2025-08-10T03:43:43.821835+00:00"}, "USER_GUIDE.md": {"path": "USER_GUIDE.md", "sha1": "332792ee6d1754ef0b504deab8664948884d6532", "bytes": 8779, "is_binary": false, "scanned_lines": 294, "nuggets": 10, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.848546+00:00", "last_scan_utc": "2025-08-10T03:43:43.848566+00:00"}, "api_integration.py": {"path": "api_integration.py", "sha1": "c4e9ddb4cd617697f3aa5da5e1b779b59579ecb4", "bytes": 15131, "is_binary": false, "scanned_lines": 468, "nuggets": 10, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.876192+00:00", "last_scan_utc": "2025-08-10T03:43:43.876199+00:00"}, "api_tests.py": {"path": "api_tests.py", "sha1": "5509931df7a3f831ad901c64eef829744bfdf8ee", "bytes": 9335, "is_binary": false, "scanned_lines": 284, "nuggets": 0, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.904977+00:00", "last_scan_utc": "2025-08-10T03:43:43.904996+00:00"}, "code_analysis.md": {"path": "code_analysis.md", "sha1": "19d82dc53dd7b0cb5b0b27c53e1a5326d179aa83", "bytes": 7195, "is_binary": false, "scanned_lines": 189, "nuggets": 0, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.920580+00:00", "last_scan_utc": "2025-08-10T03:43:43.920590+00:00"}, "component_tests.js": {"path": "component_tests.js", "sha1": "fcbd0993edadcf09c3bf5c9dcf3c838c1fa7704c", "bytes": 7574, "is_binary": false, "scanned_lines": 228, "nuggets": 2, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.931704+00:00", "last_scan_utc": "2025-08-10T03:43:43.931711+00:00"}, "database_integration.py": {"path": "database_integration.py", "sha1": "2642e67d50f07f6cf42ae9ceed1d5e3dabaf4c8e", "bytes": 11582, "is_binary": false, "scanned_lines": 349, "nuggets": 6, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.944453+00:00", "last_scan_utc": "2025-08-10T03:43:43.944464+00:00"}, "end_to_end_tests.py": {"path": "end_to_end_tests.py", "sha1": "c9d65fed689f4b93dd20b6748ed487b540b1699d", "bytes": 14378, "is_binary": false, "scanned_lines": 348, "nuggets": 10, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.968855+00:00", "last_scan_utc": "2025-08-10T03:43:43.968871+00:00"}, "frontend_integration.tsx": {"path": "frontend_integration.tsx", "sha1": "694b46c0ec39446f390d8309667c222d25d8c78e", "bytes": 9066, "is_binary": false, "scanned_lines": 300, "nuggets": 2, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:43.990847+00:00", "last_scan_utc": "2025-08-10T03:43:43.990854+00:00"}, "policyMatcherService.ts": {"path": "policyMatcherService.ts", "sha1": "835955a9cd6797edcbb6255148749c68ae3bdfc1", "bytes": 2279, "is_binary": false, "scanned_lines": 84, "nuggets": 0, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:44.006026+00:00", "last_scan_utc": "2025-08-10T03:43:44.006043+00:00"}, "policy_matcher_api.py": {"path": "policy_matcher_api.py", "sha1": "c303b90461211785cdfa8054c7264bc5bace0a83", "bytes": 10700, "is_binary": false, "scanned_lines": 309, "nuggets": 2, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:44.010259+00:00", "last_scan_utc": "2025-08-10T03:43:44.010265+00:00"}, "test_plan.md": {"path": "test_plan.md", "sha1": "a1fa3f78dc7da5128a2308278da5e08e07f01ace", "bytes": 4540, "is_binary": false, "scanned_lines": 180, "nuggets": 5, "has_more_in_file": false, "first_seen_utc": "2025-08-10T03:43:44.028780+00:00", "last_scan_utc": "2025-08-10T03:43:44.028799+00:00"}}