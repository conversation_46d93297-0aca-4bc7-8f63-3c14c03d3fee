#!/usr/bin/env python3
"""
Phase 1D Roll-Up — Aggregate Nuggets & Coverage
Read-only from SCAN_BUCKET, deterministic, resume-safe, append-only writes
"""

import os
import sys
import json
import csv
import time
from datetime import datetime
from typing import List, Dict, Any

# Configuration
SCAN_BUCKET = r"C:\Users\<USER>\Documents\repo analysis 202508\SCANS_OUT"
OUTPUT_DIR = os.path.join(SCAN_BUCKET, 'ROLLUPS')
FINAL_DEST_ROOT = r"C:\Users\<USER>\Documents\ComplianceMax_Rebuild_2025"  # do not write here

# Required files per phase1d folder
REQ_FILES = ['deep_nuggets.jsonl', 'full_scan_coverage.json', 'full_scan_manifest.csv']
OPT_FILES = ['zero_hit_report.md', 'detector_expansion.md']

# Output files
ROLLUP_SUMMARY_MD = os.path.join(OUTPUT_DIR, 'rollup_summary.md')
ROLLUP_SUMMARY_JSON = os.path.join(OUTPUT_DIR, 'rollup_summary.json')
SAVE_CANDIDATES_CSV = os.path.join(OUTPUT_DIR, 'save_candidates.csv')
ROLLUP_WARNINGS_MD = os.path.join(OUTPUT_DIR, 'rollup_warnings.md')
ROLLUP_PROGRESS_LOG = os.path.join(OUTPUT_DIR, 'rollup_progress.log')

# Scoring weights for nugget prioritization
RISK_WEIGHTS = {'high': 3, 'medium': 2, 'low': 1}
CATEGORY_WEIGHTS = {
    'Authentication/Security': 3,
    'FEMA/Compliance': 2,
    'Data Models': 1,
    'Backend Logic': 1,
    'Frontend/UI': 1,
    'Wizard/Workflow': 2,
    'Docs/Guides': 1,
    'Config/Data Assets': 1,
    'Other': 0
}

def now_ts() -> str:
    """Return current UTC timestamp in ISO format"""
    return datetime.utcnow().isoformat(timespec='seconds') + 'Z'

def append_log(line: str) -> None:
    """Append line to progress log"""
    with open(ROLLUP_PROGRESS_LOG, 'a', encoding='utf-8') as f:
        f.write(f"{line}\n")

def atomic_write(path: str, content: str) -> None:
    """Atomic write using temp file + rename"""
    tmp = path + '.tmp'
    with open(tmp, 'w', encoding='utf-8', newline='') as f:
        f.write(content)
    os.replace(tmp, path)

def discover_phase1d_folders() -> List[str]:
    """Discover all phase1d folders under SCAN_BUCKET in deterministic order"""
    phase_dirs = []
    for root, dirs, files in os.walk(SCAN_BUCKET):
        # Skip ROLLUPS directory to maintain read discipline
        if os.path.abspath(root) == os.path.abspath(OUTPUT_DIR):
            dirs[:] = []
            continue
        
        # Sort directories for deterministic traversal
        dirs.sort()
        
        if os.path.basename(root) == 'phase1d':
            phase_dirs.append(root)
    
    # Sort all discovered paths for deterministic processing
    phase_dirs.sort(key=lambda p: p.lower())
    return phase_dirs

def load_manifest_count(manifest_path: str) -> int:
    """Load manifest CSV and return count of data rows (excluding header)"""
    try:
        with open(manifest_path, 'r', encoding='utf-8', newline='') as f:
            reader = csv.reader(f)
            header = next(reader, None)  # Skip header
            count = 0
            for row in reader:
                if row:  # Non-empty row
                    count += 1
            return count
    except Exception as e:
        raise Exception(f"Error reading manifest: {e}")

def load_coverage_data(coverage_path: str) -> List[Dict[str, Any]]:
    """Load coverage JSON and return list of entries"""
    try:
        with open(coverage_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if isinstance(data, list):
            return data
        elif isinstance(data, dict):
            return list(data.values())
        else:
            return []
    except Exception as e:
        raise Exception(f"Error reading coverage: {e}")

def load_nuggets(nuggets_path: str) -> List[Dict[str, Any]]:
    """Load nuggets from JSONL file"""
    nuggets = []
    try:
        with open(nuggets_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                try:
                    obj = json.loads(line)
                    if isinstance(obj, dict):
                        nuggets.append(obj)
                except json.JSONDecodeError as e:
                    # Log but continue processing
                    append_log(f"Warning: Invalid JSON at line {line_num} in {nuggets_path}: {e}")
                    continue
    except Exception as e:
        raise Exception(f"Error reading nuggets: {e}")
    
    return nuggets

def count_zero_hits(zero_hit_path: str) -> int:
    """Count zero-hit entries from markdown file"""
    if not os.path.exists(zero_hit_path):
        return 0
    
    try:
        with open(zero_hit_path, 'r', encoding='utf-8') as f:
            content = f.read()
        # Count lines that start with "- " (list items)
        return len([line for line in content.splitlines() if line.strip().startswith('- ')])
    except Exception:
        return 0

def calculate_nugget_score(nugget: Dict[str, Any]) -> int:
    """Calculate priority score for a nugget"""
    risk_level = nugget.get('risk_level', 'low')
    category = nugget.get('category', 'Other')
    detectors = nugget.get('detectors', [])
    
    risk_score = RISK_WEIGHTS.get(risk_level, 1)
    category_score = CATEGORY_WEIGHTS.get(category, 0)
    detector_score = len(detectors) if isinstance(detectors, list) else 0
    
    return risk_score * 10 + category_score * 5 + detector_score

def initialize_outputs() -> None:
    """Initialize output files if they don't exist"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    if not os.path.exists(ROLLUP_SUMMARY_MD):
        atomic_write(ROLLUP_SUMMARY_MD, f"""# Phase 1D Roll-Up Summary
Initialized {now_ts()}

""")
    
    if not os.path.exists(ROLLUP_WARNINGS_MD):
        atomic_write(ROLLUP_WARNINGS_MD, f"""# Roll-Up Warnings
Initialized {now_ts()}

""")
    
    # Ensure CSV header exists
    if not os.path.exists(SAVE_CANDIDATES_CSV):
        with open(SAVE_CANDIDATES_CSV, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f, lineterminator='\n')
            writer.writerow(['repo', 'path', 'start', 'end', 'category', 'tags', 'action', 'reason'])

def process_repo_folder(phase_dir: str) -> Dict[str, Any]:
    """Process a single phase1d folder and return stats"""
    repo = os.path.basename(os.path.dirname(phase_dir))
    
    # Check required files
    missing_files = []
    for req_file in REQ_FILES:
        if not os.path.exists(os.path.join(phase_dir, req_file)):
            missing_files.append(req_file)
    
    if missing_files:
        raise Exception(f"Missing required files: {', '.join(missing_files)}")
    
    # Load and validate manifest vs coverage
    manifest_path = os.path.join(phase_dir, 'full_scan_manifest.csv')
    coverage_path = os.path.join(phase_dir, 'full_scan_coverage.json')
    
    manifest_count = load_manifest_count(manifest_path)
    coverage_entries = load_coverage_data(coverage_path)
    coverage_count = len(coverage_entries)
    
    if coverage_count != manifest_count:
        # Try to identify missing paths for better diagnostics
        try:
            manifest_paths = []
            with open(manifest_path, 'r', encoding='utf-8', newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row.get('path'):
                        manifest_paths.append(row['path'])
            
            coverage_paths = set()
            for entry in coverage_entries:
                if isinstance(entry, dict) and entry.get('path'):
                    coverage_paths.add(entry['path'])
            
            missing = [p for p in manifest_paths if p not in coverage_paths][:10]
            raise Exception(f"Coverage({coverage_count}) != Manifest({manifest_count}); missing up to 10: {missing}")
        except Exception as inner_e:
            raise Exception(f"Coverage({coverage_count}) != Manifest({manifest_count}); details: {inner_e}")
    
    # Load nuggets
    nuggets_path = os.path.join(phase_dir, 'deep_nuggets.jsonl')
    nuggets = load_nuggets(nuggets_path)
    
    # Count zero hits
    zero_hit_path = os.path.join(phase_dir, 'zero_hit_report.md')
    zero_hits = count_zero_hits(zero_hit_path)
    
    # Check detector expansion
    detector_expansion_path = os.path.join(phase_dir, 'detector_expansion.md')
    has_detector_expansion = os.path.exists(detector_expansion_path)
    
    return {
        'repo': repo,
        'manifest': manifest_count,
        'coverage': coverage_count,
        'nuggets': nuggets,
        'nugget_count': len(nuggets),
        'zero_hits': zero_hits,
        'detector_expansion': has_detector_expansion
    }

def main():
    """Main rollup processing function"""
    print(f"Starting Phase 1D Roll-Up at {now_ts()}")
    
    # Initialize outputs
    initialize_outputs()
    
    # Discover phase1d folders
    phase_dirs = discover_phase1d_folders()
    append_log(f"time={now_ts()} phase=rollup discovered_phase1d={len(phase_dirs)} start")
    
    if not phase_dirs:
        print("No phase1d folders found under SCAN_BUCKET")
        return
    
    # Process each folder
    repo_stats = []
    all_candidates = []
    warnings = []
    
    for phase_dir in phase_dirs:
        repo = os.path.basename(os.path.dirname(phase_dir))
        
        try:
            result = process_repo_folder(phase_dir)
            repo_stats.append({
                'repo': result['repo'],
                'manifest': result['manifest'],
                'coverage': result['coverage'],
                'nuggets': result['nugget_count'],
                'zero_hits': result['zero_hits'],
                'detector_expansion': result['detector_expansion']
            })
            
            # Process nuggets into candidates
            for nugget in result['nuggets']:
                lines = nugget.get('lines', [])
                if not (isinstance(lines, list) and len(lines) == 2):
                    continue
                
                start, end = lines
                candidate = {
                    'repo': repo,
                    'path': nugget.get('path', ''),
                    'start': start,
                    'end': end,
                    'category': nugget.get('category', 'Other'),
                    'tags': '|'.join(nugget.get('tags', [])),
                    'action': nugget.get('action', ''),
                    'reason': nugget.get('reason', ''),
                    '_score': calculate_nugget_score(nugget)
                }
                all_candidates.append(candidate)
            
            append_log(f"time={now_ts()} repo={repo} gate=PASS nuggets={result['nugget_count']} zero_hits={result['zero_hits']}")
            
        except Exception as e:
            warning = f"- {repo}: {str(e)}"
            warnings.append(warning)
            append_log(f"time={now_ts()} repo={repo} gate=FAIL error={str(e)}")
    
    # Write warnings
    if warnings:
        with open(ROLLUP_WARNINGS_MD, 'a', encoding='utf-8') as f:
            f.write('\n'.join(warnings) + '\n')
    
    # Sort candidates by score (descending), then by repo/path for deterministic ordering
    all_candidates.sort(key=lambda c: (-c['_score'], c['repo'].lower(), c['path'].lower(), c['start'], c['end']))
    
    # Get top 20
    top20 = all_candidates[:20]
    
    # Append to CSV
    if top20:
        with open(SAVE_CANDIDATES_CSV, 'a', encoding='utf-8', newline='') as f:
            writer = csv.writer(f, lineterminator='\n')
            for candidate in top20:
                writer.writerow([
                    candidate['repo'], candidate['path'], candidate['start'], candidate['end'],
                    candidate['category'], candidate['tags'], candidate['action'], candidate['reason']
                ])
    
    # Build summary
    summary_lines = [
        f"## Roll-Up Run ({now_ts()})",
        f"- phase1d folders discovered: {len(phase_dirs)}",
        f"- repos gated (pass): {len(repo_stats)}",
        f"- total candidates: {len(all_candidates)}",
        "",
        "### Top 20 Candidates"
    ]
    
    if not top20:
        summary_lines.append("(none)")
    else:
        for i, candidate in enumerate(top20, 1):
            summary_lines.append(
                f"{i:02d}. {candidate['repo']} — {candidate['path']} "
                f"[{candidate['start']},{candidate['end']}] — {candidate['action']} — "
                f"{candidate['category']} — tags: {candidate['tags']}"
            )
    
    summary_lines.append("")
    
    # Append to markdown summary
    with open(ROLLUP_SUMMARY_MD, 'a', encoding='utf-8') as f:
        f.write('\n' + '\n'.join(summary_lines) + '\n')
    
    # Load existing JSON summary and append new run
    summary_runs = []
    if os.path.exists(ROLLUP_SUMMARY_JSON):
        try:
            with open(ROLLUP_SUMMARY_JSON, 'r', encoding='utf-8') as f:
                summary_runs = json.load(f)
            if not isinstance(summary_runs, list):
                summary_runs = []
        except Exception:
            summary_runs = []
    
    # Create new run entry
    run_entry = {
        'ts_utc': now_ts(),
        'discovered_phase1d': len(phase_dirs),
        'repos_passed': len(repo_stats),
        'repos': repo_stats,
        'top20': [{k: v for k, v in c.items() if k != '_score'} for c in top20]
    }
    
    summary_runs.append(run_entry)
    atomic_write(ROLLUP_SUMMARY_JSON, json.dumps(summary_runs, ensure_ascii=False, indent=2))
    
    # Final log
    append_log(f"time={now_ts()} phase=rollup complete repos_passed={len(repo_stats)} candidates={len(all_candidates)}")
    
    print(f"ROLLUP COMPLETED repos_passed={len(repo_stats)} candidates={len(all_candidates)} top20={len(top20)}")

if __name__ == "__main__":
    main()
