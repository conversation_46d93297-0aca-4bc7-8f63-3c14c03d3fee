#!/usr/bin/env python3
"""
Phase 1A — <PERSON><PERSON><PERSON> with Tagging
Batch-safe, resume-safe, append-only scanning with deterministic file tagging
"""

import os
import sys
import json
import time
import re
from datetime import datetime
from typing import List, Dict, Any, Tuple, Optional

# Configuration
SOURCE_DIR = r"C:\Users\<USER>\Documents\SalvageControlSystem"
OUTPUT_DIR = r"C:\Users\<USER>\Documents\repo analysis 202508\SCANS_OUT\SalvageControlSystem\phase1a"

# Output files
SUPER_REPO_MD = os.path.join(OUTPUT_DIR, 'super_repo_initial_scan.md')
SUPER_REPO_JSONL = os.path.join(OUTPUT_DIR, 'super_repo_initial_scan.jsonl')
TAG_MANIFEST_MD = os.path.join(OUTPUT_DIR, 'tag_manifest.md')
PROGRESS_LOG = os.path.join(OUTPUT_DIR, 'super_repo_scan_progress.log')
STATE_JSON = os.path.join(OUTPUT_DIR, 'state.json')

# Batch configuration
CHECKPOINT_FILES = 50
CHECKPOINT_SECONDS = 120
MIN_TAGS_PER_ENTRY = 2

def now_ts() -> str:
    """Return current UTC timestamp"""
    return datetime.utcnow().isoformat(timespec='seconds') + 'Z'

def as_long_path(p: str) -> str:
    """Convert to long path format for Windows"""
    ap = os.path.abspath(p)
    if ap.startswith('\\\\?\\'):
        return ap
    return "\\\\\\\\?\\" + ap

def append_log(line: str) -> None:
    """Append line to progress log"""
    with open(PROGRESS_LOG, 'a', encoding='utf-8') as f:
        f.write(f"{line}\n")

def atomic_write(path: str, content: str) -> None:
    """Atomic write using temp file + rename"""
    tmp = path + '.tmp'
    with open(tmp, 'w', encoding='utf-8', newline='') as f:
        f.write(content)
    os.replace(tmp, path)

def append_jsonl(path: str, obj: Dict[str, Any]) -> None:
    """Append JSON object to JSONL file"""
    with open(path, 'a', encoding='utf-8') as f:
        f.write(json.dumps(obj, ensure_ascii=False) + "\n")

def get_file_size(file_path: str) -> int:
    """Get file size safely"""
    try:
        return os.path.getsize(as_long_path(file_path))
    except Exception:
        return -1

def determine_role(path: str, content_sample: str = "") -> str:
    """Determine role tag based on path and content"""
    path_lower = path.lower()
    
    # Frontend patterns
    if any(ext in path_lower for ext in ['.tsx', '.jsx', '.vue', '.svelte', '.html', '.css', '.scss']):
        return 'frontend'
    if any(pattern in path_lower for pattern in ['frontend', 'client', 'ui', 'components', 'pages']):
        return 'frontend'
    
    # Backend patterns
    if any(ext in path_lower for ext in ['.py', '.java', '.cs', '.rb', '.go', '.php']):
        return 'backend'
    if any(pattern in path_lower for pattern in ['backend', 'server', 'api', 'service', 'controller']):
        return 'backend'
    
    # Config patterns
    if any(ext in path_lower for ext in ['.json', '.yaml', '.yml', '.toml', '.ini', '.conf', '.cfg']):
        return 'config'
    if any(pattern in path_lower for pattern in ['config', 'settings', 'env']):
        return 'config'
    
    # Data model patterns
    if any(ext in path_lower for ext in ['.sql', '.prisma']):
        return 'data-model'
    if any(pattern in path_lower for pattern in ['model', 'schema', 'migration', 'database']):
        return 'data-model'
    
    # Security patterns
    if any(pattern in path_lower for pattern in ['auth', 'security', 'jwt', 'oauth', 'crypto']):
        return 'security'
    
    # Workflow patterns
    if any(pattern in path_lower for pattern in ['workflow', 'wizard', 'step', 'process']):
        return 'workflow'
    
    # Infrastructure patterns
    if any(pattern in path_lower for pattern in ['docker', 'k8s', 'kubernetes', 'terraform', 'ansible']):
        return 'infra'
    
    # Documentation patterns
    if any(ext in path_lower for ext in ['.md', '.txt', '.rst', '.adoc']):
        return 'docs'
    
    return 'backend'  # default

def determine_domain(path: str, content_sample: str = "") -> str:
    """Determine domain tag based on path and content"""
    path_lower = path.lower()
    content_lower = content_sample.lower()
    
    # FEMA patterns
    if any(pattern in path_lower for pattern in ['fema', 'public-assistance', 'pa-check']):
        return 'fema-pa'
    if any(pattern in path_lower for pattern in ['individual-assistance', 'ia']):
        return 'fema-ia'
    
    # DRRA patterns
    if any(pattern in path_lower for pattern in ['drra', '1235b']):
        return 'drra-1235b'
    
    # Category patterns
    for cat in ['category-a', 'category-b', 'category-c', 'category-d', 'category-e', 'category-f', 'category-g']:
        if cat.replace('-', '') in path_lower or cat in path_lower:
            return cat
    
    # Other FEMA domains
    if any(pattern in path_lower for pattern in ['ehp', 'environmental']):
        return 'ehp'
    if any(pattern in path_lower for pattern in ['bca', 'benefit-cost']):
        return 'bca'
    if any(pattern in path_lower for pattern in ['procurement', 'contract']):
        return 'procurement'
    if any(pattern in path_lower for pattern in ['policy', 'compliance']):
        return 'policy'
    
    # Content-based detection
    if any(term in content_lower for term in ['pappg', 'public assistance', 'fema', 'category']):
        return 'fema-pa'
    
    return 'policy'  # default domain

def determine_tech_format(path: str) -> str:
    """Determine tech/format tag based on file extension and path"""
    ext = os.path.splitext(path)[1].lower()
    path_lower = path.lower()
    
    # Direct extension mapping
    ext_map = {
        '.ts': 'ts', '.tsx': 'tsx', '.js': 'js', '.jsx': 'jsx',
        '.py': 'py', '.java': 'java', '.cs': 'cs', '.rb': 'rb', '.go': 'go',
        '.json': 'json', '.yaml': 'yaml', '.yml': 'yaml',
        '.sql': 'sql', '.md': 'md', '.html': 'html', '.css': 'css',
        '.toml': 'toml', '.ini': 'ini', '.conf': 'conf', '.cfg': 'cfg'
    }
    
    if ext in ext_map:
        base_tech = ext_map[ext]
    else:
        base_tech = 'other'
    
    # Special framework/library detection
    if 'prisma' in path_lower or 'schema.prisma' in path_lower:
        return 'prisma'
    if 'openapi' in path_lower or 'swagger' in path_lower:
        return 'openapi'
    if 'pydantic' in path_lower:
        return 'pydantic'
    if 'zod' in path_lower:
        return 'zod'
    
    return base_tech

def determine_decision(path: str, size: int, role: str, domain: str) -> str:
    """Determine keep/rewrite/archive decision"""
    path_lower = path.lower()
    
    # Archive candidates
    if any(pattern in path_lower for pattern in [
        'node_modules', '.git', 'dist', 'build', '__pycache__', '.venv',
        'temp', 'tmp', 'cache', 'log', '.old', '.bak'
    ]):
        return 'archive'
    
    # Keep high-value items
    if role in ['security', 'data-model', 'workflow']:
        return 'keep'
    if domain in ['fema-pa', 'fema-ia', 'drra-1235b']:
        return 'keep'
    if any(pattern in path_lower for pattern in ['schema', 'model', 'auth', 'wizard']):
        return 'keep'
    
    # Rewrite candidates (old or problematic code)
    if any(pattern in path_lower for pattern in ['.old', 'legacy', 'deprecated', 'todo']):
        return 'rewrite'
    if size > 10000 and role == 'backend':  # Large backend files might need refactoring
        return 'rewrite'
    
    return 'keep'  # default

def should_emit_entry(path: str, size: int) -> bool:
    """Decide whether to emit an entry for this file"""
    path_lower = path.lower()
    
    # Skip binary files that are not useful
    if any(ext in path_lower for ext in ['.exe', '.dll', '.so', '.dylib', '.bin']):
        return False
    
    # Skip very large files unless they're important
    if size > 50 * 1024 * 1024:  # 50MB
        return False
    
    # Skip hidden/system files unless they're config
    if path_lower.startswith('.') and not any(name in path_lower for name in ['.env', '.config', '.gitignore']):
        return False
    
    return True

def read_content_sample(file_path: str, max_bytes: int = 1024) -> str:
    """Read a small sample of file content for analysis"""
    try:
        with open(as_long_path(file_path), 'r', encoding='utf-8', errors='ignore') as f:
            return f.read(max_bytes)
    except Exception:
        return ""

def create_file_entry(abs_path: str, rel_path: str) -> Optional[Dict[str, Any]]:
    """Create a tagged entry for a file"""
    try:
        size = get_file_size(abs_path)
        if not should_emit_entry(rel_path, size):
            return None
        
        # Read content sample for better tagging
        content_sample = read_content_sample(abs_path)
        
        # Determine tags
        role = determine_role(rel_path, content_sample)
        domain = determine_domain(rel_path, content_sample)
        tech = determine_tech_format(rel_path)
        decision = determine_decision(rel_path, size, role, domain)
        
        # Ensure minimum tags
        tags = [role, domain, tech, decision]
        # Remove duplicates while preserving order
        unique_tags = []
        for tag in tags:
            if tag not in unique_tags:
                unique_tags.append(tag)
        
        # Add extra tags if needed to meet minimum
        if len(unique_tags) < MIN_TAGS_PER_ENTRY:
            if 'important' not in unique_tags:
                unique_tags.append('important')
        
        # Create brief note
        note = f"{role.title()} {tech} file"
        if domain != 'policy':
            note += f" for {domain}"
        if decision != 'keep':
            note += f" (recommend {decision})"
        
        # Determine kind
        if os.path.isdir(abs_path):
            kind = 'directory'
        elif any(ext in rel_path.lower() for ext in ['.py', '.js', '.ts', '.java', '.cs']):
            kind = 'source'
        elif any(ext in rel_path.lower() for ext in ['.json', '.yaml', '.yml', '.toml']):
            kind = 'config'
        elif any(ext in rel_path.lower() for ext in ['.md', '.txt', '.rst']):
            kind = 'docs'
        else:
            kind = 'file'
        
        return {
            'path': rel_path.replace('\\', '/'),
            'size': size,
            'kind': kind,
            'tags': unique_tags,
            'note': note,
            'ts_utc': now_ts()
        }
    
    except Exception as e:
        append_log(f"Error processing {rel_path}: {e}")
        return None

def iter_files_deterministic(root_dir: str):
    """Iterate files in deterministic lexicographic order"""
    for root, dirs, files in os.walk(root_dir):
        # Sort for deterministic order
        dirs.sort()
        files.sort()
        
        # Yield files first
        for file in files:
            abs_path = os.path.join(root, file)
            rel_path = os.path.relpath(abs_path, root_dir)
            yield abs_path, rel_path
        
        # Then yield directories (for completeness)
        for dir_name in dirs:
            abs_path = os.path.join(root, dir_name)
            rel_path = os.path.relpath(abs_path, root_dir)
            yield abs_path, rel_path

def load_state() -> Dict[str, Any]:
    """Load resume state if it exists"""
    if os.path.exists(STATE_JSON):
        try:
            with open(STATE_JSON, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            pass
    
    return {
        'phase': '1a',
        'last_path': None,
        'processed': 0,
        'emitted': 0,
        'start_time': now_ts()
    }

def save_state(state: Dict[str, Any]) -> None:
    """Save current state"""
    atomic_write(STATE_JSON, json.dumps(state, ensure_ascii=False, indent=2))

def initialize_outputs() -> None:
    """Initialize output files if they don't exist"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Initialize markdown table if not exists
    if not os.path.exists(SUPER_REPO_MD):
        header = """# Super Repo Initial Scan

| Path | Size | Kind | Tags | Note |
|------|------|------|------|------|
"""
        atomic_write(SUPER_REPO_MD, header)

def update_tag_manifest(entries: List[Dict[str, Any]]) -> None:
    """Update tag manifest with current statistics"""
    tag_counts = {}
    kind_counts = {}
    decision_counts = {}
    
    # Count all tags and categories
    for entry in entries:
        # Count individual tags
        for tag in entry.get('tags', []):
            tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        # Count kinds
        kind = entry.get('kind', 'unknown')
        kind_counts[kind] = kind_counts.get(kind, 0) + 1
        
        # Count decisions
        tags = entry.get('tags', [])
        decision = next((tag for tag in tags if tag in ['keep', 'rewrite', 'archive']), 'unknown')
        decision_counts[decision] = decision_counts.get(decision, 0) + 1
    
    # Generate manifest content
    content = [
        f"# Tag Manifest",
        f"Generated: {now_ts()}",
        f"Total entries: {len(entries)}",
        "",
        "## Tag Distribution",
        ""
    ]
    
    for tag, count in sorted(tag_counts.items(), key=lambda x: -x[1]):
        content.append(f"- {tag}: {count}")
    
    content.extend([
        "",
        "## Kind Distribution",
        ""
    ])
    
    for kind, count in sorted(kind_counts.items(), key=lambda x: -x[1]):
        content.append(f"- {kind}: {count}")
    
    content.extend([
        "",
        "## Decision Distribution",
        ""
    ])
    
    for decision, count in sorted(decision_counts.items(), key=lambda x: -x[1]):
        content.append(f"- {decision}: {count}")
    
    atomic_write(TAG_MANIFEST_MD, '\n'.join(content) + '\n')

def main():
    """Main scanning function"""
    print(f"Starting Phase 1A Cursory Scan at {now_ts()}")
    
    # Initialize
    initialize_outputs()
    state = load_state()
    
    # Track all entries for tag manifest
    all_entries = []
    if os.path.exists(SUPER_REPO_JSONL):
        try:
            with open(SUPER_REPO_JSONL, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        all_entries.append(json.loads(line))
        except Exception:
            pass
    
    # Resume tracking
    last_path = state.get('last_path')
    processed = state.get('processed', 0)
    emitted = state.get('emitted', 0)
    skipping = last_path is not None
    
    files_this_batch = 0
    last_checkpoint = time.time()
    
    append_log(f"time={now_ts()} phase=1a start processed={processed} emitted={emitted} resume_from={last_path}")
    
    # Process files
    for abs_path, rel_path in iter_files_deterministic(SOURCE_DIR):
        # Skip until resume point
        if skipping:
            if rel_path <= last_path:
                continue
            else:
                skipping = False
        
        # Create entry
        entry = create_file_entry(abs_path, rel_path)
        processed += 1
        
        if entry:
            # Append to JSONL
            append_jsonl(SUPER_REPO_JSONL, entry)
            
            # Append to markdown table
            tags_str = ' | '.join(entry['tags'])
            md_row = f"| {entry['path']} | {entry['size']} | {entry['kind']} | {tags_str} | {entry['note']} |\n"
            with open(SUPER_REPO_MD, 'a', encoding='utf-8') as f:
                f.write(md_row)
            
            all_entries.append(entry)
            emitted += 1
        
        files_this_batch += 1
        
        # Checkpoint
        current_time = time.time()
        if (files_this_batch >= CHECKPOINT_FILES) or (current_time - last_checkpoint >= CHECKPOINT_SECONDS):
            state.update({
                'last_path': rel_path,
                'processed': processed,
                'emitted': emitted,
                'last_checkpoint': now_ts()
            })
            save_state(state)
            update_tag_manifest(all_entries)
            append_log(f"time={now_ts()} phase=1a checkpoint processed={processed} emitted={emitted} last_path={rel_path}")
            
            files_this_batch = 0
            last_checkpoint = current_time
    
    # Final checkpoint
    state.update({
        'last_path': 'COMPLETE',
        'processed': processed,
        'emitted': emitted,
        'completed': now_ts()
    })
    save_state(state)
    update_tag_manifest(all_entries)
    
    append_log(f"time={now_ts()} phase=1a complete processed={processed} emitted={emitted}")
    print(f"Phase 1A Complete: processed={processed} emitted={emitted}")

if __name__ == "__main__":
    main()
