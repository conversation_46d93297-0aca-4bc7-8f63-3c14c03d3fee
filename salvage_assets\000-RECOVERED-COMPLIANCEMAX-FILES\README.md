# Salvaged Files – 000-RECOVERED COMPLIANCEMAX FILES

**Date:** January 5, 2025  
**Operation:** Local Salvage Audit - Phase 1

## Overview

These files were removed from Git tracking because they exceeded the GitHub file size limit (typically 50MB) or were not appropriate for version control (e.g., binary archives, compressed logs, large JSON datasets).

## Salvaged Files Summary

### Large Text Files (1632.8 MB each)
- `api/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/api/script_content_results.txt`
- `asset/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/asset/script_content_results.txt`
- `backend/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/backend/script_content_results.txt`
- `client/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/client/script_content_results.txt`
- `component/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/component/script_content_results.txt`
- `config/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/config/script_content_results.txt`
- `csv/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/csv/script_content_results.txt`
- `dashboard/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/dashboard/script_content_results.txt`
- `doc/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/doc/script_content_results.txt`
- `endpoint/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/endpoint/script_content_results.txt`
- `file/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/file/script_content_results.txt`
- `form/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/form/script_content_results.txt`
- `found_scripts/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/found_scripts/script_content_results.txt`
- `helper/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/helper/script_content_results.txt`
- `index/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/index/script_content_results.txt`
- `intake/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/intake/script_content_results.txt`
- `json/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/json/script_content_results.txt`
- `landing/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/landing/script_content_results.txt`
- `layout/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/layout/script_content_results.txt`
- `md/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/md/script_content_results.txt`
- `node/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/node/script_content_results.txt`
- `onboarding/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/onboarding/script_content_results.txt`
- `page/script_content_results.txt` → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/page/script_content_results.txt`

### Archive Files
- `found_scripts/script_content_results.zip` (288.3 MB) → `salvage_assets/000-RECOVERED-COMPLIANCEMAX-FILES/found_scripts/script_content_results.zip`

### Large JSON Files (25-51 MB each)
Multiple compliance checklist JSON files across various directories including:
- Unified_Compliance_Checklist_Mapped_v2.xlsx.json (50.9 MB)
- Unified_Compliance_Checklist_FINAL_Cursor.xlsx.json (50.4 MB)
- Final_Compliance_Checklist_with_GROK_and_CFR_v2.xlsx.json (50.4 MB)
- Unified_Compliance_Checklist_With_PAPPG_Version.xlsx.json (50.1 MB)
- Updated_Compliance_Checklist.json (49.5 MB)
- Unified_Compliance_Checklist_FINAL.xlsx.json (48.9 MB)
- Unified_Compliance_Checklist_Cleaned.xlsx.json (48.7 MB)
- And many more compliance-related JSON files

### HTML Files
- Multiple `chat.html` files (31.3 MB each) across various directories

### Other Large Files
- Multiple `conversations.json` files (30.6 MB each)
- Multiple `LIST OF FILES.xlsx.json` files (30.5 MB each)
- Multiple `LIST OF ALL FILES IN APP.xlsx.json` files (29.2 MB each)

## Total Files Processed
Approximately 400+ files were moved to the salvage directory, preserving the original folder structure.

## Git Tracking Status
- Some files were previously Git-tracked and have been removed from tracking
- Most large files were not tracked by Git
- All salvaged files are now ignored via .gitignore patterns

## Next Steps
This operation is complete. GitHub release publishing will occur in a future phase.

## Special Notes
- All original folder structures have been preserved in the salvage directory
- File integrity maintained during the move operation
- Complete inventory available in: `C:\Users\<USER>\Documents\SalvageControlSystem\file_inventory_master.csv`
