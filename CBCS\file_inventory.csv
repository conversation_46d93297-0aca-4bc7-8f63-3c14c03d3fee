﻿original_path,new_path,ext,size_MB,note
"compliancemax_clean_new.rar","salvage_assets/compliancemax_clean_new.rar",".rar",3861.5,"moved to reduce Git push bloat"
"FEMA FACT SHEETS.rar","salvage_assets/FEMA FACT SHEETS.rar",".rar",768.9,"moved to reduce Git push bloat"
"frontend.zip","salvage_assets/frontend.zip",".zip",194.7,"moved to reduce Git push bloat"
"000-RECOVERED COMPLIANCEMAX FILES\api\chat.html","salvage_assets/000-RECOVERED COMPLIANCEMAX FILES\api\chat.html",".html",31.3,"moved to reduce Git push bloat"
"000-RECOVERED COMPLIANCEMAX FILES\api\Cleaned_Compliance_Checklist.xlsx.json","salvage_assets/000-RECOVERED COMPLIANCEMAX FILES\api\Cleaned_Compliance_Checklist.xlsx.json",".json",40.7,"moved to reduce Git push bloat"
"000-RECOVERED COMPLIANCEMAX FILES\api\Cleaned_Compliance_Checklist_Final.xlsx.json","salvage_assets/000-RECOVERED COMPLIANCEMAX FILES\api\Cleaned_Compliance_Checklist_Final.xlsx.json",".json",38.8,"moved to reduce Git push bloat"
"000-RECOVERED COMPLIANCEMAX FILES\api\Cleaned_Compliance_Checklist_W-PROCESS TREE-V1.xlsx.json","salvage_assets/000-RECOVERED COMPLIANCEMAX FILES\api\Cleaned_Compliance_Checklist_W-PROCESS TREE-V1.xlsx.json",".json",38.8,"moved to reduce Git push bloat"
"000-RECOVERED COMPLIANCEMAX FILES\api\Cleaned_Compliance_Checklist_W-PROCESS TREE-V2.xlsx.json","salvage_assets/000-RECOVERED COMPLIANCEMAX FILES\api\Cleaned_Compliance_Checklist_W-PROCESS TREE-V2.xlsx.json",".json",38.8,"moved to reduce Git push bloat"
"000-RECOVERED COMPLIANCEMAX FILES\api\Cleaned_Compliance_Checklist_W-PROCESS TREE.xlsx.json","salvage_assets/000-RECOVERED COMPLIANCEMAX FILES\api\Cleaned_Compliance_Checklist_W-PROCESS TREE.xlsx.json",".json",38.8,"moved to reduce Git push bloat"
"000-RECOVERED COMPLIANCEMAX FILES\api\Compliance_Checklist_Docling_Schema.json","salvage_assets/000-RECOVERED COMPLIANCEMAX FILES\api\Compliance_Checklist_Docling_Schema.json",".json",35.3,"moved to reduce Git push bloat"
"000-RECOVERED COMPLIANCEMAX FILES\api\conversations.json","salvage_assets/000-RECOVERED COMPLIANCEMAX FILES\api\conversations.json",".json",30.6,"moved to reduce Git push bloat"
"000-RECOVERED COMPLIANCEMAX FILES\api\Exported_Compliance_Checklist_Final.json","salvage_assets/000-RECOVERED COMPLIANCEMAX FILES\api\Exported_Compliance_Checklist_Final.json",".json",42.3,"moved to reduce Git push bloat"
"000-RECOVERED COMPLIANCEMAX FILES\api\Exported_Compliance_Checklist_Final.md","salvage_assets/000-RECOVERED COMPLIANCEMAX FILES\api\Exported_Compliance_Checklist_Final.md",".md",38.8,"moved to reduce Git push bloat"
"ALL NEW APP\core","salvage_assets/ALL NEW APP\core","",140.1,"moved to reduce Git push bloat"
"ALL NEW APP\zi8K3KP7","salvage_assets/ALL NEW APP\zi8K3KP7","",38.1,"moved to reduce Git push bloat"
"ALL NEW APP\.next\cache\webpack\client-production\0.pack","salvage_assets/ALL NEW APP\.next\cache\webpack\client-production\0.pack",".pack",42.2,"moved to reduce Git push bloat"
"ALL NEW APP\.next\cache\webpack\server-production\1.pack","salvage_assets/ALL NEW APP\.next\cache\webpack\server-production\1.pack",".pack",26.1,"moved to reduce Git push bloat"
"ALL NEW APP\backup\app\core","salvage_assets/ALL NEW APP\backup\app\core","",140.1,"moved to reduce Git push bloat"
"ALL NEW APP\clean-app\.next\cache\webpack\server-production\1.pack","salvage_assets/ALL NEW APP\clean-app\.next\cache\webpack\server-production\1.pack",".pack",36.3,"moved to reduce Git push bloat"
"ALL NEW APP\clean-app\node_modules\@next\swc-win32-x64-msvc\next-swc.win32-x64-msvc.node","salvage_assets/ALL NEW APP\clean-app\node_modules\@next\swc-win32-x64-msvc\next-swc.win32-x64-msvc.node",".node",122.6,"moved to reduce Git push bloat"
"ALL NEW APP\compliance_app_fixed\app\core","salvage_assets/ALL NEW APP\compliance_app_fixed\app\core","",140.1,"moved to reduce Git push bloat"
"ALL NEW APP\compliance_app_fixed\app\.next\cache\webpack\client-production\0.pack","salvage_assets/ALL NEW APP\compliance_app_fixed\app\.next\cache\webpack\client-production\0.pack",".pack",47.5,"moved to reduce Git push bloat"
"ALL NEW APP\compliance_app_fixed\app\.next\cache\webpack\server-production\0.pack","salvage_assets/ALL NEW APP\compliance_app_fixed\app\.next\cache\webpack\server-production\0.pack",".pack",33.5,"moved to reduce Git push bloat"
"ALL NEW APP\compliance_app_fixed\app\node_modules\@next\swc-linux-x64-gnu\next-swc.linux-x64-gnu.node","salvage_assets/ALL NEW APP\compliance_app_fixed\app\node_modules\@next\swc-linux-x64-gnu\next-swc.linux-x64-gnu.node",".node",125.3,"moved to reduce Git push bloat"
"ALL NEW APP\compliance_app_fixed\app\node_modules\@next\swc-linux-x64-musl\next-swc.linux-x64-musl.node","salvage_assets/ALL NEW APP\compliance_app_fixed\app\node_modules\@next\swc-linux-x64-musl\next-swc.linux-x64-musl.node",".node",149.5,"moved to reduce Git push bloat"
"ALL NEW APP\consolidated-app\node_modules\@next\swc-wasm-nodejs\wasm_bg.wasm","salvage_assets/ALL NEW APP\consolidated-app\node_modules\@next\swc-wasm-nodejs\wasm_bg.wasm",".wasm",44,"moved to reduce Git push bloat"
"ALL NEW APP\consolidated-app\node_modules\@next\swc-win32-x64-msvc\next-swc.win32-x64-msvc.node","salvage_assets/ALL NEW APP\consolidated-app\node_modules\@next\swc-win32-x64-msvc\next-swc.win32-x64-msvc.node",".node",122.6,"moved to reduce Git push bloat"
"ALL NEW APP\DOCS\compliance_app_fixed_complete.zip","salvage_assets/ALL NEW APP\DOCS\compliance_app_fixed_complete.zip",".zip",240.5,"moved to reduce Git push bloat"
"ALL NEW APP\DOCS\compliance_app_fixed_complete\home\ubuntu\compliance_app_fixed\app\core","salvage_assets/ALL NEW APP\DOCS\compliance_app_fixed_complete\home\ubuntu\compliance_app_fixed\app\core","",140.1,"moved to reduce Git push bloat"
"ALL NEW APP\DOCS\compliance_app_fixed_complete\home\ubuntu\compliance_app_fixed\app\.next\cache\webpack\client-production\0.pack","salvage_assets/ALL NEW APP\DOCS\compliance_app_fixed_complete\home\ubuntu\compliance_app_fixed\app\.next\cache\webpack\client-production\0.pack",".pack",47.5,"moved to reduce Git push bloat"
"ALL NEW APP\DOCS\compliance_app_fixed_complete\home\ubuntu\compliance_app_fixed\app\.next\cache\webpack\server-production\0.pack","salvage_assets/ALL NEW APP\DOCS\compliance_app_fixed_complete\home\ubuntu\compliance_app_fixed\app\.next\cache\webpack\server-production\0.pack",".pack",33.5,"moved to reduce Git push bloat"
"ALL NEW APP\DOCS\compliance_app_fixed_complete\home\ubuntu\compliance_app_fixed\app\node_modules\@next\swc-linux-x64-gnu\next-swc.linux-x64-gnu.node","salvage_assets/ALL NEW APP\DOCS\compliance_app_fixed_complete\home\ubuntu\compliance_app_fixed\app\node_modules\@next\swc-linux-x64-gnu\next-swc.linux-x64-gnu.node",".node",125.3,"moved to reduce Git push bloat"
"ALL NEW APP\DOCS\compliance_app_fixed_complete\home\ubuntu\compliance_app_fixed\app\node_modules\@next\swc-linux-x64-musl\next-swc.linux-x64-musl.node","salvage_assets/ALL NEW APP\DOCS\compliance_app_fixed_complete\home\ubuntu\compliance_app_fixed\app\node_modules\@next\swc-linux-x64-musl\next-swc.linux-x64-musl.node",".node",149.5,"moved to reduce Git push bloat"
