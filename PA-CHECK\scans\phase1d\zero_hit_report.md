# Zero Hit Report - Phase 1D

## Suspicious Files with No Nuggets

### app\core\logging_config.py

**Reason:** domain_relevant_path  
**Word Count:** 97  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\documents\models_old.py

**Reason:** domain_relevant_path  
**Word Count:** 189  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\documents\service_old.py

**Reason:** policy_document  
**Word Count:** 1774  
**Policy Keywords:** 3  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\core\logging_config.py

**Reason:** domain_relevant_path  
**Word Count:** 97  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\base.py

**Reason:** domain_relevant_path  
**Word Count:** 49  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\bca.py

**Reason:** domain_relevant_path  
**Word Count:** 320  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\cbcs.py

**Reason:** domain_relevant_path  
**Word Count:** 160  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\cbcs_scan.py

**Reason:** domain_relevant_path  
**Word Count:** 77  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\cost.py

**Reason:** domain_relevant_path  
**Word Count:** 159  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\document.py

**Reason:** domain_relevant_path  
**Word Count:** 189  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\document_version.py

**Reason:** domain_relevant_path  
**Word Count:** 227  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\ehp.py

**Reason:** domain_relevant_path  
**Word Count:** 185  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\flood_map.py

**Reason:** domain_relevant_path  
**Word Count:** 136  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\geo_location.py

**Reason:** domain_relevant_path  
**Word Count:** 95  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\history.py

**Reason:** domain_relevant_path  
**Word Count:** 107  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\mapping.py

**Reason:** domain_relevant_path  
**Word Count:** 128  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\policy.py

**Reason:** domain_relevant_path  
**Word Count:** 201  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\project.py

**Reason:** domain_relevant_path  
**Word Count:** 78  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\report.py

**Reason:** domain_relevant_path  
**Word Count:** 126  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\subscription.py

**Reason:** domain_relevant_path  
**Word Count:** 150  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\models\user.py

**Reason:** domain_relevant_path  
**Word Count:** 121  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\schemas\__init__.py

**Reason:** domain_relevant_path  
**Word Count:** 0  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\services\compliance_checker.py

**Reason:** domain_relevant_path  
**Word Count:** 652  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\services\document_service.py

**Reason:** policy_document  
**Word Count:** 1774  
**Policy Keywords:** 3  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\middleware_backup\services\policy_service.py

**Reason:** domain_relevant_path  
**Word Count:** 456  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\__init__.py

**Reason:** domain_relevant_path  
**Word Count:** 58  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\base.py

**Reason:** domain_relevant_path  
**Word Count:** 53  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\bca.py

**Reason:** domain_relevant_path  
**Word Count:** 320  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\cbcs.py

**Reason:** domain_relevant_path  
**Word Count:** 285  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\cbcs_scan.py

**Reason:** domain_relevant_path  
**Word Count:** 77  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\checklist.py

**Reason:** domain_relevant_path  
**Word Count:** 167  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\cost.py

**Reason:** domain_relevant_path  
**Word Count:** 159  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\ehp.py

**Reason:** domain_relevant_path  
**Word Count:** 185  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\enums.py

**Reason:** domain_relevant_path  
**Word Count:** 131  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\flood_map.py

**Reason:** domain_relevant_path  
**Word Count:** 136  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\geo_location.py

**Reason:** domain_relevant_path  
**Word Count:** 95  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\history.py

**Reason:** domain_relevant_path  
**Word Count:** 107  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\mapping.py

**Reason:** domain_relevant_path  
**Word Count:** 128  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\project.py

**Reason:** domain_relevant_path  
**Word Count:** 78  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\report.py

**Reason:** domain_relevant_path  
**Word Count:** 126  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\subscription.py

**Reason:** domain_relevant_path  
**Word Count:** 150  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\models\user.py

**Reason:** domain_relevant_path  
**Word Count:** 121  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\schemas\__init__.py

**Reason:** domain_relevant_path  
**Word Count:** 0  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\services\compliance_checker.py

**Reason:** domain_relevant_path  
**Word Count:** 652  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### app\services\policy_service.py

**Reason:** domain_relevant_path  
**Word Count:** 456  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\core\logging_config.py

**Reason:** domain_relevant_path  
**Word Count:** 97  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\base.py

**Reason:** domain_relevant_path  
**Word Count:** 49  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\bca.py

**Reason:** domain_relevant_path  
**Word Count:** 320  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\cbcs.py

**Reason:** domain_relevant_path  
**Word Count:** 160  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\cbcs_scan.py

**Reason:** domain_relevant_path  
**Word Count:** 77  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\compliance.py

**Reason:** domain_relevant_path  
**Word Count:** 391  
**Policy Keywords:** 3  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\cost.py

**Reason:** domain_relevant_path  
**Word Count:** 159  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\document.py

**Reason:** domain_relevant_path  
**Word Count:** 189  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\document_version.py

**Reason:** domain_relevant_path  
**Word Count:** 227  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\ehp.py

**Reason:** domain_relevant_path  
**Word Count:** 185  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\flood_map.py

**Reason:** domain_relevant_path  
**Word Count:** 136  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\geo_location.py

**Reason:** domain_relevant_path  
**Word Count:** 95  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\history.py

**Reason:** domain_relevant_path  
**Word Count:** 107  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\mapping.py

**Reason:** domain_relevant_path  
**Word Count:** 128  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\policy.py

**Reason:** domain_relevant_path  
**Word Count:** 201  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\project.py

**Reason:** domain_relevant_path  
**Word Count:** 78  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\report.py

**Reason:** domain_relevant_path  
**Word Count:** 126  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\subscription.py

**Reason:** domain_relevant_path  
**Word Count:** 150  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\backup_before_cleanup\app\models\user.py

**Reason:** domain_relevant_path  
**Word Count:** 121  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### backup_20250427_150828\compliancemax\config\__init__.py

**Reason:** domain_relevant_path  
**Word Count:** 0  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### check_users.py

**Reason:** domain_relevant_path  
**Word Count:** 40  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### config\development.py

**Reason:** domain_relevant_path  
**Word Count:** 151  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### config\monitoring.py

**Reason:** domain_relevant_path  
**Word Count:** 244  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### config\monitoring\grafana\dashboards\system_overview.json

**Reason:** domain_relevant_path  
**Word Count:** 682  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### config\test.py

**Reason:** domain_relevant_path  
**Word Count:** 257  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### core\compliance_checker.py

**Reason:** domain_relevant_path  
**Word Count:** 1035  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### core\policy_matcher.py

**Reason:** domain_relevant_path  
**Word Count:** 848  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### data\check_db.py

**Reason:** domain_relevant_path  
**Word Count:** 31  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### data\check_document_storage.py

**Reason:** domain_relevant_path  
**Word Count:** 347  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### deployment\dr\multi-region-config.yaml

**Reason:** domain_relevant_path  
**Word Count:** 150  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### deployment\monitoring\otel-collector-config.yaml

**Reason:** domain_relevant_path  
**Word Count:** 120  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### deployment\service-mesh\istio-config.yaml

**Reason:** domain_relevant_path  
**Word Count:** 117  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### frontend\src\config.ts

**Reason:** domain_relevant_path  
**Word Count:** 154  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### frontend\tsconfig.json

**Reason:** domain_relevant_path  
**Word Count:** 55  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### frontend\tsconfig.node.json

**Reason:** domain_relevant_path  
**Word Count:** 17  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### health_check.py

**Reason:** domain_relevant_path  
**Word Count:** 836  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### istio\istio-1.20.3\manifests\charts\istio-cni\templates\configmap-cni.yaml

**Reason:** domain_relevant_path  
**Word Count:** 127  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### istio\istio-1.20.3\manifests\charts\istio-control\istio-discovery\templates\configmap-jwks.yaml

**Reason:** domain_relevant_path  
**Word Count:** 56  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### istio\istio-1.20.3\manifests\charts\istio-control\istio-discovery\templates\configmap.yaml

**Reason:** domain_relevant_path  
**Word Count:** 594  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### istio\istio-1.20.3\manifests\charts\istio-control\istio-discovery\templates\istiod-injector-configmap.yaml

**Reason:** domain_relevant_path  
**Word Count:** 411  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### istio\istio-1.20.3\manifests\charts\istiod-remote\templates\configmap.yaml

**Reason:** domain_relevant_path  
**Word Count:** 594  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### istio\istio-1.20.3\manifests\charts\istiod-remote\templates\istiod-injector-configmap.yaml

**Reason:** domain_relevant_path  
**Word Count:** 411  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### istio\istio-1.20.3\samples\bookinfo\policy\productpage_envoy_ratelimit.yaml

**Reason:** domain_relevant_path  
**Word Count:** 179  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### istio\istio-1.20.3\samples\extauthz\README.md

**Reason:** domain_relevant_path  
**Word Count:** 570  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### istio\istio-1.20.3\samples\health-check\liveness-http-same-port.yaml

**Reason:** domain_relevant_path  
**Word Count:** 65  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### istio\istio-1.20.3\samples\security\spire\istio-spire-config.yaml

**Reason:** domain_relevant_path  
**Word Count:** 149  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### manifests\charts\istio-cni\templates\configmap-cni.yaml

**Reason:** domain_relevant_path  
**Word Count:** 127  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### manifests\charts\istio-control\istio-discovery\templates\configmap-jwks.yaml

**Reason:** domain_relevant_path  
**Word Count:** 56  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### manifests\charts\istio-control\istio-discovery\templates\configmap.yaml

**Reason:** domain_relevant_path  
**Word Count:** 594  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### manifests\charts\istio-control\istio-discovery\templates\istiod-injector-configmap.yaml

**Reason:** domain_relevant_path  
**Word Count:** 411  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### manifests\charts\istiod-remote\templates\configmap.yaml

**Reason:** domain_relevant_path  
**Word Count:** 594  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### manifests\charts\istiod-remote\templates\istiod-injector-configmap.yaml

**Reason:** domain_relevant_path  
**Word Count:** 411  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### pa-check\alembic.ini

**Reason:** domain_relevant_path  
**Word Count:** 365  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### pa-check\app\api\__init__.py

**Reason:** domain_relevant_path  
**Word Count:** 4  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### pa-check\app\api\v1\__init__.py

**Reason:** domain_relevant_path  
**Word Count:** 25  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### pa-check\app\cbcs_repository.py

**Reason:** domain_relevant_path  
**Word Count:** 577  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### pa-check\app\database.py

**Reason:** domain_relevant_path  
**Word Count:** 350  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### pa-check\app\project_repository.py

**Reason:** domain_relevant_path  
**Word Count:** 821  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### pa-check\config\logging_config.py

**Reason:** domain_relevant_path  
**Word Count:** 82  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### pa-check\docker-compose.yml

**Reason:** domain_relevant_path  
**Word Count:** 50  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### pa-check\pyproject.toml

**Reason:** domain_relevant_path  
**Word Count:** 27  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### pa-check\setup.py

**Reason:** domain_relevant_path  
**Word Count:** 312  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### pa-check\static\README.md

**Reason:** domain_relevant_path  
**Word Count:** 58  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### refactored_policy_matcher_analysis\refactored_policy_matcher\README.md

**Reason:** domain_relevant_path  
**Word Count:** 197  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### refactored_policy_matcher_analysis\refactored_policy_matcher\USER_GUIDE.md

**Reason:** domain_relevant_path  
**Word Count:** 677  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### refactored_policy_matcher_analysis\refactored_policy_matcher\__init__.py

**Reason:** domain_relevant_path  
**Word Count:** 40  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### refactored_policy_matcher_analysis\refactored_policy_matcher\tests\__init__.py

**Reason:** domain_relevant_path  
**Word Count:** 29  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### samples\bookinfo\policy\productpage_envoy_ratelimit.yaml

**Reason:** domain_relevant_path  
**Word Count:** 179  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### samples\extauthz\README.md

**Reason:** domain_relevant_path  
**Word Count:** 570  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### samples\health-check\liveness-http-same-port.yaml

**Reason:** domain_relevant_path  
**Word Count:** 65  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### samples\security\spire\istio-spire-config.yaml

**Reason:** domain_relevant_path  
**Word Count:** 149  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### scripts\health_check.sh

**Reason:** domain_relevant_path  
**Word Count:** 299  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\archive\frontend\port_check.js

**Reason:** domain_relevant_path  
**Word Count:** 79  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\archive\frontend\src\components\auth\Register.tsx

**Reason:** domain_relevant_path  
**Word Count:** 333  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\archive\frontend\src\components\quick-check\QuickCheck.tsx

**Reason:** domain_relevant_path  
**Word Count:** 379  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\archive\frontend\vite.config.ts

**Reason:** domain_relevant_path  
**Word Count:** 46  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\archive\temp_project\compliancemax_clean_new\frontend\port_check.js

**Reason:** domain_relevant_path  
**Word Count:** 79  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\archive\temp_project\compliancemax_clean_new\frontend\src\components\auth\Register.tsx

**Reason:** domain_relevant_path  
**Word Count:** 333  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\archive\temp_project\compliancemax_clean_new\frontend\src\components\quick-check\QuickCheck.tsx

**Reason:** domain_relevant_path  
**Word Count:** 379  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\archive\temp_project\compliancemax_clean_new\frontend\vite.config.ts

**Reason:** domain_relevant_path  
**Word Count:** 46  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\backend\tsconfig.json

**Reason:** domain_relevant_path  
**Word Count:** 37  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\core\config.py

**Reason:** domain_relevant_path  
**Word Count:** 202  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\models\activity_log.py

**Reason:** domain_relevant_path  
**Word Count:** 111  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\models\compliance.py

**Reason:** domain_relevant_path  
**Word Count:** 151  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\models\document_share.py

**Reason:** domain_relevant_path  
**Word Count:** 119  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\models\document_version.py

**Reason:** domain_relevant_path  
**Word Count:** 103  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\routers\auth.py

**Reason:** domain_relevant_path  
**Word Count:** 169  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\routers\authorizations.py

**Reason:** domain_relevant_path  
**Word Count:** 329  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### src\services\authorization.py

**Reason:** domain_relevant_path  
**Word Count:** 329  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### tasks\compliance\05_policy_management.md

**Reason:** domain_relevant_path  
**Word Count:** 204  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### tasks\deployment\02_environment_configuration.md

**Reason:** domain_relevant_path  
**Word Count:** 182  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### tasks\monitoring\06_health_checks.md

**Reason:** domain_relevant_path  
**Word Count:** 192  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### tasks\security\01_authentication.md

**Reason:** domain_relevant_path  
**Word Count:** 173  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\core\logging_config.py

**Reason:** domain_relevant_path  
**Word Count:** 463  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\base.py

**Reason:** domain_relevant_path  
**Word Count:** 49  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\bca.py

**Reason:** domain_relevant_path  
**Word Count:** 320  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\cbcs.py

**Reason:** domain_relevant_path  
**Word Count:** 160  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\cbcs_scan.py

**Reason:** domain_relevant_path  
**Word Count:** 77  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\compliance.py

**Reason:** domain_relevant_path  
**Word Count:** 391  
**Policy Keywords:** 3  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\cost.py

**Reason:** domain_relevant_path  
**Word Count:** 159  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\document.py

**Reason:** domain_relevant_path  
**Word Count:** 189  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\document_version.py

**Reason:** domain_relevant_path  
**Word Count:** 227  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\ehp.py

**Reason:** domain_relevant_path  
**Word Count:** 185  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\flood_map.py

**Reason:** domain_relevant_path  
**Word Count:** 136  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\geo_location.py

**Reason:** domain_relevant_path  
**Word Count:** 95  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\history.py

**Reason:** domain_relevant_path  
**Word Count:** 107  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\mapping.py

**Reason:** domain_relevant_path  
**Word Count:** 128  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\policy.py

**Reason:** domain_relevant_path  
**Word Count:** 201  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\project.py

**Reason:** domain_relevant_path  
**Word Count:** 78  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\report.py

**Reason:** domain_relevant_path  
**Word Count:** 126  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\subscription.py

**Reason:** domain_relevant_path  
**Word Count:** 150  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\models\user.py

**Reason:** domain_relevant_path  
**Word Count:** 121  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\schemas\__init__.py

**Reason:** domain_relevant_path  
**Word Count:** 0  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\services\compliance_checker.py

**Reason:** domain_relevant_path  
**Word Count:** 652  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\services\document_service.py

**Reason:** policy_document  
**Word Count:** 1774  
**Policy Keywords:** 3  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\app\services\policy_service.py

**Reason:** domain_relevant_path  
**Word Count:** 456  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\check_db.py

**Reason:** domain_relevant_path  
**Word Count:** 31  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\check_port_3000.ps1

**Reason:** domain_relevant_path  
**Word Count:** 267  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\check_users.py

**Reason:** domain_relevant_path  
**Word Count:** 40  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\compliancemax\config\__init__.py

**Reason:** domain_relevant_path  
**Word Count:** 0  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\enhanced_policy_matcher_part4.py

**Reason:** domain_relevant_path  
**Word Count:** 170  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\frontend\src\config.ts

**Reason:** domain_relevant_path  
**Word Count:** 154  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\frontend\tsconfig.json

**Reason:** domain_relevant_path  
**Word Count:** 55  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\frontend\tsconfig.node.json

**Reason:** domain_relevant_path  
**Word Count:** 17  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\health_check.py

**Reason:** domain_relevant_path  
**Word Count:** 838  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\refactored_policy_matcher_analysis\refactored_policy_matcher\README.md

**Reason:** domain_relevant_path  
**Word Count:** 197  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\refactored_policy_matcher_analysis\refactored_policy_matcher\USER_GUIDE.md

**Reason:** domain_relevant_path  
**Word Count:** 677  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\refactored_policy_matcher_analysis\refactored_policy_matcher\__init__.py

**Reason:** domain_relevant_path  
**Word Count:** 40  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\refactored_policy_matcher_analysis\refactored_policy_matcher\tests\__init__.py

**Reason:** domain_relevant_path  
**Word Count:** 29  
**Policy Keywords:** 1  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\test_api_auth.py

**Reason:** domain_relevant_path  
**Word Count:** 183  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\test_auth.ps1

**Reason:** domain_relevant_path  
**Word Count:** 58  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### temp_project\compliancemax_clean_new\tests\test_auth.py

**Reason:** domain_relevant_path  
**Word Count:** 97  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### testing\chaos\chaos-mesh-config.yaml

**Reason:** domain_relevant_path  
**Word Count:** 96  
**Policy Keywords:** 2  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### testing\performance\k6-test-config.yaml

**Reason:** domain_relevant_path  
**Word Count:** 105  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### tests\config.py

**Reason:** domain_relevant_path  
**Word Count:** 105  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### tests\test_auth.py

**Reason:** domain_relevant_path  
**Word Count:** 97  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

### tests\test_models\test_document.py

**Reason:** domain_relevant_path  
**Word Count:** 529  
**Policy Keywords:** 0  
**Detectors Tried:** fema_patterns, data_patterns, auth_patterns  
**Hypothesis:** May contain PA-CHECK domain logic in non-standard format or require specialized detectors

