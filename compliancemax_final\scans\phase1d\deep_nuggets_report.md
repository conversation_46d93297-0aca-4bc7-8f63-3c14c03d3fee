# ComplianceMax Final - Phase 1D Deep Nuggets Report

## High-Value Code Nuggets

### DEPLOYMENT_GUIDE.md (Lines 61-73)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, general, code-logic  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```markdown
4. **Policy Matcher Service**: Specialized service for policy matching functionality
5. **Integration Services**: Connectors for external systems (FEMA, document repositories, calendars)

## Frontend Deployment

### Building the Frontend

1. Navigate to the frontend directory:
   ```bash
   cd /path/to/compliancemax/frontend
   ```

```

### DEPLOYMENT_GUIDE.md (Lines 342-354)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, general, code-logic  
**Risk:** high | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```markdown

### FEMA Database Integration

1. Register for FEMA API access at https://www.fema.gov/api
2. Add your API key to the .env file:
   ```
   FEMA_API_KEY=***REDACTED***
   FEMA_API_URL=https://www.fema.gov/api/open/v1
   ```

### Document Management Integration

```

### DEPLOYMENT_GUIDE.md (Lines 344-356)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, general, code-logic  
**Risk:** high | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```markdown

1. Register for FEMA API access at https://www.fema.gov/api
2. Add your API key to the .env file:
   ```
   FEMA_API_KEY=***REDACTED***
   FEMA_API_URL=https://www.fema.gov/api/open/v1
   ```

### Document Management Integration

Configure the following variables in the .env file for each document repository:

```

### DEPLOYMENT_GUIDE.md (Lines 348-360)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, general, code-logic  
**Risk:** high | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```markdown
   FEMA_API_KEY=***REDACTED***
   FEMA_API_URL=https://www.fema.gov/api/open/v1
   ```

### Document Management Integration

Configure the following variables in the .env file for each document repository:

```
# SharePoint
SHAREPOINT_CLIENT_ID=your-client-id
SHAREPOINT_CLIENT_SECRET=***REDACTED***
```

### DEPLOYMENT_GUIDE.md (Lines 43-55)

**Category:** Config/Data Assets  
**Tags:** data-model, general, code-logic  
**Risk:** high | **Action:** verify-policy  
**Reason:** Contains reusable logic or patterns

```markdown
  - Outbound access for external integrations
  - Firewall rules for appropriate ports

### Development Environment

- Node.js 16.x or higher
- Python 3.10 or higher
- PostgreSQL 14.x or higher
- Git
- Docker and Docker Compose (optional but recommended)

## Architecture Overview
```

### DEPLOYMENT_GUIDE.md (Lines 397-409)

**Category:** Authentication/Security  
**Tags:** access-control, auth-security, general  
**Risk:** high | **Action:** review-security  
**Reason:** Handles authentication or security

```markdown
   ```
   JWT_SECRET=***REDACTED***
   JWT_ALGORITHM=HS256
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   REFRESH_TOKEN_EXPIRE_DAYS=7
   ```

2. For production, consider integrating with OAuth providers:
   ```
   OAUTH_GOOGLE_CLIENT_ID=your-client-id
   OAUTH_GOOGLE_CLIENT_SECRET=***REDACTED***
   OAUTH_MICROSOFT_CLIENT_ID=your-client-id
```

### DEPLOYMENT_GUIDE.md (Lines 403-415)

**Category:** Authentication/Security  
**Tags:** access-control, auth-security, general  
**Risk:** high | **Action:** review-security  
**Reason:** Handles authentication or security

```markdown

2. For production, consider integrating with OAuth providers:
   ```
   OAUTH_GOOGLE_CLIENT_ID=your-client-id
   OAUTH_GOOGLE_CLIENT_SECRET=***REDACTED***
   OAUTH_MICROSOFT_CLIENT_ID=your-client-id
   OAUTH_MICROSOFT_CLIENT_SECRET=***REDACTED***
   ```

### Data Protection

1. Ensure sensitive data is encrypted at rest:
```

### Documents.tsx (Lines 31-43)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, typescript, general  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```typescript
    { id: 3, name: 'Employee Handbook', type: 'Policy', uploadDate: '2025-01-10', status: 'Active', size: '2.8 MB' },
    { id: 4, name: 'FEMA Compliance Checklist', type: 'Form', uploadDate: '2025-03-22', status: 'Active', size: '0.5 MB' },
    { id: 5, name: 'Quarterly Compliance Report', type: 'Report', uploadDate: '2025-04-01', status: 'Draft', size: '1.7 MB' },
  ]);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('All');
  const [openUploadDialog, setOpenUploadDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState('Policy');
  
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
```

### Documents.tsx (Lines 17-29)

**Category:** Wizard/Workflow Logic  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface Document {
  id: number;
  name: string;
  type: string;
  uploadDate: string;
  status: string;
  size: string;
}

const Documents: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([
```

### Documents.tsx (Lines 31-43)

**Category:** FEMA/Compliance  
**Tags:** typescript, data-model, general  
**Risk:** low | **Action:** port  
**Reason:** Validation or checking logic

```typescript
    { id: 3, name: 'Employee Handbook', type: 'Policy', uploadDate: '2025-01-10', status: 'Active', size: '2.8 MB' },
    { id: 4, name: 'FEMA Compliance Checklist', type: 'Form', uploadDate: '2025-03-22', status: 'Active', size: '0.5 MB' },
    { id: 5, name: 'Quarterly Compliance Report', type: 'Report', uploadDate: '2025-04-01', status: 'Draft', size: '1.7 MB' },
  ]);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('All');
  const [openUploadDialog, setOpenUploadDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState('Policy');
  
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
```

### EnhancedDashboard.tsx (Lines 12-24)

**Category:** Data Models  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
```

### EnhancedPolicyMatcher.tsx (Lines 19-31)

**Category:** Data Models  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
```

### EnhancedPolicyMatcher.tsx (Lines 45-57)

**Category:** FEMA/Compliance  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface MatchResult {
  id: number;
  requirement: string;
  policy: string;
  section: string;
  confidence: number;
}

interface Job {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
```

### EnhancedPolicyMatcher.tsx (Lines 53-65)

**Category:** FEMA/Compliance  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface Job {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  file_name: string;
  results?: MatchResult[];
}

const PolicyMatcher: React.FC = () => {
  // State for file upload
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
```

### ExportFunctionality.tsx (Lines 41-53)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, typescript, data-structure  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```typescript
      { id: 3, name: 'Detailed Analysis', description: 'Comprehensive report with all details' },
      { id: 4, name: 'FEMA Compliance', description: 'Formatted specifically for FEMA requirements' },
      { id: 5, name: 'Custom Template 1', description: 'User-defined custom template' }
    ]);
  }
};

interface Report {
  id: number;
  name: string;
  type: string;
  date: string;
```

### ExportFunctionality.tsx (Lines 60-72)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, typescript, general  
**Risk:** high | **Action:** review-security  
**Reason:** Contains FEMA compliance or policy logic

```typescript
    { id: 2, name: 'Policy Coverage Analysis', type: 'Policy', date: '2025-03-15', status: 'Complete' },
    { id: 3, name: 'FEMA Requirements Gap Analysis', type: 'Gap Analysis', date: '2025-03-22', status: 'Complete' },
    { id: 4, name: 'Document Completeness Report', type: 'Document', date: '2025-04-05', status: 'Complete' },
    { id: 5, name: 'Annual Security Assessment', type: 'Security', date: '2025-02-28', status: 'Complete' },
  ]);
  
  // State for export options
  const [selectedReports, setSelectedReports] = useState<number[]>([]);
  const [exportFormat, setExportFormat] = useState('pdf');
  const [exportLoading, setExportLoading] = useState(false);
  
  // State for template dialog
```

### ExportFunctionality.tsx (Lines 47-59)

**Category:** Wizard/Workflow Logic  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface Report {
  id: number;
  name: string;
  type: string;
  date: string;
  status: string;
}

const ExportFunctionality: React.FC = () => {
  // State for reports
  const [reports, setReports] = useState<Report[]>([
```

### ExternalSystemsIntegration.tsx (Lines 51-63)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, fema-pa, typescript  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```typescript

// Mock FEMA data
const femaDisasterData = [
  { id: 'DR-4611', name: 'Hurricane Alpha', state: 'Florida', declarationDate: '2025-03-15', type: 'Hurricane', status: 'Active' },
  { id: 'DR-4612', name: 'Severe Storms and Flooding', state: 'Texas', declarationDate: '2025-02-28', type: 'Flood', status: 'Active' },
  { id: 'DR-4613', name: 'Wildfire', state: 'California', declarationDate: '2025-01-20', type: 'Fire', status: 'Closed' },
  { id: 'DR-4614', name: 'Tornado', state: 'Oklahoma', declarationDate: '2025-04-05', type: 'Tornado', status: 'Active' },
  { id: 'DR-4615', name: 'Winter Storm', state: 'Michigan', declarationDate: '2025-02-10', type: 'Winter Storm', status: 'Closed' },
];

const femaAssistancePrograms = [
  { id: 'PA', name: 'Public Assistance', description: 'Provides assistance to state, local, tribal, and territorial governments for emergency work and repair/replacement of disaster-damaged facilities.' },
```

### ExternalSystemsIntegration.tsx (Lines 61-73)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, fema-pa, typescript  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```typescript
const femaAssistancePrograms = [
  { id: 'PA', name: 'Public Assistance', description: 'Provides assistance to state, local, tribal, and territorial governments for emergency work and repair/replacement of disaster-damaged facilities.' },
  { id: 'IA', name: 'Individual Assistance', description: 'Provides direct assistance to individuals and households for housing and other needs.' },
  { id: 'HMGP', name: 'Hazard Mitigation Grant Program', description: 'Provides funding for hazard mitigation measures that reduce the risk of loss of life and property from future disasters.' },
  { id: 'FMA', name: 'Flood Mitigation Assistance', description: 'Provides funding to reduce or eliminate the risk of repetitive flood damage to buildings insured under the NFIP.' },
  { id: 'BRIC', name: 'Building Resilient Infrastructure and Communities', description: 'Supports states, local communities, tribes and territories as they undertake hazard mitigation projects.' },
];

// Mock document management system data
const documentRepositories = [
  { id: 1, name: 'SharePoint', connected: true, lastSync: '2025-04-12 09:30 AM', documentCount: 156 },
  { id: 2, name: 'Google Drive', connected: true, lastSync: '2025-04-13 10:15 AM', documentCount: 89 },
```

### ExternalSystemsIntegration.tsx (Lines 62-74)

**Category:** Wizard/Workflow Logic  
**Tags:** fema-compliance, fema-pa, typescript  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```typescript
  { id: 'PA', name: 'Public Assistance', description: 'Provides assistance to state, local, tribal, and territorial governments for emergency work and repair/replacement of disaster-damaged facilities.' },
  { id: 'IA', name: 'Individual Assistance', description: 'Provides direct assistance to individuals and households for housing and other needs.' },
  { id: 'HMGP', name: 'Hazard Mitigation Grant Program', description: 'Provides funding for hazard mitigation measures that reduce the risk of loss of life and property from future disasters.' },
  { id: 'FMA', name: 'Flood Mitigation Assistance', description: 'Provides funding to reduce or eliminate the risk of repetitive flood damage to buildings insured under the NFIP.' },
  { id: 'BRIC', name: 'Building Resilient Infrastructure and Communities', description: 'Supports states, local communities, tribes and territories as they undertake hazard mitigation projects.' },
];

// Mock document management system data
const documentRepositories = [
  { id: 1, name: 'SharePoint', connected: true, lastSync: '2025-04-12 09:30 AM', documentCount: 156 },
  { id: 2, name: 'Google Drive', connected: true, lastSync: '2025-04-13 10:15 AM', documentCount: 89 },
  { id: 3, name: 'Dropbox', connected: false, lastSync: 'Never', documentCount: 0 },
```

### ExternalSystemsIntegration.tsx (Lines 63-75)

**Category:** Wizard/Workflow Logic  
**Tags:** fema-compliance, typescript, general  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```typescript
  { id: 'IA', name: 'Individual Assistance', description: 'Provides direct assistance to individuals and households for housing and other needs.' },
  { id: 'HMGP', name: 'Hazard Mitigation Grant Program', description: 'Provides funding for hazard mitigation measures that reduce the risk of loss of life and property from future disasters.' },
  { id: 'FMA', name: 'Flood Mitigation Assistance', description: 'Provides funding to reduce or eliminate the risk of repetitive flood damage to buildings insured under the NFIP.' },
  { id: 'BRIC', name: 'Building Resilient Infrastructure and Communities', description: 'Supports states, local communities, tribes and territories as they undertake hazard mitigation projects.' },
];

// Mock document management system data
const documentRepositories = [
  { id: 1, name: 'SharePoint', connected: true, lastSync: '2025-04-12 09:30 AM', documentCount: 156 },
  { id: 2, name: 'Google Drive', connected: true, lastSync: '2025-04-13 10:15 AM', documentCount: 89 },
  { id: 3, name: 'Dropbox', connected: false, lastSync: 'Never', documentCount: 0 },
  { id: 4, name: 'OneDrive', connected: true, lastSync: '2025-04-10 02:45 PM', documentCount: 42 },
```

### ExternalSystemsIntegration.tsx (Lines 63-75)

**Category:** Wizard/Workflow Logic  
**Tags:** fema-compliance, typescript, general  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```typescript
  { id: 'IA', name: 'Individual Assistance', description: 'Provides direct assistance to individuals and households for housing and other needs.' },
  { id: 'HMGP', name: 'Hazard Mitigation Grant Program', description: 'Provides funding for hazard mitigation measures that reduce the risk of loss of life and property from future disasters.' },
  { id: 'FMA', name: 'Flood Mitigation Assistance', description: 'Provides funding to reduce or eliminate the risk of repetitive flood damage to buildings insured under the NFIP.' },
  { id: 'BRIC', name: 'Building Resilient Infrastructure and Communities', description: 'Supports states, local communities, tribes and territories as they undertake hazard mitigation projects.' },
];

// Mock document management system data
const documentRepositories = [
  { id: 1, name: 'SharePoint', connected: true, lastSync: '2025-04-12 09:30 AM', documentCount: 156 },
  { id: 2, name: 'Google Drive', connected: true, lastSync: '2025-04-13 10:15 AM', documentCount: 89 },
  { id: 3, name: 'Dropbox', connected: false, lastSync: 'Never', documentCount: 0 },
  { id: 4, name: 'OneDrive', connected: true, lastSync: '2025-04-10 02:45 PM', documentCount: 42 },
```

### ExternalSystemsIntegration.tsx (Lines 65-77)

**Category:** Wizard/Workflow Logic  
**Tags:** fema-compliance, typescript, general  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```typescript
  { id: 'FMA', name: 'Flood Mitigation Assistance', description: 'Provides funding to reduce or eliminate the risk of repetitive flood damage to buildings insured under the NFIP.' },
  { id: 'BRIC', name: 'Building Resilient Infrastructure and Communities', description: 'Supports states, local communities, tribes and territories as they undertake hazard mitigation projects.' },
];

// Mock document management system data
const documentRepositories = [
  { id: 1, name: 'SharePoint', connected: true, lastSync: '2025-04-12 09:30 AM', documentCount: 156 },
  { id: 2, name: 'Google Drive', connected: true, lastSync: '2025-04-13 10:15 AM', documentCount: 89 },
  { id: 3, name: 'Dropbox', connected: false, lastSync: 'Never', documentCount: 0 },
  { id: 4, name: 'OneDrive', connected: true, lastSync: '2025-04-10 02:45 PM', documentCount: 42 },
  { id: 5, name: 'Box', connected: false, lastSync: 'Never', documentCount: 0 },
];
```

### ExternalSystemsIntegration.tsx (Lines 79-91)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, fema-pa, typescript  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```typescript
  { id: 1, name: 'Disaster Recovery Plan.docx', repository: 'SharePoint', lastModified: '2025-04-12', size: '2.4 MB' },
  { id: 2, name: 'FEMA Compliance Checklist.xlsx', repository: 'Google Drive', lastModified: '2025-04-11', size: '1.8 MB' },
  { id: 3, name: 'Emergency Response Procedures.pdf', repository: 'SharePoint', lastModified: '2025-04-10', size: '3.5 MB' },
  { id: 4, name: 'Hazard Mitigation Plan.docx', repository: 'OneDrive', lastModified: '2025-04-09', size: '5.2 MB' },
  { id: 5, name: 'Public Assistance Application.pdf', repository: 'Google Drive', lastModified: '2025-04-08', size: '1.2 MB' },
];

// Mock calendar events
const calendarEvents = [
  { id: 1, title: 'FEMA Application Deadline', start: '2025-04-20', end: '2025-04-20', type: 'deadline', calendar: 'Compliance' },
  { id: 2, title: 'Quarterly Compliance Review', start: '2025-04-25', end: '2025-04-26', type: 'meeting', calendar: 'Compliance' },
  { id: 3, title: 'Document Submission', start: '2025-05-05', end: '2025-05-05', type: 'deadline', calendar: 'FEMA' },
```

### ExternalSystemsIntegration.tsx (Lines 81-93)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, fema-pa, typescript  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```typescript
  { id: 3, name: 'Emergency Response Procedures.pdf', repository: 'SharePoint', lastModified: '2025-04-10', size: '3.5 MB' },
  { id: 4, name: 'Hazard Mitigation Plan.docx', repository: 'OneDrive', lastModified: '2025-04-09', size: '5.2 MB' },
  { id: 5, name: 'Public Assistance Application.pdf', repository: 'Google Drive', lastModified: '2025-04-08', size: '1.2 MB' },
];

// Mock calendar events
const calendarEvents = [
  { id: 1, title: 'FEMA Application Deadline', start: '2025-04-20', end: '2025-04-20', type: 'deadline', calendar: 'Compliance' },
  { id: 2, title: 'Quarterly Compliance Review', start: '2025-04-25', end: '2025-04-26', type: 'meeting', calendar: 'Compliance' },
  { id: 3, title: 'Document Submission', start: '2025-05-05', end: '2025-05-05', type: 'deadline', calendar: 'FEMA' },
  { id: 4, title: 'Policy Update Training', start: '2025-05-10', end: '2025-05-10', type: 'training', calendar: 'Team' },
  { id: 5, title: 'Disaster Recovery Exercise', start: '2025-05-15', end: '2025-05-16', type: 'exercise', calendar: 'Emergency' },
```

### ExternalSystemsIntegration.tsx (Lines 82-94)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, fema-pa, typescript  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```typescript
  { id: 4, name: 'Hazard Mitigation Plan.docx', repository: 'OneDrive', lastModified: '2025-04-09', size: '5.2 MB' },
  { id: 5, name: 'Public Assistance Application.pdf', repository: 'Google Drive', lastModified: '2025-04-08', size: '1.2 MB' },
];

// Mock calendar events
const calendarEvents = [
  { id: 1, title: 'FEMA Application Deadline', start: '2025-04-20', end: '2025-04-20', type: 'deadline', calendar: 'Compliance' },
  { id: 2, title: 'Quarterly Compliance Review', start: '2025-04-25', end: '2025-04-26', type: 'meeting', calendar: 'Compliance' },
  { id: 3, title: 'Document Submission', start: '2025-05-05', end: '2025-05-05', type: 'deadline', calendar: 'FEMA' },
  { id: 4, title: 'Policy Update Training', start: '2025-05-10', end: '2025-05-10', type: 'training', calendar: 'Team' },
  { id: 5, title: 'Disaster Recovery Exercise', start: '2025-05-15', end: '2025-05-16', type: 'exercise', calendar: 'Emergency' },
];
```

### ExternalSystemsIntegration.tsx (Lines 87-99)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, typescript, general  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```typescript
const calendarEvents = [
  { id: 1, title: 'FEMA Application Deadline', start: '2025-04-20', end: '2025-04-20', type: 'deadline', calendar: 'Compliance' },
  { id: 2, title: 'Quarterly Compliance Review', start: '2025-04-25', end: '2025-04-26', type: 'meeting', calendar: 'Compliance' },
  { id: 3, title: 'Document Submission', start: '2025-05-05', end: '2025-05-05', type: 'deadline', calendar: 'FEMA' },
  { id: 4, title: 'Policy Update Training', start: '2025-05-10', end: '2025-05-10', type: 'training', calendar: 'Team' },
  { id: 5, title: 'Disaster Recovery Exercise', start: '2025-05-15', end: '2025-05-16', type: 'exercise', calendar: 'Emergency' },
];

const calendarSources = [
  { id: 1, name: 'Google Calendar', connected: true, lastSync: '2025-04-13 11:30 AM' },
  { id: 2, name: 'Microsoft Outlook', connected: true, lastSync: '2025-04-13 10:45 AM' },
  { id: 3, name: 'Apple Calendar', connected: false, lastSync: 'Never' },
```

### NotificationSystem.tsx (Lines 24-36)

**Category:** Data Models  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
```

### NotificationSystem.tsx (Lines 50-62)

**Category:** Data Models  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface Notification {
  id: number;
  type: 'email' | 'sms' | 'app';
  title: string;
  message: string;
  date: string;
  read: boolean;
  priority: 'high' | 'medium' | 'low';
}

interface NotificationRule {
```

### NotificationSystem.tsx (Lines 60-72)

**Category:** Data Models  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface NotificationRule {
  id: number;
  name: string;
  event: string;
  channels: string[];
  enabled: boolean;
}

interface ScheduledReport {
  id: number;
  name: string;
```

### NotificationSystem.tsx (Lines 68-80)

**Category:** Data Models  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface ScheduledReport {
  id: number;
  name: string;
  reportType: string;
  frequency: string;
  nextDelivery: string;
  recipients: string[];
  enabled: boolean;
}

const NotificationSystem: React.FC = () => {
```

### NotificationSystem.tsx (Lines 90-102)

**Category:** FEMA/Compliance  
**Tags:** typescript, data-model, general  
**Risk:** low | **Action:** port  
**Reason:** Contains reusable logic or patterns

```typescript
  
  // Notification rules state
  const [notificationRules, setNotificationRules] = useState<NotificationRule[]>([
    { id: 1, name: 'Policy Match Completion', event: 'policy_match_complete', channels: ['email', 'app'], enabled: true },
    { id: 2, name: 'Compliance Alerts', event: 'compliance_alert', channels: ['email', 'sms', 'app'], enabled: true },
    { id: 3, name: 'Report Generation', event: 'report_generated', channels: ['email'], enabled: true },
    { id: 4, name: 'New Policy Added', event: 'policy_added', channels: ['app'], enabled: false },
    { id: 5, name: 'System Updates', event: 'system_update', channels: ['email', 'app'], enabled: true },
  ]);
  
  // Scheduled reports state
  const [scheduledReports, setScheduledReports] = useState<ScheduledReport[]>([
```

### NotificationSystem.tsx (Lines 148-160)

**Category:** Other  
**Tags:** typescript, data-model, general  
**Risk:** low | **Action:** port  
**Reason:** Contains reusable logic or patterns

```typescript
    setNotificationRules(prev => 
      prev.map(rule => 
        rule.id === id ? { ...rule, enabled: !rule.enabled } : rule
      )
    );
  };
  
  const handleToggleReport = (id: number) => {
    setScheduledReports(prev => 
      prev.map(report => 
        report.id === id ? { ...report, enabled: !report.enabled } : report
      )
```

### NotificationSystem.tsx (Lines 149-161)

**Category:** Other  
**Tags:** typescript, data-model, general  
**Risk:** low | **Action:** port  
**Reason:** Contains reusable logic or patterns

```typescript
      prev.map(rule => 
        rule.id === id ? { ...rule, enabled: !rule.enabled } : rule
      )
    );
  };
  
  const handleToggleReport = (id: number) => {
    setScheduledReports(prev => 
      prev.map(report => 
        report.id === id ? { ...report, enabled: !report.enabled } : report
      )
    );
```

### NotificationSystem.tsx (Lines 162-174)

**Category:** Other  
**Tags:** typescript, data-model, general  
**Risk:** low | **Action:** port  
**Reason:** Contains reusable logic or patterns

```typescript
  
  const handleOpenRuleDialog = (rule?: NotificationRule) => {
    if (rule) {
      setEditingRule(rule);
      setRuleName(rule.name);
      setRuleEvent(rule.event);
      setRuleChannels(rule.channels);
      setRuleEnabled(rule.enabled);
    } else {
      setEditingRule(null);
      setRuleName('');
      setRuleEvent('');
```

### NotificationSystem.tsx (Lines 163-175)

**Category:** Other  
**Tags:** typescript, data-model, general  
**Risk:** low | **Action:** port  
**Reason:** Contains reusable logic or patterns

```typescript
  const handleOpenRuleDialog = (rule?: NotificationRule) => {
    if (rule) {
      setEditingRule(rule);
      setRuleName(rule.name);
      setRuleEvent(rule.event);
      setRuleChannels(rule.channels);
      setRuleEnabled(rule.enabled);
    } else {
      setEditingRule(null);
      setRuleName('');
      setRuleEvent('');
      setRuleChannels([]);
```

### NotificationSystem.tsx (Lines 164-176)

**Category:** Other  
**Tags:** typescript, data-model, general  
**Risk:** low | **Action:** port  
**Reason:** Contains reusable logic or patterns

```typescript
    if (rule) {
      setEditingRule(rule);
      setRuleName(rule.name);
      setRuleEvent(rule.event);
      setRuleChannels(rule.channels);
      setRuleEnabled(rule.enabled);
    } else {
      setEditingRule(null);
      setRuleName('');
      setRuleEvent('');
      setRuleChannels([]);
      setRuleEnabled(true);
```

### Profile.tsx (Lines 21-33)

**Category:** Data Models  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
```

### README.md (Lines 15-27)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, general, code-logic  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```markdown
- **Enhanced Dashboard**: Detailed trend analysis and compliance monitoring
- **External Systems Integration**: Connect with FEMA database, document repositories, and calendars

## Repository Structure

```
compliancemax/
├── frontend/                  # React frontend application
│   ├── src/
│   │   ├── components/        # Reusable UI components
│   │   ├── pages/             # Page components
│   │   ├── services/          # API service integrations
```

### Reports.tsx (Lines 57-69)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, typescript, general  
**Risk:** high | **Action:** review-security  
**Reason:** Contains FEMA compliance or policy logic

```typescript
    { id: 2, name: 'Policy Coverage Analysis', type: 'Policy', generatedDate: '2025-03-15', status: 'Complete', score: 92 },
    { id: 3, name: 'FEMA Requirements Gap Analysis', type: 'Gap Analysis', generatedDate: '2025-03-22', status: 'Complete', score: 76 },
    { id: 4, name: 'Document Completeness Report', type: 'Document', generatedDate: '2025-04-05', status: 'In Progress', score: 45 },
    { id: 5, name: 'Annual Security Assessment', type: 'Security', generatedDate: '2025-02-28', status: 'Complete', score: 81 },
  ]);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('All');
  const [tabValue, setTabValue] = useState(0);
  const [openGenerateDialog, setOpenGenerateDialog] = useState(false);
  const [reportType, setReportType] = useState('Compliance');
  
```

### Reports.tsx (Lines 329-341)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, typescript, general  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```typescript
              <Typography variant="body2" color="text.secondary">
                • FEMA Requirements Gap Analysis
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • Document Completeness Report
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </TabPanel>
      
      {/* Generate Report Dialog */}
```

### Reports.tsx (Lines 18-30)

**Category:** Data Models  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface Report {
  id: number;
  name: string;
  type: string;
  generatedDate: string;
  status: string;
  score: number;
}

interface TabPanelProps {
  children?: React.ReactNode;
```

### Reports.tsx (Lines 27-39)

**Category:** Data Models  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
```

### USER_GUIDE.md (Lines 227-239)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, general, code-logic  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```markdown

### FEMA Database Integration

1. Navigate to "External Systems Integration" from the main menu
2. Select the "FEMA Database" tab
3. Use the search box to find specific disasters
4. Click on a disaster to view details
5. Click "Import Selected Disaster" to bring data into ComplianceMax

### Document Management Integration

1. Select the "Document Management" tab
```

### USER_GUIDE.md (Lines 230-242)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, general, code-logic  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```markdown
1. Navigate to "External Systems Integration" from the main menu
2. Select the "FEMA Database" tab
3. Use the search box to find specific disasters
4. Click on a disaster to view details
5. Click "Import Selected Disaster" to bring data into ComplianceMax

### Document Management Integration

1. Select the "Document Management" tab
2. View connected repositories
3. Click "Add Repository" to connect a new document source
4. Configure connection details:
```

### USER_GUIDE.md (Lines 202-214)

**Category:** Config/Data Assets  
**Tags:** data-model, general, code-logic  
**Risk:** medium | **Action:** port  
**Reason:** Configuration or mapping logic

```markdown

### Configuring Notification Rules

1. Select the "Notification Rules" tab
2. Click "Add Rule" to create a new notification rule
3. Enter a rule name
4. Select the event that triggers the notification
5. Choose notification channels (Email, SMS, In-App)
6. Toggle the rule on/off as needed
7. Click "Create" to save the rule

### Scheduled Reports
```

### USER_GUIDE.md (Lines 204-216)

**Category:** Other  
**Tags:** data-model, general, code-logic  
**Risk:** low | **Action:** port  
**Reason:** Contains reusable logic or patterns

```markdown

1. Select the "Notification Rules" tab
2. Click "Add Rule" to create a new notification rule
3. Enter a rule name
4. Select the event that triggers the notification
5. Choose notification channels (Email, SMS, In-App)
6. Toggle the rule on/off as needed
7. Click "Create" to save the rule

### Scheduled Reports

1. Select the "Scheduled Reports" tab
```

### USER_GUIDE.md (Lines 205-217)

**Category:** Other  
**Tags:** data-model, general, code-logic  
**Risk:** low | **Action:** port  
**Reason:** Contains reusable logic or patterns

```markdown
1. Select the "Notification Rules" tab
2. Click "Add Rule" to create a new notification rule
3. Enter a rule name
4. Select the event that triggers the notification
5. Choose notification channels (Email, SMS, In-App)
6. Toggle the rule on/off as needed
7. Click "Create" to save the rule

### Scheduled Reports

1. Select the "Scheduled Reports" tab
2. Click "Schedule New Report" to set up a recurring report
```

### USER_GUIDE.md (Lines 206-218)

**Category:** Config/Data Assets  
**Tags:** data-model, general, code-logic  
**Risk:** medium | **Action:** port  
**Reason:** Configuration or mapping logic

```markdown
2. Click "Add Rule" to create a new notification rule
3. Enter a rule name
4. Select the event that triggers the notification
5. Choose notification channels (Email, SMS, In-App)
6. Toggle the rule on/off as needed
7. Click "Create" to save the rule

### Scheduled Reports

1. Select the "Scheduled Reports" tab
2. Click "Schedule New Report" to set up a recurring report
3. Configure report details:
```

### USER_GUIDE.md (Lines 209-221)

**Category:** Data Models  
**Tags:** data-model, general, code-logic  
**Risk:** medium | **Action:** port  
**Reason:** Configuration or mapping logic

```markdown
5. Choose notification channels (Email, SMS, In-App)
6. Toggle the rule on/off as needed
7. Click "Create" to save the rule

### Scheduled Reports

1. Select the "Scheduled Reports" tab
2. Click "Schedule New Report" to set up a recurring report
3. Configure report details:
   - Report name and type
   - Frequency (Daily, Weekly, Monthly, Quarterly)
   - Recipients (email addresses)
```

### USER_GUIDE.md (Lines 210-222)

**Category:** Data Models  
**Tags:** data-model, general, code-logic  
**Risk:** medium | **Action:** port  
**Reason:** Configuration or mapping logic

```markdown
6. Toggle the rule on/off as needed
7. Click "Create" to save the rule

### Scheduled Reports

1. Select the "Scheduled Reports" tab
2. Click "Schedule New Report" to set up a recurring report
3. Configure report details:
   - Report name and type
   - Frequency (Daily, Weekly, Monthly, Quarterly)
   - Recipients (email addresses)
4. Toggle the schedule on/off as needed
```

### USER_GUIDE.md (Lines 262-274)

**Category:** Authentication/Security  
**Tags:** access-control, auth-security, general  
**Risk:** high | **Action:** verify-policy  
**Reason:** Handles authentication or security

```markdown

The User Management section allows administrators to manage user accounts and permissions.

### Adding Users

1. Navigate to "Settings" > "User Management"
2. Click "Add User"
3. Enter user details:
   - Name
   - Email address
   - Role (Admin, Manager, User)
4. Click "Create User"
```

### USER_GUIDE.md (Lines 275-287)

**Category:** FEMA/Compliance  
**Tags:** access-control, auth-security, general  
**Risk:** high | **Action:** verify-policy  
**Reason:** Handles authentication or security

```markdown

### Managing Roles and Permissions

1. Navigate to "Settings" > "Roles & Permissions"
2. Select a role to modify
3. Adjust permissions for each feature
4. Click "Save Changes" to update

## Support and Troubleshooting

If you encounter any issues while using ComplianceMax:

```

### api_integration.py (Lines 68-80)

**Category:** FEMA/Compliance  
**Tags:** access-control, auth-security, python  
**Risk:** high | **Action:** verify-policy  
**Reason:** Handles authentication or security

```python
    """
    # Verify permissions
    if not verify_permissions(current_user, "policy_matcher:use"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to use policy matcher"
        )
    
    # Create temporary directory for processing
    temp_dir = tempfile.mkdtemp(prefix="policy_matcher_")
    
    # Save uploaded requirements file
```

### api_integration.py (Lines 72-84)

**Category:** FEMA/Compliance  
**Tags:** access-control, auth-security, python  
**Risk:** high | **Action:** verify-policy  
**Reason:** Handles authentication or security

```python
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to use policy matcher"
        )
    
    # Create temporary directory for processing
    temp_dir = tempfile.mkdtemp(prefix="policy_matcher_")
    
    # Save uploaded requirements file
    requirements_path = os.path.join(temp_dir, requirements_file.filename)
    with open(requirements_path, "wb") as f:
        content = await requirements_file.read()
        f.write(content)
```

### api_integration.py (Lines 149-161)

**Category:** FEMA/Compliance  
**Tags:** access-control, auth-security, python  
**Risk:** high | **Action:** verify-policy  
**Reason:** Handles authentication or security

```python
    """
    # Verify permissions
    if not verify_permissions(current_user, "policy_matcher:use"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to use policy matcher"
        )
    
    # Check if job exists
    job = db.query(PolicyMatch).filter(
        PolicyMatch.job_id == job_id,
        PolicyMatch.user_id == current_user.id
```

### api_integration.py (Lines 153-165)

**Category:** FEMA/Compliance  
**Tags:** access-control, auth-security, python  
**Risk:** high | **Action:** verify-policy  
**Reason:** Handles authentication or security

```python
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to use policy matcher"
        )
    
    # Check if job exists
    job = db.query(PolicyMatch).filter(
        PolicyMatch.job_id == job_id,
        PolicyMatch.user_id == current_user.id
    ).first()
    
    if not job:
        raise HTTPException(
```

### api_integration.py (Lines 194-206)

**Category:** FEMA/Compliance  
**Tags:** access-control, auth-security, python  
**Risk:** high | **Action:** verify-policy  
**Reason:** Handles authentication or security

```python
    """
    # Verify permissions
    if not verify_permissions(current_user, "policy_matcher:use"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to use policy matcher"
        )
    
    # Check if job exists and is completed
    job = db.query(PolicyMatch).filter(
        PolicyMatch.job_id == job_id,
        PolicyMatch.user_id == current_user.id,
```

### api_integration.py (Lines 198-210)

**Category:** FEMA/Compliance  
**Tags:** access-control, auth-security, python  
**Risk:** high | **Action:** verify-policy  
**Reason:** Handles authentication or security

```python
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to use policy matcher"
        )
    
    # Check if job exists and is completed
    job = db.query(PolicyMatch).filter(
        PolicyMatch.job_id == job_id,
        PolicyMatch.user_id == current_user.id,
        PolicyMatch.status == "completed"
    ).first()
    
    if not job:
```

### api_integration.py (Lines 235-247)

**Category:** FEMA/Compliance  
**Tags:** access-control, auth-security, python  
**Risk:** high | **Action:** verify-policy  
**Reason:** Handles authentication or security

```python
    """
    # Verify permissions
    if not verify_permissions(current_user, "policy_matcher:use"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to use policy matcher"
        )
    
    # Check if job exists and is completed
    job = db.query(PolicyMatch).filter(
        PolicyMatch.job_id == job_id,
        PolicyMatch.user_id == current_user.id,
```

### api_integration.py (Lines 239-251)

**Category:** FEMA/Compliance  
**Tags:** access-control, auth-security, python  
**Risk:** high | **Action:** verify-policy  
**Reason:** Handles authentication or security

```python
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to use policy matcher"
        )
    
    # Check if job exists and is completed
    job = db.query(PolicyMatch).filter(
        PolicyMatch.job_id == job_id,
        PolicyMatch.user_id == current_user.id,
        PolicyMatch.status == "completed"
    ).first()
    
    if not job:
```

### api_integration.py (Lines 307-319)

**Category:** FEMA/Compliance  
**Tags:** access-control, auth-security, python  
**Risk:** high | **Action:** verify-policy  
**Reason:** Handles authentication or security

```python
    """
    # Verify permissions
    if not verify_permissions(current_user, "policy_matcher:delete"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to delete policy matcher jobs"
        )
    
    # Check if job exists
    job = db.query(PolicyMatch).filter(
        PolicyMatch.job_id == job_id,
        PolicyMatch.user_id == current_user.id
```

### api_integration.py (Lines 311-323)

**Category:** FEMA/Compliance  
**Tags:** access-control, auth-security, python  
**Risk:** high | **Action:** verify-policy  
**Reason:** Handles authentication or security

```python
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to delete policy matcher jobs"
        )
    
    # Check if job exists
    job = db.query(PolicyMatch).filter(
        PolicyMatch.job_id == job_id,
        PolicyMatch.user_id == current_user.id
    ).first()
    
    if not job:
        raise HTTPException(
```

### component_tests.js (Lines 193-205)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, javascript, general  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```javascript
    expect(screen.getByText('External Systems Integration')).toBeInTheDocument();
    expect(screen.getByText('FEMA Database')).toBeInTheDocument();
  });

  test('searches FEMA database', () => {
    render(<ExternalSystemsIntegration />);
    
    // Enter search query
    const searchInput = screen.getByPlaceholderText('Search disasters by ID, name, or state');
    fireEvent.change(searchInput, { target: { value: 'Hurricane' } });
    
    // Click search button
```

### component_tests.js (Lines 196-208)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, javascript, general  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```javascript

  test('searches FEMA database', () => {
    render(<ExternalSystemsIntegration />);
    
    // Enter search query
    const searchInput = screen.getByPlaceholderText('Search disasters by ID, name, or state');
    fireEvent.change(searchInput, { target: { value: 'Hurricane' } });
    
    // Click search button
    const searchButton = screen.getByText('Search').closest('button');
    fireEvent.click(searchButton);
    
```

### database_integration.py (Lines 56-68)

**Category:** FEMA/Compliance  
**Tags:** data-model, python, general  
**Risk:** low | **Action:** port  
**Reason:** Configuration or mapping logic

```python
        Returns:
            Dictionary mapping policy names to policy text
        """
        query = self.db.query(Document).filter(Document.document_type == "policy")
        
        # Apply filters
        if policy_ids:
            query = query.filter(Document.id.in_(policy_ids))
        
        if project_id:
            query = query.filter(Document.project_id == project_id)
        
```

### database_integration.py (Lines 98-110)

**Category:** Data Models  
**Tags:** eligibility-rules, data-model, python  
**Risk:** low | **Action:** port  
**Reason:** Contains eligibility or business rules

```python
        Args:
            requirements: Dictionary mapping requirement names to requirement text
            requirement_categories: Dictionary mapping category types to lists of requirement names
            job_id: Job ID for the matching task
            user_id: ID of the user who initiated the matching
            project_id: Optional project ID to associate with the requirements
            
        Returns:
            Dictionary mapping requirement names to requirement IDs
        """
        requirement_ids = {}
        
```

### database_integration.py (Lines 99-111)

**Category:** Data Models  
**Tags:** eligibility-rules, data-model, python  
**Risk:** low | **Action:** port  
**Reason:** Contains eligibility or business rules

```python
            requirements: Dictionary mapping requirement names to requirement text
            requirement_categories: Dictionary mapping category types to lists of requirement names
            job_id: Job ID for the matching task
            user_id: ID of the user who initiated the matching
            project_id: Optional project ID to associate with the requirements
            
        Returns:
            Dictionary mapping requirement names to requirement IDs
        """
        requirement_ids = {}
        
        for req_name, req_text in requirements.items():
```

### database_integration.py (Lines 105-117)

**Category:** Data Models  
**Tags:** eligibility-rules, data-model, python  
**Risk:** low | **Action:** port  
**Reason:** Contains eligibility or business rules

```python
        Returns:
            Dictionary mapping requirement names to requirement IDs
        """
        requirement_ids = {}
        
        for req_name, req_text in requirements.items():
            # Determine category
            category = "Other"
            for cat_type, cat_reqs in requirement_categories.items():
                if req_name in cat_reqs:
                    category = cat_type
                    break
```

### database_integration.py (Lines 149-161)

**Category:** FEMA/Compliance  
**Tags:** data-model, python, general  
**Risk:** low | **Action:** port  
**Reason:** Configuration or mapping logic

```python
        Args:
            matches: Dictionary mapping requirement names to lists of (policy_name, score) tuples
            requirement_ids: Dictionary mapping requirement names to requirement IDs
            job_id: Job ID for the matching task
            user_id: ID of the user who initiated the matching
            project_id: Optional project ID to associate with the matches
        """
        match_count = 0
        
        for req_name, policy_matches in matches.items():
            if req_name not in requirement_ids:
                logger.warning(f"Requirement '{req_name}' not found in requirement_ids")
```

### database_integration.py (Lines 150-162)

**Category:** FEMA/Compliance  
**Tags:** data-model, python, general  
**Risk:** low | **Action:** port  
**Reason:** Configuration or mapping logic

```python
            matches: Dictionary mapping requirement names to lists of (policy_name, score) tuples
            requirement_ids: Dictionary mapping requirement names to requirement IDs
            job_id: Job ID for the matching task
            user_id: ID of the user who initiated the matching
            project_id: Optional project ID to associate with the matches
        """
        match_count = 0
        
        for req_name, policy_matches in matches.items():
            if req_name not in requirement_ids:
                logger.warning(f"Requirement '{req_name}' not found in requirement_ids")
                continue
```

### end_to_end_tests.py (Lines 287-299)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, python, general  
**Risk:** high | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```python
    
    # Test FEMA Database tab
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//h6[contains(text(), 'FEMA Disaster Database')]"))
    )
    
    # Search for a disaster
    search_input = driver.find_element(By.XPATH, "//input[@placeholder='Search disasters by ID, name, or state']")
    search_input.send_keys("Hurricane")
    driver.find_element(By.XPATH, "//button[contains(text(), 'Search')]").click()
    
    # Verify search results
```

### end_to_end_tests.py (Lines 289-301)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, python, general  
**Risk:** high | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```python
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//h6[contains(text(), 'FEMA Disaster Database')]"))
    )
    
    # Search for a disaster
    search_input = driver.find_element(By.XPATH, "//input[@placeholder='Search disasters by ID, name, or state']")
    search_input.send_keys("Hurricane")
    driver.find_element(By.XPATH, "//button[contains(text(), 'Search')]").click()
    
    # Verify search results
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//td[contains(text(), 'Hurricane')]"))
```

### end_to_end_tests.py (Lines 198-210)

**Category:** Other  
**Tags:** data-model, python, general  
**Risk:** low | **Action:** port  
**Reason:** Contains reusable logic or patterns

```python
    
    # Go to Notification Rules tab
    driver.find_element(By.XPATH, "//span[contains(text(), 'Notification Rules')]").click()
    
    # Add a new rule
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Add Rule')]"))
    )
    driver.find_element(By.XPATH, "//button[contains(text(), 'Add Rule')]").click()
    
    # Fill in rule details
    WebDriverWait(driver, 10).until(
```

### end_to_end_tests.py (Lines 199-211)

**Category:** Other  
**Tags:** data-model, python, general  
**Risk:** low | **Action:** port  
**Reason:** Contains reusable logic or patterns

```python
    # Go to Notification Rules tab
    driver.find_element(By.XPATH, "//span[contains(text(), 'Notification Rules')]").click()
    
    # Add a new rule
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Add Rule')]"))
    )
    driver.find_element(By.XPATH, "//button[contains(text(), 'Add Rule')]").click()
    
    # Fill in rule details
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//input[@label='Rule Name']"))
```

### end_to_end_tests.py (Lines 201-213)

**Category:** Other  
**Tags:** data-model, python, general  
**Risk:** high | **Action:** verify-policy  
**Reason:** Contains reusable logic or patterns

```python
    
    # Add a new rule
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Add Rule')]"))
    )
    driver.find_element(By.XPATH, "//button[contains(text(), 'Add Rule')]").click()
    
    # Fill in rule details
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//input[@label='Rule Name']"))
    )
    driver.find_element(By.XPATH, "//input[@label='Rule Name']").send_keys("Test Rule")
```

### end_to_end_tests.py (Lines 203-215)

**Category:** Data Models  
**Tags:** data-model, python, general  
**Risk:** high | **Action:** verify-policy  
**Reason:** Contains reusable logic or patterns

```python
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Add Rule')]"))
    )
    driver.find_element(By.XPATH, "//button[contains(text(), 'Add Rule')]").click()
    
    # Fill in rule details
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//input[@label='Rule Name']"))
    )
    driver.find_element(By.XPATH, "//input[@label='Rule Name']").send_keys("Test Rule")
    
    # Select event type
```

### end_to_end_tests.py (Lines 205-217)

**Category:** FEMA/Compliance  
**Tags:** data-model, python, general  
**Risk:** high | **Action:** verify-policy  
**Reason:** Contains reusable logic or patterns

```python
    )
    driver.find_element(By.XPATH, "//button[contains(text(), 'Add Rule')]").click()
    
    # Fill in rule details
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//input[@label='Rule Name']"))
    )
    driver.find_element(By.XPATH, "//input[@label='Rule Name']").send_keys("Test Rule")
    
    # Select event type
    driver.find_element(By.ID, "event-select").click()
    driver.find_element(By.XPATH, "//li[contains(text(), 'Policy Match Completion')]").click()
```

### end_to_end_tests.py (Lines 207-219)

**Category:** FEMA/Compliance  
**Tags:** data-model, python, general  
**Risk:** high | **Action:** verify-policy  
**Reason:** Contains reusable logic or patterns

```python
    
    # Fill in rule details
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//input[@label='Rule Name']"))
    )
    driver.find_element(By.XPATH, "//input[@label='Rule Name']").send_keys("Test Rule")
    
    # Select event type
    driver.find_element(By.ID, "event-select").click()
    driver.find_element(By.XPATH, "//li[contains(text(), 'Policy Match Completion')]").click()
    
    # Select channels
```

### end_to_end_tests.py (Lines 209-221)

**Category:** FEMA/Compliance  
**Tags:** data-model, python, general  
**Risk:** high | **Action:** verify-policy  
**Reason:** Contains reusable logic or patterns

```python
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "//input[@label='Rule Name']"))
    )
    driver.find_element(By.XPATH, "//input[@label='Rule Name']").send_keys("Test Rule")
    
    # Select event type
    driver.find_element(By.ID, "event-select").click()
    driver.find_element(By.XPATH, "//li[contains(text(), 'Policy Match Completion')]").click()
    
    # Select channels
    driver.find_element(By.XPATH, "//span[contains(text(), 'Email')]").click()
    
```

### end_to_end_tests.py (Lines 211-223)

**Category:** FEMA/Compliance  
**Tags:** data-model, python, general  
**Risk:** high | **Action:** verify-policy  
**Reason:** Contains reusable logic or patterns

```python
    )
    driver.find_element(By.XPATH, "//input[@label='Rule Name']").send_keys("Test Rule")
    
    # Select event type
    driver.find_element(By.ID, "event-select").click()
    driver.find_element(By.XPATH, "//li[contains(text(), 'Policy Match Completion')]").click()
    
    # Select channels
    driver.find_element(By.XPATH, "//span[contains(text(), 'Email')]").click()
    
    # Save rule
    driver.find_element(By.XPATH, "//button[contains(text(), 'Create')]").click()
```

### frontend_integration.tsx (Lines 29-41)

**Category:** FEMA/Compliance  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript
// Types
interface PolicyMatcherProps {
  projectId?: number;
}

interface MatchingJob {
  job_id: string;
  status: 'processing' | 'completed' | 'failed';
  created_at: string;
  completed_at?: string;
  report_url?: string;
  error_message?: string;
```

### frontend_integration.tsx (Lines 33-45)

**Category:** FEMA/Compliance  
**Tags:** typescript, data-model, data-structure  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```typescript

interface MatchingJob {
  job_id: string;
  status: 'processing' | 'completed' | 'failed';
  created_at: string;
  completed_at?: string;
  report_url?: string;
  error_message?: string;
}

const PolicyMatcher: React.FC<PolicyMatcherProps> = ({ projectId }) => {
  // State
```

### policy_matcher_api.py (Lines 3-15)

**Category:** FEMA/Compliance  
**Tags:** data-model, data-structure, python  
**Risk:** medium | **Action:** port  
**Reason:** Defines data schemas or models

```python
from typing import List, Optional
from pydantic import BaseModel
import uuid
import os
import tempfile
import json
from datetime import datetime
import shutil

# Import the refactored policy matcher
from refactored_policy_matcher.main import run_policy_matcher
from refactored_policy_matcher.config import Config
```

### policy_matcher_api.py (Lines 303-309)

**Category:** FEMA/Compliance  
**Tags:** data-model, python, general  
**Risk:** high | **Action:** review-security  
**Reason:** Contains reusable logic or patterns

```python
        {"id": 3, "name": "Data Protection Policy", "description": "Guidelines for data handling", "last_updated": "2025-03-10"},
        {"id": 4, "name": "Acceptable Use Policy", "description": "Rules for IT resource usage", "last_updated": "2025-01-05"},
        {"id": 5, "name": "Incident Response Policy", "description": "Procedures for security incidents", "last_updated": "2025-02-28"}
    ]
    
    return {"policies": policies}

```

### test_plan.md (Lines 29-41)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, general, code-logic  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```markdown
- Enhanced Dashboard with Trend Analysis
- External Systems Integration (FEMA, Document Management, Calendar)

## 4. Test Cases

### 4.1 Unit Tests

#### Policy Matcher Component
- Test file upload functionality
- Test confidence threshold selection
- Test algorithm selection
- Test results display
```

### test_plan.md (Lines 61-73)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, general, code-logic  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```markdown
#### External Systems Integration
- Test FEMA database connection and search
- Test document repository connections
- Test calendar integration and event creation

### 4.2 Integration Tests

#### Policy Matcher with Backend
- Test API communication for uploading documents
- Test job status polling
- Test results retrieval
- Test report download
```

### test_plan.md (Lines 86-98)

**Category:** FEMA/Compliance  
**Tags:** fema-compliance, general, code-logic  
**Risk:** medium | **Action:** verify-policy  
**Reason:** Contains FEMA compliance or policy logic

```markdown
- Test calendar events with compliance deadlines
- Test FEMA data with reports generation

### 4.3 End-to-End Tests

#### Complete Policy Matching Workflow
- Upload document
- Process matching
- View results
- Generate report
- Export report
- Receive notification
```

### test_plan.md (Lines 56-68)

**Category:** FEMA/Compliance  
**Tags:** data-model, general, code-logic  
**Risk:** medium | **Action:** port  
**Reason:** Configuration or mapping logic

```markdown
- Test notification display
- Test notification rules creation
- Test scheduled reports configuration
- Test notification status updates

#### External Systems Integration
- Test FEMA database connection and search
- Test document repository connections
- Test calendar integration and event creation

### 4.2 Integration Tests

```

### test_plan.md (Lines 155-167)

**Category:** Data Models  
**Tags:** data-model, data-structure, general  
**Risk:** low | **Action:** port  
**Reason:** Defines data schemas or models

```markdown
- Mobile responsiveness testing
- Touch interface testing

## 10. Test Documentation

- Test results documentation
- Issue tracking and resolution
- Test coverage reporting
- Performance metrics reporting

## 11. Continuous Integration

```

